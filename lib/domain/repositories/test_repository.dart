import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/data/models/quiz_response.dart';

abstract class TestRepository {
  Future<List<QuestionResponse>> doPracticeTest(int numQuestion);
  Future<List<QuestionResponse>> doDomainTest(
      int numQuestion, List<TestDomain> domains);
  Future<List<QuizResponse>> getListMockTest();
  Future<List<QuestionResponse>> getQuestionMockTest(int id);
}
