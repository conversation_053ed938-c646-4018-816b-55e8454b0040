import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

var formatter = NumberFormat(',000');

class SubscriptionEntity extends Equatable {
  final String duration;
  final double price;
  final String concurrency;
  final double? oldPrice;
  final String tag;

  SubscriptionEntity({
    required this.duration,
    required this.price,
    required this.concurrency,
    this.oldPrice,
    required this.tag,
  });

  String getDisplayPrice(double price) {
    final result =
        price > 1000 ? formatter.format(price) : price.toStringAsFixed(2);
    return result;
  }

  String get displayPrice => getDisplayPrice(price);
  String get displayOldPrice => getDisplayPrice(oldPrice ?? 0);

  String get displayString => "$displayPrice $concurrency / $duration";

  @override
  List<Object?> get props => [duration, price, concurrency, oldPrice];
}
