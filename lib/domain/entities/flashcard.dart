import 'package:isar/isar.dart';

part 'flashcard.g.dart';

@Collection()
class FlashcardEntity {
  String? fid;
  String? term;
  String? explanation;
  String? explanationVn;
  String? certificate;
  String? labels;
  String? status;
  String? createAt;

  late final Id? id = fid.hashCode;

  FlashcardEntity(
      {this.fid,
      this.term,
      this.explanation,
      this.explanationVn,
      this.certificate,
      this.labels,
      this.status,
      this.createAt});
}
