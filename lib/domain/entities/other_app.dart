import 'package:isar/isar.dart';

part 'other_app.g.dart';

@Collection()
class OtherAppEntity {
  OtherAppEntity({
    required this.appid,
    required this.appIcon,
    required this.driveIcon,
    required this.appName,
    required this.iosID,
    required this.androidID,
  });

  final String appid;
  final String appIcon;
  final String driveIcon;
  final String appName;
  final String iosID;
  final String androidID;

  late final Id? id = appid.hashCode;
}
