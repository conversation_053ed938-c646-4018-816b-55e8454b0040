import 'package:isar/isar.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';

part 'quiz.g.dart';

@Collection()
class QuizEntity {
  QuizEntity({
    required this.quid,
    required this.quizName,
    required this.noq,
    required this.duration,
    required this.passPercentage,
    required this.description,
    this.premium = false,
  });

  final String quid;
  final String quizName;
  final int noq;

  /// in minutes
  final int duration;
  final int passPercentage;
  final String description;
  final bool premium;
  double highscore = 0;

  late final Id? id = Utils.fastHash(quid);
}
