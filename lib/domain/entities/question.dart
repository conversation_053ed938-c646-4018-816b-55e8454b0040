// ignore_for_file: must_be_immutable

import 'package:equatable/equatable.dart';
import 'package:get/get.dart';
import 'package:isar/isar.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';

part 'question.g.dart';

@Collection()
class QuestionEntity {
  final String? qid;
  @Enumerated(EnumType.name)
  final QuestionType? type;
  final String? question;
  final List<OptionEntity>? options;
  final String? description;
  final String? nextQuestion;
  bool isBookmark;
  @Enumerated(EnumType.name)
  QuestionStatus status;
  @ignore
  bool? isCorrect;
  int? timeAnswering;

  late final Id? id = int.tryParse(qid ?? '0') ?? qid.hashCode;

  QuestionEntity({
    this.qid,
    this.type,
    this.question,
    this.options,
    this.description,
    this.nextQuestion,
    this.isBookmark = false,
    this.status = QuestionStatus.notAnswered,
    this.isCorrect,
    this.timeAnswering,
  });

  @ignore
  bool get hasAnySelect {
    final isSelected = options?.where((e) => e.isSelected).isNotEmpty;
    return isSelected ?? false;
  }

  @ignore
  bool get isShowResult => status == QuestionStatus.answered;

  QuestionListStatus maptoListStatus() {
    if (status == QuestionStatus.doing) {
      return QuestionListStatus.doing;
    } else if (isBookmark) {
      return QuestionListStatus.bookmarked;
    } else if (status == QuestionStatus.answered) {
      return QuestionListStatus.done;
    } else {
      return QuestionListStatus.notDone;
    }
  }

  String getQuestionText() {
    if (question?.isEmpty ?? true) return "";
    String questionText = question!;
    if (questionText.startsWith('<p>')) {
      questionText = questionText.replaceFirst('<p>', '');
      questionText = questionText = questionText.replaceFirst('</p>', '');
    } else if (RegExp(r'^<p><br />.*').hasMatch(questionText)) {
      questionText = questionText.substring(9, questionText.length - 4);
    } else if (RegExp(r'<p>.*?</p>').hasMatch(questionText)) {
      questionText = questionText.substring(3, questionText.length - 4);
    } else if (RegExp(r'^<br>.*').hasMatch(questionText)) {
      questionText = questionText.substring(4);
    }
    return questionText
        .replaceAll('../../../', Constants.webUrl)
        .replaceAll('../../', Constants.webUrl);
  }

  static List<String?> filterListQuestionBookmark() {
    final localService = Get.find<LocalService>();
    final listAllQuestions = localService.getAllQuestions();
    List<QuestionEntity> bookmarked = [];
    List<QuestionEntity> bookmarkedQuestions =
        listAllQuestions.where((q) => q.isBookmark == true).toList();
    for (QuestionEntity element in bookmarkedQuestions) {
      QuestionEntity? latestQuestion = listAllQuestions.firstWhereOrNull(
          (e) => e.qid == element.qid && e.isBookmark == true);
      bookmarked.add(
        QuestionEntity(
          qid: element.qid,
          question: element.question,
          description: element.description,
          options: latestQuestion?.options ?? element.options,
          status: latestQuestion?.status ?? element.status,
          isCorrect: latestQuestion?.isCorrect ?? element.isCorrect,
          type: element.type,
          isBookmark: element.isBookmark,
        ),
      );
    }
    // Tạo set các ID đã bookmark để dễ kiểm tra
    List<String?> bookmarkedQids = bookmarked.map((q) => q.qid).toList();
    return bookmarkedQids;
  }

  static List<QuestionEntity> updateQuestionBookmarkState(
      List<QuestionEntity> questionEntites) {
    // Danh sách id các câu hỏi đã bookmark
    List<String?> bookmarkedQids = filterListQuestionBookmark();
    // Cập nhật từng phần tử trong danh sách
    final updatedQuestions = questionEntites.map((q) {
      if (!bookmarkedQids.contains(q.qid)) {
        return q.copyWith(isBookmark: false);
      }
      return q;
    }).toList();

    return updatedQuestions;
  }

  QuestionEntity copyWith({
    String? qid,
    QuestionType? type,
    String? question,
    List<OptionEntity>? options,
    String? description,
    String? nextQuestion,
    bool? isBookmark,
    QuestionStatus? status,
    bool? isCorrect,
    int? timeAnswering,
  }) {
    return QuestionEntity(
      qid: qid ?? this.qid,
      type: type ?? this.type,
      question: question ?? this.question,
      options: options ?? this.options,
      description: description ?? this.description,
      nextQuestion: nextQuestion ?? this.nextQuestion,
      isBookmark: isBookmark ?? this.isBookmark,
      status: status ?? this.status,
      isCorrect: isCorrect ?? this.isCorrect,
      timeAnswering: timeAnswering ?? this.timeAnswering,
    );
  }
}

@Embedded(inheritance: false)
class OptionEntity extends Equatable {
  OptionEntity({
    this.oid,
    this.qOption,
    this.score,
  });
  final String? oid;
  final String? qOption;
  final String? score;
  bool isSelected = false;

  @ignore
  @override
  List<Object?> get props => [oid];

  @ignore
  bool get isCorrect {
    final score = double.tryParse(this.score ?? '0') ?? 0;
    return score > 0;
  }

  @ignore
  OptionStatus get status {
    if (isSelected) {
      return OptionStatus.selected;
    } else {
      return OptionStatus.none;
    }
  }

  @ignore
  OptionStatus get resultStatus {
    if (isSelected) {
      if (isCorrect) {
        return OptionStatus.correct;
      } else {
        return OptionStatus.wrong;
      }
    } else {
      if (isCorrect) {
        return OptionStatus.notSelectCorrect;
      } else {
        return OptionStatus.none;
      }
    }
  }
}
