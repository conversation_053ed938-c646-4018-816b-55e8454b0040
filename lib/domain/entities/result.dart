import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';

part 'result.g.dart';

@Collection()
class ResultEntity {
  final String testName;
  final DateTime time;
  int timeDoTest;
  List<QuestionResult> questions;
  @Enumerated(EnumType.name)
  ResultStatus status;
  @Enumerated(EnumType.name)
  TestType type;
  double score;
  bool? pass;
  List<String>? domain;
  final mockTest = IsarLink<QuizEntity>();

  final Id? id = Utils.generateDbId();

  ResultEntity({
    required this.testName,
    required this.time,
    required this.timeDoTest,
    required this.questions,
    required this.status,
    required this.type,
    this.score = 0,
    this.pass,
    this.domain,
    QuizEntity? mockTest,
  }) {
    if (mockTest != null) {
      this.mockTest.value = mockTest;
    }
  }

  @ignore
  bool get isMockTest {
    return type == TestType.miniTest || type == TestType.fullTest;
  }
}

@Embedded(inheritance: false)
class QuestionResult extends Equatable {
  QuestionResult({
    this.question,
    this.qid,
    this.type,
    this.options,
    this.description,
    this.isBookmark,
    this.status,
    this.isCorrect,
    this.indexInTest,
  });
  final String? question;
  final String? qid;
  @Enumerated(EnumType.name)
  final QuestionType? type;
  final List<OptionEntity>? options;
  final String? description;
  final bool? isBookmark;
  @Enumerated(EnumType.name)
  final QuestionStatus? status;
  final bool? isCorrect;
  @ignore
  int? indexInTest;

  @ignore
  bool get hasAnySelect {
    final isSelected = options?.where((e) => e.isSelected).isNotEmpty;
    return isSelected ?? false;
  }

  String getQuestionText() {
    if (question?.isEmpty ?? true) return "";
    String questionText = question!;
    if (questionText.startsWith('<p>')) {
      questionText = questionText.replaceFirst('<p>', '');
      questionText = questionText = questionText.replaceFirst('</p>', '');
    } else if (RegExp(r'^<p><br />.*').hasMatch(questionText)) {
      questionText = questionText.substring(9, questionText.length - 4);
    } else if (RegExp(r'<p>.*?</p>').hasMatch(questionText)) {
      questionText = questionText.substring(3, questionText.length - 4);
    } else if (RegExp(r'^<br>.*').hasMatch(questionText)) {
      questionText = questionText.substring(4);
    }
    return questionText
        .replaceAll('../../../', Constants.webUrl)
        .replaceAll('../../', Constants.webUrl);
  }

  static List<QuestionResult> removeQuestionNotBookmarked(
      List<QuestionResult> questionEntites) {
    // Danh sách id các câu hỏi đã bookmark
    List<String?> bookmarkedQids = QuestionEntity.filterListQuestionBookmark();

    // Giữ lại những câu hỏi có trong danh sách bookmarked
    final filteredQuestions =
        questionEntites.where((q) => bookmarkedQids.contains(q.qid)).toList();

    return filteredQuestions;
  }

  @ignore
  @override
  List<Object?> get props => [
        qid,
      ];

  @ignore
  QuestionEntity toCleanQuestionEntity() {
    final newOptions = options?.map<OptionEntity>((e) {
      e.isSelected = false;
      return e;
    }).toList();
    return QuestionEntity(
      question: question,
      qid: qid,
      type: type,
      options: newOptions,
      description: description,
      isBookmark: isBookmark ?? false,
      status: QuestionStatus.notAnswered,
      isCorrect: false,
    );
  }
}
