// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'relative_app.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RelativeAppEntity {
  String? get name => throw _privateConstructorUsedError;
  String? get iosId => throw _privateConstructorUsedError;
  String? get androidId => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RelativeAppEntityCopyWith<RelativeAppEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RelativeAppEntityCopyWith<$Res> {
  factory $RelativeAppEntityCopyWith(
          RelativeAppEntity value, $Res Function(RelativeAppEntity) then) =
      _$RelativeAppEntityCopyWithImpl<$Res, RelativeAppEntity>;
  @useResult
  $Res call({String? name, String? iosId, String? androidId, String? icon});
}

/// @nodoc
class _$RelativeAppEntityCopyWithImpl<$Res, $Val extends RelativeAppEntity>
    implements $RelativeAppEntityCopyWith<$Res> {
  _$RelativeAppEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? iosId = freezed,
    Object? androidId = freezed,
    Object? icon = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      iosId: freezed == iosId
          ? _value.iosId
          : iosId // ignore: cast_nullable_to_non_nullable
              as String?,
      androidId: freezed == androidId
          ? _value.androidId
          : androidId // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RelativeAppEntityImplCopyWith<$Res>
    implements $RelativeAppEntityCopyWith<$Res> {
  factory _$$RelativeAppEntityImplCopyWith(_$RelativeAppEntityImpl value,
          $Res Function(_$RelativeAppEntityImpl) then) =
      __$$RelativeAppEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, String? iosId, String? androidId, String? icon});
}

/// @nodoc
class __$$RelativeAppEntityImplCopyWithImpl<$Res>
    extends _$RelativeAppEntityCopyWithImpl<$Res, _$RelativeAppEntityImpl>
    implements _$$RelativeAppEntityImplCopyWith<$Res> {
  __$$RelativeAppEntityImplCopyWithImpl(_$RelativeAppEntityImpl _value,
      $Res Function(_$RelativeAppEntityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? iosId = freezed,
    Object? androidId = freezed,
    Object? icon = freezed,
  }) {
    return _then(_$RelativeAppEntityImpl(
      freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      freezed == iosId
          ? _value.iosId
          : iosId // ignore: cast_nullable_to_non_nullable
              as String?,
      freezed == androidId
          ? _value.androidId
          : androidId // ignore: cast_nullable_to_non_nullable
              as String?,
      freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$RelativeAppEntityImpl implements _RelativeAppEntity {
  _$RelativeAppEntityImpl(this.name, this.iosId, this.androidId, this.icon);

  @override
  final String? name;
  @override
  final String? iosId;
  @override
  final String? androidId;
  @override
  final String? icon;

  @override
  String toString() {
    return 'RelativeAppEntity(name: $name, iosId: $iosId, androidId: $androidId, icon: $icon)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RelativeAppEntityImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.iosId, iosId) || other.iosId == iosId) &&
            (identical(other.androidId, androidId) ||
                other.androidId == androidId) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @override
  int get hashCode => Object.hash(runtimeType, name, iosId, androidId, icon);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RelativeAppEntityImplCopyWith<_$RelativeAppEntityImpl> get copyWith =>
      __$$RelativeAppEntityImplCopyWithImpl<_$RelativeAppEntityImpl>(
          this, _$identity);
}

abstract class _RelativeAppEntity implements RelativeAppEntity {
  factory _RelativeAppEntity(final String? name, final String? iosId,
      final String? androidId, final String? icon) = _$RelativeAppEntityImpl;

  @override
  String? get name;
  @override
  String? get iosId;
  @override
  String? get androidId;
  @override
  String? get icon;
  @override
  @JsonKey(ignore: true)
  _$$RelativeAppEntityImplCopyWith<_$RelativeAppEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
