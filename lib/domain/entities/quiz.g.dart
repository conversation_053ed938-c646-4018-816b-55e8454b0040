// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetQuizEntityCollection on Isar {
  IsarCollection<QuizEntity> get quizEntitys => this.collection();
}

const QuizEntitySchema = CollectionSchema(
  name: r'QuizEntity',
  id: -1952386176760672561,
  properties: {
    r'description': PropertySchema(
      id: 0,
      name: r'description',
      type: IsarType.string,
    ),
    r'duration': PropertySchema(
      id: 1,
      name: r'duration',
      type: IsarType.long,
    ),
    r'highscore': PropertySchema(
      id: 2,
      name: r'highscore',
      type: IsarType.double,
    ),
    r'noq': PropertySchema(
      id: 3,
      name: r'noq',
      type: IsarType.long,
    ),
    r'passPercentage': PropertySchema(
      id: 4,
      name: r'passPercentage',
      type: IsarType.long,
    ),
    r'premium': PropertySchema(
      id: 5,
      name: r'premium',
      type: IsarType.bool,
    ),
    r'quid': PropertySchema(
      id: 6,
      name: r'quid',
      type: IsarType.string,
    ),
    r'quizName': PropertySchema(
      id: 7,
      name: r'quizName',
      type: IsarType.string,
    )
  },
  estimateSize: _quizEntityEstimateSize,
  serialize: _quizEntitySerialize,
  deserialize: _quizEntityDeserialize,
  deserializeProp: _quizEntityDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _quizEntityGetId,
  getLinks: _quizEntityGetLinks,
  attach: _quizEntityAttach,
  version: '3.1.0+1',
);

int _quizEntityEstimateSize(
  QuizEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.description.length * 3;
  bytesCount += 3 + object.quid.length * 3;
  bytesCount += 3 + object.quizName.length * 3;
  return bytesCount;
}

void _quizEntitySerialize(
  QuizEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.description);
  writer.writeLong(offsets[1], object.duration);
  writer.writeDouble(offsets[2], object.highscore);
  writer.writeLong(offsets[3], object.noq);
  writer.writeLong(offsets[4], object.passPercentage);
  writer.writeBool(offsets[5], object.premium);
  writer.writeString(offsets[6], object.quid);
  writer.writeString(offsets[7], object.quizName);
}

QuizEntity _quizEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = QuizEntity(
    description: reader.readString(offsets[0]),
    duration: reader.readLong(offsets[1]),
    noq: reader.readLong(offsets[3]),
    passPercentage: reader.readLong(offsets[4]),
    premium: reader.readBoolOrNull(offsets[5]) ?? false,
    quid: reader.readString(offsets[6]),
    quizName: reader.readString(offsets[7]),
  );
  object.highscore = reader.readDouble(offsets[2]);
  return object;
}

P _quizEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readString(offset)) as P;
    case 1:
      return (reader.readLong(offset)) as P;
    case 2:
      return (reader.readDouble(offset)) as P;
    case 3:
      return (reader.readLong(offset)) as P;
    case 4:
      return (reader.readLong(offset)) as P;
    case 5:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 6:
      return (reader.readString(offset)) as P;
    case 7:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _quizEntityGetId(QuizEntity object) {
  return object.id ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _quizEntityGetLinks(QuizEntity object) {
  return [];
}

void _quizEntityAttach(IsarCollection<dynamic> col, Id id, QuizEntity object) {}

extension QuizEntityQueryWhereSort
    on QueryBuilder<QuizEntity, QuizEntity, QWhere> {
  QueryBuilder<QuizEntity, QuizEntity, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension QuizEntityQueryWhere
    on QueryBuilder<QuizEntity, QuizEntity, QWhereClause> {
  QueryBuilder<QuizEntity, QuizEntity, QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterWhereClause> idGreaterThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterWhereClause> idLessThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension QuizEntityQueryFilter
    on QueryBuilder<QuizEntity, QuizEntity, QFilterCondition> {
  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'description',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'description',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      descriptionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> durationEqualTo(
      int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'duration',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      durationGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'duration',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> durationLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'duration',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> durationBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'duration',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> highscoreEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'highscore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      highscoreGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'highscore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> highscoreLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'highscore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> highscoreBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'highscore',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> idEqualTo(
      Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> idGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> idLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> idBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> noqEqualTo(
      int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'noq',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> noqGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'noq',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> noqLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'noq',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> noqBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'noq',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      passPercentageEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'passPercentage',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      passPercentageGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'passPercentage',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      passPercentageLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'passPercentage',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      passPercentageBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'passPercentage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> premiumEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'premium',
        value: value,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'quid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'quid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'quid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'quid',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'quid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'quid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'quid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'quid',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'quid',
        value: '',
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quidIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'quid',
        value: '',
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quizNameEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'quizName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      quizNameGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'quizName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quizNameLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'quizName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quizNameBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'quizName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      quizNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'quizName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quizNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'quizName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quizNameContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'quizName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition> quizNameMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'quizName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      quizNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'quizName',
        value: '',
      ));
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterFilterCondition>
      quizNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'quizName',
        value: '',
      ));
    });
  }
}

extension QuizEntityQueryObject
    on QueryBuilder<QuizEntity, QuizEntity, QFilterCondition> {}

extension QuizEntityQueryLinks
    on QueryBuilder<QuizEntity, QuizEntity, QFilterCondition> {}

extension QuizEntityQuerySortBy
    on QueryBuilder<QuizEntity, QuizEntity, QSortBy> {
  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByDuration() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByDurationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByHighscore() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'highscore', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByHighscoreDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'highscore', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByNoq() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'noq', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByNoqDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'noq', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByPassPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'passPercentage', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy>
      sortByPassPercentageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'passPercentage', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByPremium() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'premium', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByPremiumDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'premium', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByQuid() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quid', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByQuidDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quid', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByQuizName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quizName', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> sortByQuizNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quizName', Sort.desc);
    });
  }
}

extension QuizEntityQuerySortThenBy
    on QueryBuilder<QuizEntity, QuizEntity, QSortThenBy> {
  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByDuration() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByDurationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByHighscore() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'highscore', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByHighscoreDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'highscore', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByNoq() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'noq', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByNoqDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'noq', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByPassPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'passPercentage', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy>
      thenByPassPercentageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'passPercentage', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByPremium() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'premium', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByPremiumDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'premium', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByQuid() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quid', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByQuidDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quid', Sort.desc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByQuizName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quizName', Sort.asc);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QAfterSortBy> thenByQuizNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'quizName', Sort.desc);
    });
  }
}

extension QuizEntityQueryWhereDistinct
    on QueryBuilder<QuizEntity, QuizEntity, QDistinct> {
  QueryBuilder<QuizEntity, QuizEntity, QDistinct> distinctByDescription(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'description', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QDistinct> distinctByDuration() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'duration');
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QDistinct> distinctByHighscore() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'highscore');
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QDistinct> distinctByNoq() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'noq');
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QDistinct> distinctByPassPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'passPercentage');
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QDistinct> distinctByPremium() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'premium');
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QDistinct> distinctByQuid(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'quid', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QuizEntity, QuizEntity, QDistinct> distinctByQuizName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'quizName', caseSensitive: caseSensitive);
    });
  }
}

extension QuizEntityQueryProperty
    on QueryBuilder<QuizEntity, QuizEntity, QQueryProperty> {
  QueryBuilder<QuizEntity, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<QuizEntity, String, QQueryOperations> descriptionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'description');
    });
  }

  QueryBuilder<QuizEntity, int, QQueryOperations> durationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'duration');
    });
  }

  QueryBuilder<QuizEntity, double, QQueryOperations> highscoreProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'highscore');
    });
  }

  QueryBuilder<QuizEntity, int, QQueryOperations> noqProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'noq');
    });
  }

  QueryBuilder<QuizEntity, int, QQueryOperations> passPercentageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'passPercentage');
    });
  }

  QueryBuilder<QuizEntity, bool, QQueryOperations> premiumProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'premium');
    });
  }

  QueryBuilder<QuizEntity, String, QQueryOperations> quidProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'quid');
    });
  }

  QueryBuilder<QuizEntity, String, QQueryOperations> quizNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'quizName');
    });
  }
}
