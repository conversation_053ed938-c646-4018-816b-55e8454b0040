// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_data.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetAppDataCollection on Isar {
  IsarCollection<AppData> get appDatas => this.collection();
}

const AppDataSchema = CollectionSchema(
  name: r'AppData',
  id: 3950144987861183497,
  properties: {
    r'debugId': PropertySchema(
      id: 0,
      name: r'debugId',
      type: IsarType.string,
    ),
    r'environment': PropertySchema(
      id: 1,
      name: r'environment',
      type: IsarType.string,
      enumMap: _AppDataenvironmentEnumValueMap,
    ),
    r'finishPrimaryTour': PropertySchema(
      id: 2,
      name: r'finishPrimaryTour',
      type: IsarType.bool,
    ),
    r'finishTourAnalyst': PropertySchema(
      id: 3,
      name: r'finishTourAnalyst',
      type: IsarType.bool,
    ),
    r'finishTourMock': PropertySchema(
      id: 4,
      name: r'finishTourMock',
      type: IsarType.bool,
    ),
    r'finishTourReview': PropertySchema(
      id: 5,
      name: r'finishTourReview',
      type: IsarType.bool,
    ),
    r'firstLaunch': PropertySchema(
      id: 6,
      name: r'firstLaunch',
      type: IsarType.bool,
    ),
    r'lastOpen': PropertySchema(
      id: 7,
      name: r'lastOpen',
      type: IsarType.dateTime,
    ),
    r'locale': PropertySchema(
      id: 8,
      name: r'locale',
      type: IsarType.string,
      enumMap: _AppDatalocaleEnumValueMap,
    ),
    r'reminder': PropertySchema(
      id: 9,
      name: r'reminder',
      type: IsarType.object,
      target: r'Reminder',
    ),
    r'sessionId': PropertySchema(
      id: 10,
      name: r'sessionId',
      type: IsarType.string,
    ),
    r'streak': PropertySchema(
      id: 11,
      name: r'streak',
      type: IsarType.long,
    ),
    r'testDate': PropertySchema(
      id: 12,
      name: r'testDate',
      type: IsarType.dateTime,
    ),
    r'testFontSize': PropertySchema(
      id: 13,
      name: r'testFontSize',
      type: IsarType.double,
    ),
    r'trainingMode': PropertySchema(
      id: 14,
      name: r'trainingMode',
      type: IsarType.string,
      enumMap: _AppDatatrainingModeEnumValueMap,
    ),
    r'userName': PropertySchema(
      id: 15,
      name: r'userName',
      type: IsarType.string,
    )
  },
  estimateSize: _appDataEstimateSize,
  serialize: _appDataSerialize,
  deserialize: _appDataDeserialize,
  deserializeProp: _appDataDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {r'Reminder': ReminderSchema},
  getId: _appDataGetId,
  getLinks: _appDataGetLinks,
  attach: _appDataAttach,
  version: '3.1.0+1',
);

int _appDataEstimateSize(
  AppData object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.debugId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.environment;
    if (value != null) {
      bytesCount += 3 + value.name.length * 3;
    }
  }
  {
    final value = object.locale;
    if (value != null) {
      bytesCount += 3 + value.name.length * 3;
    }
  }
  {
    final value = object.reminder;
    if (value != null) {
      bytesCount += 3 +
          ReminderSchema.estimateSize(value, allOffsets[Reminder]!, allOffsets);
    }
  }
  {
    final value = object.sessionId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.trainingMode;
    if (value != null) {
      bytesCount += 3 + value.name.length * 3;
    }
  }
  {
    final value = object.userName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _appDataSerialize(
  AppData object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.debugId);
  writer.writeString(offsets[1], object.environment?.name);
  writer.writeBool(offsets[2], object.finishPrimaryTour);
  writer.writeBool(offsets[3], object.finishTourAnalyst);
  writer.writeBool(offsets[4], object.finishTourMock);
  writer.writeBool(offsets[5], object.finishTourReview);
  writer.writeBool(offsets[6], object.firstLaunch);
  writer.writeDateTime(offsets[7], object.lastOpen);
  writer.writeString(offsets[8], object.locale?.name);
  writer.writeObject<Reminder>(
    offsets[9],
    allOffsets,
    ReminderSchema.serialize,
    object.reminder,
  );
  writer.writeString(offsets[10], object.sessionId);
  writer.writeLong(offsets[11], object.streak);
  writer.writeDateTime(offsets[12], object.testDate);
  writer.writeDouble(offsets[13], object.testFontSize);
  writer.writeString(offsets[14], object.trainingMode?.name);
  writer.writeString(offsets[15], object.userName);
}

AppData _appDataDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AppData(
    debugId: reader.readStringOrNull(offsets[0]),
    environment:
        _AppDataenvironmentValueEnumMap[reader.readStringOrNull(offsets[1])],
    finishPrimaryTour: reader.readBoolOrNull(offsets[2]) ?? false,
    finishTourAnalyst: reader.readBoolOrNull(offsets[3]) ?? false,
    finishTourMock: reader.readBoolOrNull(offsets[4]) ?? false,
    finishTourReview: reader.readBoolOrNull(offsets[5]) ?? false,
    firstLaunch: reader.readBoolOrNull(offsets[6]) ?? false,
    lastOpen: reader.readDateTimeOrNull(offsets[7]),
    locale: _AppDatalocaleValueEnumMap[reader.readStringOrNull(offsets[8])],
    reminder: reader.readObjectOrNull<Reminder>(
      offsets[9],
      ReminderSchema.deserialize,
      allOffsets,
    ),
    sessionId: reader.readStringOrNull(offsets[10]),
    streak: reader.readLongOrNull(offsets[11]) ?? 0,
    testDate: reader.readDateTimeOrNull(offsets[12]),
    testFontSize: reader.readDoubleOrNull(offsets[13]),
    trainingMode:
        _AppDatatrainingModeValueEnumMap[reader.readStringOrNull(offsets[14])],
    userName: reader.readStringOrNull(offsets[15]),
  );
  return object;
}

P _appDataDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (_AppDataenvironmentValueEnumMap[reader.readStringOrNull(offset)])
          as P;
    case 2:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 3:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 4:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 5:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 6:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (_AppDatalocaleValueEnumMap[reader.readStringOrNull(offset)]) as P;
    case 9:
      return (reader.readObjectOrNull<Reminder>(
        offset,
        ReminderSchema.deserialize,
        allOffsets,
      )) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 12:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 13:
      return (reader.readDoubleOrNull(offset)) as P;
    case 14:
      return (_AppDatatrainingModeValueEnumMap[reader.readStringOrNull(offset)])
          as P;
    case 15:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _AppDataenvironmentEnumValueMap = {
  r'dev': r'dev',
  r'prod': r'prod',
  r'prodBackup': r'prodBackup',
  r'local': r'local',
};
const _AppDataenvironmentValueEnumMap = {
  r'dev': Environment.dev,
  r'prod': Environment.prod,
  r'prodBackup': Environment.prodBackup,
  r'local': Environment.local,
};
const _AppDatalocaleEnumValueMap = {
  r'en': r'en',
  r'vi': r'vi',
  r'de': r'de',
  r'fr': r'fr',
  r'hi': r'hi',
};
const _AppDatalocaleValueEnumMap = {
  r'en': Language.en,
  r'vi': Language.vi,
  r'de': Language.de,
  r'fr': Language.fr,
  r'hi': Language.hi,
};
const _AppDatatrainingModeEnumValueMap = {
  r'practice': r'practice',
  r'quickPractice': r'quickPractice',
  r'exam': r'exam',
};
const _AppDatatrainingModeValueEnumMap = {
  r'practice': TrainingMode.practice,
  r'quickPractice': TrainingMode.quickPractice,
  r'exam': TrainingMode.exam,
};

Id _appDataGetId(AppData object) {
  return object.id ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _appDataGetLinks(AppData object) {
  return [];
}

void _appDataAttach(IsarCollection<dynamic> col, Id id, AppData object) {}

extension AppDataQueryWhereSort on QueryBuilder<AppData, AppData, QWhere> {
  QueryBuilder<AppData, AppData, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension AppDataQueryWhere on QueryBuilder<AppData, AppData, QWhereClause> {
  QueryBuilder<AppData, AppData, QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<AppData, AppData, QAfterWhereClause> idGreaterThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<AppData, AppData, QAfterWhereClause> idLessThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<AppData, AppData, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AppDataQueryFilter
    on QueryBuilder<AppData, AppData, QFilterCondition> {
  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'debugId',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'debugId',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'debugId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'debugId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'debugId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'debugId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'debugId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'debugId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'debugId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'debugId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'debugId',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> debugIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'debugId',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'environment',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'environment',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentEqualTo(
    Environment? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'environment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentGreaterThan(
    Environment? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'environment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentLessThan(
    Environment? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'environment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentBetween(
    Environment? lower,
    Environment? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'environment',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'environment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'environment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'environment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'environment',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> environmentIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'environment',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition>
      environmentIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'environment',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition>
      finishPrimaryTourEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'finishPrimaryTour',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition>
      finishTourAnalystEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'finishTourAnalyst',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> finishTourMockEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'finishTourMock',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> finishTourReviewEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'finishTourReview',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> firstLaunchEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'firstLaunch',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> idEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> idGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> idLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> idBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> lastOpenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastOpen',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> lastOpenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastOpen',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> lastOpenEqualTo(
      DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastOpen',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> lastOpenGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastOpen',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> lastOpenLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastOpen',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> lastOpenBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastOpen',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'locale',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'locale',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeEqualTo(
    Language? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'locale',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeGreaterThan(
    Language? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'locale',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeLessThan(
    Language? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'locale',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeBetween(
    Language? lower,
    Language? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'locale',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'locale',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'locale',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'locale',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'locale',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'locale',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> localeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'locale',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> reminderIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reminder',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> reminderIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reminder',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sessionId',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sessionId',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sessionId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sessionId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionId',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> sessionIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sessionId',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> streakEqualTo(
      int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'streak',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> streakGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'streak',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> streakLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'streak',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> streakBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'streak',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'testDate',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'testDate',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testDateEqualTo(
      DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'testDate',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'testDate',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'testDate',
        value: value,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'testDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testFontSizeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'testFontSize',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition>
      testFontSizeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'testFontSize',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testFontSizeEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'testFontSize',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testFontSizeGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'testFontSize',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testFontSizeLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'testFontSize',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> testFontSizeBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'testFontSize',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'trainingMode',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition>
      trainingModeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'trainingMode',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeEqualTo(
    TrainingMode? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'trainingMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeGreaterThan(
    TrainingMode? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'trainingMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeLessThan(
    TrainingMode? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'trainingMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeBetween(
    TrainingMode? lower,
    TrainingMode? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'trainingMode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'trainingMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'trainingMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'trainingMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'trainingMode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> trainingModeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'trainingMode',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition>
      trainingModeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'trainingMode',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'userName',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'userName',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'userName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'userName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'userName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'userName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userName',
        value: '',
      ));
    });
  }

  QueryBuilder<AppData, AppData, QAfterFilterCondition> userNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'userName',
        value: '',
      ));
    });
  }
}

extension AppDataQueryObject
    on QueryBuilder<AppData, AppData, QFilterCondition> {
  QueryBuilder<AppData, AppData, QAfterFilterCondition> reminder(
      FilterQuery<Reminder> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'reminder');
    });
  }
}

extension AppDataQueryLinks
    on QueryBuilder<AppData, AppData, QFilterCondition> {}

extension AppDataQuerySortBy on QueryBuilder<AppData, AppData, QSortBy> {
  QueryBuilder<AppData, AppData, QAfterSortBy> sortByDebugId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'debugId', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByDebugIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'debugId', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByEnvironment() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'environment', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByEnvironmentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'environment', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFinishPrimaryTour() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishPrimaryTour', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFinishPrimaryTourDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishPrimaryTour', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFinishTourAnalyst() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourAnalyst', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFinishTourAnalystDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourAnalyst', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFinishTourMock() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourMock', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFinishTourMockDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourMock', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFinishTourReview() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourReview', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFinishTourReviewDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourReview', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFirstLaunch() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firstLaunch', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByFirstLaunchDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firstLaunch', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByLastOpen() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastOpen', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByLastOpenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastOpen', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByLocale() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'locale', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByLocaleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'locale', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortBySessionId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortBySessionIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByStreak() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'streak', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByStreakDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'streak', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByTestDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'testDate', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByTestDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'testDate', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByTestFontSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'testFontSize', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByTestFontSizeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'testFontSize', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByTrainingMode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'trainingMode', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByTrainingModeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'trainingMode', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByUserName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userName', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> sortByUserNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userName', Sort.desc);
    });
  }
}

extension AppDataQuerySortThenBy
    on QueryBuilder<AppData, AppData, QSortThenBy> {
  QueryBuilder<AppData, AppData, QAfterSortBy> thenByDebugId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'debugId', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByDebugIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'debugId', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByEnvironment() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'environment', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByEnvironmentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'environment', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFinishPrimaryTour() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishPrimaryTour', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFinishPrimaryTourDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishPrimaryTour', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFinishTourAnalyst() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourAnalyst', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFinishTourAnalystDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourAnalyst', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFinishTourMock() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourMock', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFinishTourMockDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourMock', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFinishTourReview() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourReview', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFinishTourReviewDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'finishTourReview', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFirstLaunch() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firstLaunch', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByFirstLaunchDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firstLaunch', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByLastOpen() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastOpen', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByLastOpenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastOpen', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByLocale() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'locale', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByLocaleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'locale', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenBySessionId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenBySessionIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByStreak() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'streak', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByStreakDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'streak', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByTestDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'testDate', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByTestDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'testDate', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByTestFontSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'testFontSize', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByTestFontSizeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'testFontSize', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByTrainingMode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'trainingMode', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByTrainingModeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'trainingMode', Sort.desc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByUserName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userName', Sort.asc);
    });
  }

  QueryBuilder<AppData, AppData, QAfterSortBy> thenByUserNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userName', Sort.desc);
    });
  }
}

extension AppDataQueryWhereDistinct
    on QueryBuilder<AppData, AppData, QDistinct> {
  QueryBuilder<AppData, AppData, QDistinct> distinctByDebugId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'debugId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByEnvironment(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'environment', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByFinishPrimaryTour() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'finishPrimaryTour');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByFinishTourAnalyst() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'finishTourAnalyst');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByFinishTourMock() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'finishTourMock');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByFinishTourReview() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'finishTourReview');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByFirstLaunch() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'firstLaunch');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByLastOpen() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastOpen');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByLocale(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'locale', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctBySessionId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sessionId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByStreak() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'streak');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByTestDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'testDate');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByTestFontSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'testFontSize');
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByTrainingMode(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'trainingMode', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppData, AppData, QDistinct> distinctByUserName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userName', caseSensitive: caseSensitive);
    });
  }
}

extension AppDataQueryProperty
    on QueryBuilder<AppData, AppData, QQueryProperty> {
  QueryBuilder<AppData, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<AppData, String?, QQueryOperations> debugIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'debugId');
    });
  }

  QueryBuilder<AppData, Environment?, QQueryOperations> environmentProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'environment');
    });
  }

  QueryBuilder<AppData, bool, QQueryOperations> finishPrimaryTourProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'finishPrimaryTour');
    });
  }

  QueryBuilder<AppData, bool, QQueryOperations> finishTourAnalystProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'finishTourAnalyst');
    });
  }

  QueryBuilder<AppData, bool, QQueryOperations> finishTourMockProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'finishTourMock');
    });
  }

  QueryBuilder<AppData, bool, QQueryOperations> finishTourReviewProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'finishTourReview');
    });
  }

  QueryBuilder<AppData, bool, QQueryOperations> firstLaunchProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'firstLaunch');
    });
  }

  QueryBuilder<AppData, DateTime?, QQueryOperations> lastOpenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastOpen');
    });
  }

  QueryBuilder<AppData, Language?, QQueryOperations> localeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'locale');
    });
  }

  QueryBuilder<AppData, Reminder?, QQueryOperations> reminderProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reminder');
    });
  }

  QueryBuilder<AppData, String?, QQueryOperations> sessionIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sessionId');
    });
  }

  QueryBuilder<AppData, int, QQueryOperations> streakProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'streak');
    });
  }

  QueryBuilder<AppData, DateTime?, QQueryOperations> testDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'testDate');
    });
  }

  QueryBuilder<AppData, double?, QQueryOperations> testFontSizeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'testFontSize');
    });
  }

  QueryBuilder<AppData, TrainingMode?, QQueryOperations>
      trainingModeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'trainingMode');
    });
  }

  QueryBuilder<AppData, String?, QQueryOperations> userNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userName');
    });
  }
}

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const ReminderSchema = Schema(
  name: r'Reminder',
  id: -8566764253612256045,
  properties: {
    r'dayReminders': PropertySchema(
      id: 0,
      name: r'dayReminders',
      type: IsarType.stringList,
      enumMap: _ReminderdayRemindersEnumValueMap,
    ),
    r'isActive': PropertySchema(
      id: 1,
      name: r'isActive',
      type: IsarType.bool,
    ),
    r'reminderTime': PropertySchema(
      id: 2,
      name: r'reminderTime',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _reminderEstimateSize,
  serialize: _reminderSerialize,
  deserialize: _reminderDeserialize,
  deserializeProp: _reminderDeserializeProp,
);

int _reminderEstimateSize(
  Reminder object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final list = object.dayReminders;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.name.length * 3;
        }
      }
    }
  }
  return bytesCount;
}

void _reminderSerialize(
  Reminder object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeStringList(
      offsets[0], object.dayReminders?.map((e) => e.name).toList());
  writer.writeBool(offsets[1], object.isActive);
  writer.writeDateTime(offsets[2], object.reminderTime);
}

Reminder _reminderDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = Reminder(
    dayReminders: reader
        .readStringList(offsets[0])
        ?.map((e) => _ReminderdayRemindersValueEnumMap[e] ?? ReminderDay.monday)
        .toList(),
    isActive: reader.readBoolOrNull(offsets[1]) ?? false,
    reminderTime: reader.readDateTimeOrNull(offsets[2]),
  );
  return object;
}

P _reminderDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader
          .readStringList(offset)
          ?.map(
              (e) => _ReminderdayRemindersValueEnumMap[e] ?? ReminderDay.monday)
          .toList()) as P;
    case 1:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _ReminderdayRemindersEnumValueMap = {
  r'monday': r'monday',
  r'tuesday': r'tuesday',
  r'wednesday': r'wednesday',
  r'thursday': r'thursday',
  r'friday': r'friday',
  r'saturday': r'saturday',
  r'sunday': r'sunday',
};
const _ReminderdayRemindersValueEnumMap = {
  r'monday': ReminderDay.monday,
  r'tuesday': ReminderDay.tuesday,
  r'wednesday': ReminderDay.wednesday,
  r'thursday': ReminderDay.thursday,
  r'friday': ReminderDay.friday,
  r'saturday': ReminderDay.saturday,
  r'sunday': ReminderDay.sunday,
};

extension ReminderQueryFilter
    on QueryBuilder<Reminder, Reminder, QFilterCondition> {
  QueryBuilder<Reminder, Reminder, QAfterFilterCondition> dayRemindersIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dayReminders',
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dayReminders',
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementEqualTo(
    ReminderDay value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dayReminders',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementGreaterThan(
    ReminderDay value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dayReminders',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementLessThan(
    ReminderDay value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dayReminders',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementBetween(
    ReminderDay lower,
    ReminderDay upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dayReminders',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dayReminders',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dayReminders',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dayReminders',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dayReminders',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dayReminders',
        value: '',
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dayReminders',
        value: '',
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'dayReminders',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'dayReminders',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'dayReminders',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'dayReminders',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'dayReminders',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      dayRemindersLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'dayReminders',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition> isActiveEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isActive',
        value: value,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition> reminderTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reminderTime',
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      reminderTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reminderTime',
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition> reminderTimeEqualTo(
      DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reminderTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition>
      reminderTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reminderTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition> reminderTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reminderTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Reminder, Reminder, QAfterFilterCondition> reminderTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reminderTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension ReminderQueryObject
    on QueryBuilder<Reminder, Reminder, QFilterCondition> {}
