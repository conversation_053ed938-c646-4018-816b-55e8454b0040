import 'package:isar/isar.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';

part 'app_data.g.dart';

@Collection()
class AppData {
  final String? userName;
  final String? debugId;
  final String? sessionId;
  final DateTime? testDate;
  @Enumerated(EnumType.name)
  final Environment? environment;
  @Enumerated(EnumType.name)
  final Language? locale;
  final double? testFontSize;
  @Enumerated(EnumType.name)
  final TrainingMode? trainingMode;
  final Reminder? reminder;
  final int streak;
  final DateTime? lastOpen;
  final bool firstLaunch;
  final bool finishPrimaryTour;
  final bool finishTourReview;
  final bool finishTourMock;
  final bool finishTourAnalyst;

  late final Id? id = debugId.hashCode;

  AppData({
    this.userName,
    this.debugId,
    this.testDate,
    this.environment,
    this.sessionId,
    this.locale,
    this.testFontSize,
    this.trainingMode,
    this.reminder,
    this.lastOpen,
    this.streak = 0,
    this.firstLaunch = false,
    this.finishPrimaryTour = false,
    this.finishTourReview = false,
    this.finishTourMock = false,
    this.finishTourAnalyst = false,
  });

  AppData copyWith({
    String? userName,
    String? debugId,
    DateTime? testDate,
    Environment? environment,
    String? sessionId,
    Language? locale,
    double? testFontSize,
    TrainingMode? trainingMode,
    Reminder? reminder,
    DateTime? lastOpen,
    int? streak,
    bool? firstLaunch,
    bool? finishPrimaryTour,
    bool? finishTourReview,
    bool? finishTourMock,
    bool? finishTourAnalyst,
  }) {
    return AppData(
      userName: userName ?? this.userName,
      debugId: debugId ?? this.debugId,
      testDate: testDate ?? this.testDate,
      environment: environment ?? this.environment,
      sessionId: sessionId ?? this.sessionId,
      locale: locale ?? this.locale,
      testFontSize: testFontSize ?? this.testFontSize,
      trainingMode: trainingMode ?? this.trainingMode,
      reminder: reminder ?? this.reminder,
      lastOpen: lastOpen ?? this.lastOpen,
      streak: streak ?? this.streak,
      firstLaunch: firstLaunch ?? this.firstLaunch,
      finishPrimaryTour: finishPrimaryTour ?? this.finishPrimaryTour,
      finishTourReview: finishTourReview ?? this.finishTourReview,
      finishTourMock: finishTourMock ?? this.finishTourMock,
      finishTourAnalyst: finishTourAnalyst ?? this.finishTourAnalyst,
    );
  }
}

@embedded
class Reminder {
  Reminder({
    this.dayReminders,
    this.reminderTime,
    this.isActive = false,
  });
  final DateTime? reminderTime;
  @Enumerated(EnumType.name)
  final List<ReminderDay>? dayReminders;
  final bool isActive;
}
