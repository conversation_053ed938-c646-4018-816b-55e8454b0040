// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'other_app.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetOtherAppEntityCollection on Isar {
  IsarCollection<OtherAppEntity> get otherAppEntitys => this.collection();
}

const OtherAppEntitySchema = CollectionSchema(
  name: r'OtherAppEntity',
  id: -2554552856537736083,
  properties: {
    r'androidID': PropertySchema(
      id: 0,
      name: r'androidID',
      type: IsarType.string,
    ),
    r'appIcon': PropertySchema(
      id: 1,
      name: r'appIcon',
      type: IsarType.string,
    ),
    r'appName': PropertySchema(
      id: 2,
      name: r'appName',
      type: IsarType.string,
    ),
    r'appid': PropertySchema(
      id: 3,
      name: r'appid',
      type: IsarType.string,
    ),
    r'driveIcon': PropertySchema(
      id: 4,
      name: r'driveIcon',
      type: IsarType.string,
    ),
    r'iosID': PropertySchema(
      id: 5,
      name: r'iosID',
      type: IsarType.string,
    )
  },
  estimateSize: _otherAppEntityEstimateSize,
  serialize: _otherAppEntitySerialize,
  deserialize: _otherAppEntityDeserialize,
  deserializeProp: _otherAppEntityDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _otherAppEntityGetId,
  getLinks: _otherAppEntityGetLinks,
  attach: _otherAppEntityAttach,
  version: '3.1.0+1',
);

int _otherAppEntityEstimateSize(
  OtherAppEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.androidID.length * 3;
  bytesCount += 3 + object.appIcon.length * 3;
  bytesCount += 3 + object.appName.length * 3;
  bytesCount += 3 + object.appid.length * 3;
  bytesCount += 3 + object.driveIcon.length * 3;
  bytesCount += 3 + object.iosID.length * 3;
  return bytesCount;
}

void _otherAppEntitySerialize(
  OtherAppEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.androidID);
  writer.writeString(offsets[1], object.appIcon);
  writer.writeString(offsets[2], object.appName);
  writer.writeString(offsets[3], object.appid);
  writer.writeString(offsets[4], object.driveIcon);
  writer.writeString(offsets[5], object.iosID);
}

OtherAppEntity _otherAppEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = OtherAppEntity(
    androidID: reader.readString(offsets[0]),
    appIcon: reader.readString(offsets[1]),
    appName: reader.readString(offsets[2]),
    appid: reader.readString(offsets[3]),
    driveIcon: reader.readString(offsets[4]),
    iosID: reader.readString(offsets[5]),
  );
  return object;
}

P _otherAppEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readString(offset)) as P;
    case 1:
      return (reader.readString(offset)) as P;
    case 2:
      return (reader.readString(offset)) as P;
    case 3:
      return (reader.readString(offset)) as P;
    case 4:
      return (reader.readString(offset)) as P;
    case 5:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _otherAppEntityGetId(OtherAppEntity object) {
  return object.id ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _otherAppEntityGetLinks(OtherAppEntity object) {
  return [];
}

void _otherAppEntityAttach(
    IsarCollection<dynamic> col, Id id, OtherAppEntity object) {}

extension OtherAppEntityQueryWhereSort
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QWhere> {
  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension OtherAppEntityQueryWhere
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QWhereClause> {
  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterWhereClause> idGreaterThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension OtherAppEntityQueryFilter
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QFilterCondition> {
  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'androidID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'androidID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'androidID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'androidID',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'androidID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'androidID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'androidID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'androidID',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'androidID',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      androidIDIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'androidID',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'appIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'appIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'appIcon',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'appIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'appIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'appIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'appIcon',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appIcon',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appIconIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'appIcon',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'appName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'appName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'appName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'appName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'appName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'appName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'appName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appName',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'appName',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'appid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'appid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'appid',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'appid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'appid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'appid',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'appid',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'appid',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      appidIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'appid',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'driveIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'driveIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'driveIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'driveIcon',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'driveIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'driveIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'driveIcon',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'driveIcon',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'driveIcon',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      driveIconIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'driveIcon',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition> idEqualTo(
      Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      idGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      idLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition> idBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iosID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'iosID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'iosID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'iosID',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'iosID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'iosID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'iosID',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'iosID',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iosID',
        value: '',
      ));
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterFilterCondition>
      iosIDIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'iosID',
        value: '',
      ));
    });
  }
}

extension OtherAppEntityQueryObject
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QFilterCondition> {}

extension OtherAppEntityQueryLinks
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QFilterCondition> {}

extension OtherAppEntityQuerySortBy
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QSortBy> {
  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> sortByAndroidID() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'androidID', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy>
      sortByAndroidIDDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'androidID', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> sortByAppIcon() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appIcon', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy>
      sortByAppIconDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appIcon', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> sortByAppName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appName', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy>
      sortByAppNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appName', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> sortByAppid() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appid', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> sortByAppidDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appid', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> sortByDriveIcon() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'driveIcon', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy>
      sortByDriveIconDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'driveIcon', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> sortByIosID() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iosID', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> sortByIosIDDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iosID', Sort.desc);
    });
  }
}

extension OtherAppEntityQuerySortThenBy
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QSortThenBy> {
  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByAndroidID() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'androidID', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy>
      thenByAndroidIDDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'androidID', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByAppIcon() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appIcon', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy>
      thenByAppIconDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appIcon', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByAppName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appName', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy>
      thenByAppNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appName', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByAppid() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appid', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByAppidDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'appid', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByDriveIcon() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'driveIcon', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy>
      thenByDriveIconDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'driveIcon', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByIosID() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iosID', Sort.asc);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QAfterSortBy> thenByIosIDDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iosID', Sort.desc);
    });
  }
}

extension OtherAppEntityQueryWhereDistinct
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QDistinct> {
  QueryBuilder<OtherAppEntity, OtherAppEntity, QDistinct> distinctByAndroidID(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'androidID', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QDistinct> distinctByAppIcon(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'appIcon', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QDistinct> distinctByAppName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'appName', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QDistinct> distinctByAppid(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'appid', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QDistinct> distinctByDriveIcon(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'driveIcon', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<OtherAppEntity, OtherAppEntity, QDistinct> distinctByIosID(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'iosID', caseSensitive: caseSensitive);
    });
  }
}

extension OtherAppEntityQueryProperty
    on QueryBuilder<OtherAppEntity, OtherAppEntity, QQueryProperty> {
  QueryBuilder<OtherAppEntity, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<OtherAppEntity, String, QQueryOperations> androidIDProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'androidID');
    });
  }

  QueryBuilder<OtherAppEntity, String, QQueryOperations> appIconProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'appIcon');
    });
  }

  QueryBuilder<OtherAppEntity, String, QQueryOperations> appNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'appName');
    });
  }

  QueryBuilder<OtherAppEntity, String, QQueryOperations> appidProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'appid');
    });
  }

  QueryBuilder<OtherAppEntity, String, QQueryOperations> driveIconProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'driveIcon');
    });
  }

  QueryBuilder<OtherAppEntity, String, QQueryOperations> iosIDProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'iosID');
    });
  }
}
