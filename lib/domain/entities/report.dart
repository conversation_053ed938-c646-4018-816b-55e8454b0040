import 'package:isar/isar.dart';

part 'report.g.dart';

enum ReportStatus {
  reviewing('1'),
  answered('2'),
  closed('3'),
  ;

  const ReportStatus(this.value);
  final String value;
}

@Collection()
class ReportEntity {
  final String? reportId;
  final String? reportQuestion;
  final String? reportDetail;
  @Enumerated(EnumType.name)
  ReportStatus? status;
  final DateTime? createdAt;

  final Id? id = Isar.autoIncrement;

  ReportEntity(this.createdAt,
      {this.reportId, this.reportQuestion, this.status, this.reportDetail});
}

class ReportResponseEntity {
  final String? response;
  final DateTime? time;
  final bool? isAdmin;

  ReportResponseEntity({this.response, this.time, this.isAdmin});
}
