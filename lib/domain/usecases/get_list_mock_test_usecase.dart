import 'package:scrumpass_exam_simulator/app/core/usecases/pram_usecase.dart';
import 'package:scrumpass_exam_simulator/data/models/quiz_response.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/test_repository.dart';

class GetListMockTestUsecase
    extends ParamUseCase<List<QuizResponse>, GetMockTestParams> {
  final TestRepository _repo;
  GetListMockTestUsecase(this._repo);

  @override
  Future<List<QuizResponse>> execute(GetMockTestParams params) {
    return _repo.getListMockTest();
  }
}

class GetMockTestParams {}
