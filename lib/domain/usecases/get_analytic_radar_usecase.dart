import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/core/usecases/pram_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/app_repository.dart';

class GetAnalyticRadarUsecase
    extends ParamUseCase<String, GetAnalyticRadarParams> {
  final AppRepository _repo;
  GetAnalyticRadarUsecase(this._repo);

  @override
  Future<String> execute(GetAnalyticRadarParams params) {
    return _repo.getAnalyticRadar(Constants.appId, params.qids);
  }
}

class GetAnalyticRadarParams {
  GetAnalyticRadarParams({required this.qids});

  final List<String> qids;
}
