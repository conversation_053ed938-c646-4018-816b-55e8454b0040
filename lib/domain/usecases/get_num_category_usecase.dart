import 'package:scrumpass_exam_simulator/app/core/usecases/pram_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/app_repository.dart';

class GetNumCategoryUsecase
    extends ParamUseCase<String, NumCategoryParams> {
  final AppRepository _repo;
  GetNumCategoryUsecase(this._repo);

  @override
  Future<String> execute(NumCategoryParams params) {
    return _repo.getNumCategory(params.appId);
  }
}

class NumCategoryParams {
  NumCategoryParams({required this.appId});

  final String appId;
}
