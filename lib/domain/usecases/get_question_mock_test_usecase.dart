import 'package:scrumpass_exam_simulator/app/core/usecases/pram_usecase.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/test_repository.dart';

class GetQuestionMockTestUsecase
    extends ParamUseCase<List<QuestionResponse>, GetQuestionMockTestParams> {
  final TestRepository _repo;
  GetQuestionMockTestUsecase(this._repo);

  @override
  Future<List<QuestionResponse>> execute(GetQuestionMockTestParams params) {
    return _repo.getQuestionMockTest(params.id);
  }
}

class GetQuestionMockTestParams {
  GetQuestionMockTestParams({required this.id});

  final int id;
}
