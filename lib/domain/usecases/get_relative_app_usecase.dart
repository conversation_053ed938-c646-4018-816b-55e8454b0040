import 'package:scrumpass_exam_simulator/app/core/usecases/pram_usecase.dart';
import 'package:scrumpass_exam_simulator/data/models/relative_app_response.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/app_repository.dart';

class GetRelativeAppUsecase
    extends ParamUseCase<List<RelativeAppResponse>, RelativeAppParams> {
  final AppRepository _repo;
  GetRelativeAppUsecase(this._repo);

  @override
  Future<List<RelativeAppResponse>> execute(RelativeAppParams params) {
    return _repo.getRelativeApp(params.appId);
  }
}

class RelativeAppParams {
  RelativeAppParams({required this.appId});

  final String appId;
}
