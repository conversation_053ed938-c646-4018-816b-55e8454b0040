import 'package:scrumpass_exam_simulator/app/core/usecases/pram_usecase.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/test_repository.dart';

class DoDomainTestUsecase
    extends ParamUseCase<List<QuestionResponse>, DoDomainTestParams> {
  final TestRepository _repo;
  DoDomainTestUsecase(this._repo);

  @override
  Future<List<QuestionResponse>> execute(DoDomainTestParams params) {
    return _repo.doDomainTest(params.numQuestion, params.testDomains);
  }
}

class DoDomainTestParams {
  DoDomainTestParams({required this.numQuestion, required this.testDomains});

  final int numQuestion;
  final List<TestDomain> testDomains;
}
