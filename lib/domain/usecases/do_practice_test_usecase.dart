import 'package:scrumpass_exam_simulator/app/core/usecases/pram_usecase.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/test_repository.dart';

class DoPracticeTestUsecase
    extends ParamUseCase<List<QuestionResponse>, DoPracticeTestParams> {
  final TestRepository _repo;
  DoPracticeTestUsecase(this._repo);

  @override
  Future<List<QuestionResponse>> execute(DoPracticeTestParams params) {
    return _repo.doPracticeTest(params.numQuestion);
  }
}

class DoPracticeTestParams {
  DoPracticeTestParams({required this.numQuestion});

  final int numQuestion;
}
