import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/core/usecases/pram_usecase.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/report_repository.dart';

class SendReportQuestionUsecase
    extends ParamUseCase<String, SendReportQuestionParams> {
  final ReportRepository _repo;
  SendReportQuestionUsecase(this._repo);
  final localService = Get.find<LocalService>();

  @override
  Future<String> execute(SendReportQuestionParams params) {
    return _repo.sendReport(
        Constants.appId, params.qid, params.reportdetail, params.debugid);
  }
}

class SendReportQuestionParams {
  SendReportQuestionParams({
    required this.qid,
    required this.reportdetail,
    required this.debugid,
  });

  final String qid;
  final String reportdetail;
  final String debugid;
}
