import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onboarding_animation/onboarding_animation.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/round_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/idle_detector.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/onboarding/onbroading_controller.dart';

class OnboardingPage extends GetView<OnbroadingController> {
  const OnboardingPage();
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) {
          return;
        }
      },
      child: IdleDetectorWidget(
        child: Scaffold(
          body: Container(
            height: MediaQuery.sizeOf(context).height,
            width: MediaQuery.sizeOf(context).width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppColors.onbroadPrimaryBg,
                  AppColors.onbroadSecondaryBg,
                ],
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 0,
                  left: 0,
                  child: Assets.images.onboardBg.image(),
                ),
                Positioned(
                  bottom: 32,
                  left: 0,
                  right: 0,
                  child: Assets.images.roundCircle.svg(
                    width: MediaQuery.sizeOf(context).width,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 0, bottom: 32),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        margin:
                            EdgeInsets.only(top: AppBar().preferredSize.height),
                        child: SmoothPageIndicator(
                          controller: controller.pageController,
                          count: 4,
                          effect: WormEffect(
                            dotWidth: 40,
                            dotHeight: 6,
                            activeDotColor: AppColors.bgWhite,
                            dotColor: AppColors.bgWhite.withOpacity(0.4),
                          ),
                        ),
                      ),
                      Expanded(
                        child: PageView(
                          controller: controller.pageController,
                          pageSnapping: true,
                          children: List.generate(
                              controller.onboardingData.length, (index) {
                            final data = controller.onboardingData[index];
                            return OnboardingWidget(
                              context,
                              title: data['title'],
                              description: data['description'],
                              customWidget: data['customWidget'],
                              index: index,
                            );
                          }),
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        child: Obx(
                          () => controller.currentPage.value == 3
                              ? Global.isPremium
                                  ? Container(
                                      margin: EdgeInsets.only(bottom: 16),
                                      child: RoundButton(
                                        title: LocaleKeys.startStudyingNow.tr,
                                        textColor: AppColors.textBtn2nd,
                                        bgColor: AppColors.textWhite,
                                        borderColor: Color(0xff4FA1EB),
                                        onTap: () {
                                          controller.skipBtn();
                                        },
                                      ),
                                    )
                                  : Column(
                                      children: [
                                        RoundButton(
                                          title:
                                              LocaleKeys.startPremiumTrial.tr,
                                          textColor: AppColors.textWhite,
                                          gradient: LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: AppColors
                                                .premiumBenefitGradient,
                                          ),
                                          borderWidth: 2,
                                          onTap: () {
                                            controller.toNextPage();
                                          },
                                        ),
                                        SizedBox(height: 16),
                                        RoundButton(
                                          title: LocaleKeys.continueFree.tr,
                                          textColor: AppColors.textBtn2nd,
                                          bgColor: AppColors.textWhite,
                                          borderColor: Color(0xff4FA1EB),
                                          onTap: () {
                                            controller.skipBtn();
                                          },
                                        ),
                                      ],
                                    )
                              : Column(
                                  children: [
                                    RoundButton(
                                      title: LocaleKeys.next.toCapitalized,
                                      textColor: AppColors.textBtn2nd,
                                      bgColor: AppColors.bgWhite,
                                      borderColor: AppColors.bgWhite,
                                      borderWidth: 2,
                                      onTap: () {
                                        controller.toNextPage();
                                      },
                                    ),
                                    SizedBox(height: 16),
                                    RoundButton(
                                      title: LocaleKeys.skip.toCapitalized,
                                      textColor: AppColors.textWhite,
                                      bgColor: Colors.transparent,
                                      borderColor: AppColors.textWhite,
                                      onTap: () {
                                        controller.skipBtn();
                                      },
                                    ),
                                  ],
                                ),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget OnboardingWidget(context,
      {required String title,
      required String description,
      required Widget customWidget,
      required int index}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 35),
        Container(
          width: index != 3 ? 300 : 350,
          child: Text(
            title,
            style: AppTextStyles.xxxxLargeBold.copyWith(
              color: AppColors.bgWhite,
              height: 0,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(30, 20, 30, 0),
          child: Text(
            description,
            style: AppTextStyles.baseRegular.copyWith(
              color: AppColors.bgWhite,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: customWidget,
        ),
      ],
    );
  }
}
