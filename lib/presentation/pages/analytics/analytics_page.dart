import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overlay_tooltip/overlay_tooltip.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/analytics/analytics_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/knowledge.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/question_detail.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/quiz_detail.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/time_detail.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/product_tour_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

import 'widgets/score_chart.dart';

class AnalyticsPage extends StatelessWidget {
  const AnalyticsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final TourController tourController = Get.find<TourController>();

    return Scaffold(
        body: GetBuilder(
      init: AnalyticsController(),
      builder: (controller) => GestureDetector(
        onTap: () {
          controller.onScoreChartTouched(-1);
        },
        child: Stack(
          children: [
            _Background(),
            SafeArea(
              child: Obx(() => controller.isLoading.value
                  ? _Shimmer()
                  : SingleChildScrollView(
                      controller: controller.scrollController,
                      physics: controller.allowScroll.value
                          ? AlwaysScrollableScrollPhysics()
                          : NeverScrollableScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 40),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Text(
                              LocaleKeys.analyticsHeader.tr,
                              style: AppTextStyles.xxLargeBold
                                  .copyWith(color: AppColors.textInverseTitle),
                            ),
                          ),
                          const SizedBox(height: 20),
                          Container(
                            width: Get.width,
                            child: ProductTourWidget(
                              index: 12,
                              description: LocaleKeys.productTourChart1.tr,
                              icon: Image.asset(
                                Assets.images.statChart.path,
                                width: 24,
                              ),
                              analystics: true,
                              hideIndicator: true,
                              title: LocaleKeys.scoreChartTitle.tr,
                              positionIndicator: TourController.positionCenter,
                              child: ScoreChart(),
                              tourAction: () async {
                                await controller.handleNextTour(offset: 150);
                              },
                            ),
                          ),
                          const SizedBox(height: 20),
                          ProductTourWidget(
                            index: 13,
                            hideIndicator: true,
                            analystics: true,
                            description: LocaleKeys.productTourChart2.tr,
                            icon: Image.asset(
                              Assets.images.statPeace.path,
                              width: 24,
                            ),
                            position: TooltipVerticalPosition.TOP,
                            positionIndicator: TourController.positionCenter,
                            title: LocaleKeys.knowledgeTitle.tr,
                            child: Knowledge(),
                            tourAction: () async {
                              await controller.handleNextTour(offset: 400);
                            },
                            tourBackAction: () async {
                              await controller.handleBackTour(
                                offset: 110,
                                isFixed: true,
                              );
                            },
                          ),
                          const SizedBox(height: 20),
                          ProductTourWidget(
                            index: 14,
                            hideIndicator: true,
                            analystics: true,
                            description: LocaleKeys.productTourChart3.tr,
                            icon: Image.asset(
                              Assets.images.statPin.path,
                              width: 24,
                            ),
                            position: TooltipVerticalPosition.TOP,
                            positionIndicator: TourController.positionCenter,
                            title: LocaleKeys.quizDetailTitle.tr,
                            child: QuizDetail(),
                            tourAction: () async {
                              await controller.handleNextTour();
                            },
                            tourBackAction: () async {
                              await controller.handleBackTour(offset: 400);
                            },
                          ),
                          const SizedBox(height: 20),
                          ProductTourWidget(
                            index: 15,
                            hideIndicator: true,
                            analystics: true,
                            description: LocaleKeys.productTourChart4.tr,
                            icon: Image.asset(
                              Assets.images.statBook.path,
                              width: 24,
                            ),
                            position: TooltipVerticalPosition.TOP,
                            title: LocaleKeys.questionDetailTitle.tr,
                            positionIndicator: TourController.positionCenter,
                            child: QuestionDetail(),
                            tourAction: () async {
                              await controller.handleNextTour(min: true);
                            },
                            tourBackAction: () async {
                              await controller.handleBackTour(min: true);
                            },
                          ),
                          const SizedBox(height: 20),
                          ProductTourWidget(
                              index: 16,
                              analystics: true,
                              icon: Image.asset(
                                Assets.images.statClock.path,
                                width: 24,
                              ),
                              description: LocaleKeys.productTourChart5.tr,
                              hideIndicator: true,
                              isPause: true,
                              positionIndicator: TourController.positionCenter,
                              position: TooltipVerticalPosition.TOP,
                              tourAction: () async {
                                tourController.completeTourAnalyst();
                              },
                              tourBackAction: () async {
                                await controller.handleBackTour(min: true);
                              },
                              title: LocaleKeys.timeDetail.tr,
                              child: TimeDetail()),
                          const SizedBox(height: 100),
                        ],
                      ),
                    )),
            ),
            if (Global.isPremium == false) _PremiumButton()
          ],
        ),
      ),
    ));
  }
}

class _PremiumButton extends StatelessWidget {
  const _PremiumButton();

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 108,
      child: InkWell(
        onTap: () {
          Get.toNamed(RouterName.premium);
        },
        child: SizedBox(
          width: Get.width,
          child: Align(
            alignment: Alignment.center,
            child: DecoratedBox(
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: AppColors.analyticPremiumGradient,
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter),
                  borderRadius: BorderRadius.circular(8)),
              child: Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    child: Text(
                      LocaleKeys.analyticUnlockPremium.tr,
                      style: AppTextStyles.baseMedium
                          .copyWith(color: AppColors.textBtn1st),
                    ),
                  ),
                  Positioned(
                      child: Assets.images.analyticUnlockPre.image(),
                      right: 0,
                      bottom: 0),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _Shimmer extends StatelessWidget {
  const _Shimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Skeletonizer.zone(
        child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 60),
            Bone.text(
              words: 3,
              style: AppTextStyles.xxLargeBold,
            ),
            const SizedBox(height: 20),
            Bone.button(
              width: double.infinity,
              height: 200,
              borderRadius: BorderRadius.circular(16),
            ),
            const SizedBox(height: 20),
            Bone.button(
              width: double.infinity,
              height: 300,
              borderRadius: BorderRadius.circular(16),
            ),
            const SizedBox(height: 20),
            Bone.button(
              width: double.infinity,
              height: 250,
              borderRadius: BorderRadius.circular(16),
            ),
          ],
        ),
      ),
    ));
  }
}

class _Background extends StatelessWidget {
  const _Background();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 515,
      child: DecoratedBox(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: AppColors.analyticsBgGradient,
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              top: -6,
              left: -86,
              child: Assets.images.analyticBg1.image(width: 422),
            ),
            Positioned(
              top: 240,
              right: -30,
              child: Assets.images.analyticBg2.image(width: 173),
            ),
            Positioned(
              top: 60,
              left: -56,
              child: Assets.images.analyticBg3.image(width: 345),
            ),
          ],
        ),
      ),
    );
  }
}
