import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/analytics/analytics_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/statistic_card.dart';

class QuestionDetail extends GetView<AnalyticsController> {
  const QuestionDetail({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StatisticCard(
      enableBlur: true,
      icon: Assets.images.statBook.path,
      title: LocaleKeys.questionDetailTitle.tr,
      bgColor: AppColors.analyticsQuestionDetailGradient,
      child: Padding(
        padding: const EdgeInsets.only(left: 4, top: 4, right: 4),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    controller.totalQuestionDone.toString(),
                    style: AppTextStyles.xxxLargeSemiBold
                        .copyWith(color: AppColors.text1st, fontSize: 36),
                  ),
                  Text(LocaleKeys.questionsDone.tr,
                      style: AppTextStyles.baseMedium
                          .copyWith(color: AppColors.textHint)),
                  const SizedBox(height: 8),
                ],
              ),
            ),
            ChartItem(
                controller: controller,
                count: controller.wrongQuestion,
                height: controller.questionDetailWrongHeight,
                colors: AppColors.questionDetailWrongGradient,
                text: LocaleKeys.incorrect.tr),
            const SizedBox(width: 16),
            ChartItem(
                controller: controller,
                count: controller.bookmarkedQuestion,
                height: controller.questionDetailMarkedHeight,
                colors: AppColors.questionDetailMarkedGradient,
                text: LocaleKeys.bookmarked.tr),
          ],
        ),
      ),
    );
  }
}

class ChartItem extends StatelessWidget {
  const ChartItem({
    super.key,
    required this.controller,
    required this.count,
    required this.height,
    required this.colors,
    required this.text,
  });

  final AnalyticsController controller;
  final int count;
  final double height;
  final List<Color> colors;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(count.toString(),
            style:
                AppTextStyles.xSmallMedium.copyWith(color: AppColors.text2nd)),
        SizedBox(
            height: controller.questionDetailChartHeight,
            width: 75,
            child: DecoratedBox(
                decoration: BoxDecoration(
                    image: DecorationImage(
                        image: AssetImage(
                            Assets.images.questionDetailChartBg.path)),
                    borderRadius: BorderRadius.circular(5)),
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: SizedBox(
                    width: double.infinity,
                    height: height,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          gradient: LinearGradient(
                              colors: colors,
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter)),
                    ),
                  ),
                ))),
        const SizedBox(height: 8),
        Text(text,
            style:
                AppTextStyles.xSmallMedium.copyWith(color: AppColors.textHint))
      ],
    );
  }
}
