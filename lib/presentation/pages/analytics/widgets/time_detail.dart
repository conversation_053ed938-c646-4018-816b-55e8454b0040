import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/int.dart';
import 'package:scrumpass_exam_simulator/app/utils/datetime.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/analytics/analytics_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/statistic_card.dart';

class TimeDetail extends GetView<AnalyticsController> {
  const TimeDetail({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StatisticCard(
      enableBlur: true,
      icon: Assets.images.statClock.path,
      title: LocaleKeys.timeDetail.tr,
      bgColor: AppColors.analyticsTimeDetailGradient,
      titleDescription: RichText(
          text: TextSpan(children: [
        TextSpan(
          text: '${LocaleKeys.latestTime.tr} ',
          style: AppTextStyles.smallMedium.copyWith(color: AppColors.textHint),
        ),
        TextSpan(
          text: controller.results.isEmpty
              ? ''
              : controller.results.first.time.dateTimeString,
          style: AppTextStyles.smallMedium.copyWith(color: AppColors.text1st),
        ),
      ])),
      child: Padding(
        padding: const EdgeInsets.only(left: 4, right: 4, top: 20),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(controller.totalTime.hourMinuteString,
                    style: AppTextStyles.xxxLargeSemiBold
                        .copyWith(color: AppColors.text1st)),
                const SizedBox(height: 4),
                Text(LocaleKeys.totalTime.tr,
                    style: AppTextStyles.baseMedium
                        .copyWith(color: AppColors.textHint))
              ],
            ),
            Expanded(
                child: Center(
              child: Assets.images.arrowRight.svg(width: 24),
            )),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(controller.timeEachQuestion.round().shortTextString,
                    style: AppTextStyles.xLargeSemiBold
                        .copyWith(color: AppColors.text1st)),
                const SizedBox(height: 4),
                Text(LocaleKeys.eachQuestion.tr,
                    style: AppTextStyles.smallMedium
                        .copyWith(color: AppColors.textHint))
              ],
            ),
          ],
        ),
      ),
    );
  }
}
