import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/app/widgets/dash_border.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/analytics/analytics_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/statistic_card.dart';

class ScoreChart extends GetView<AnalyticsController> {
  @override
  AnalyticsController get controller => Get.put(AnalyticsController());

  const ScoreChart({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StatisticCard(
      icon: Assets.images.statChart.path,
      title: LocaleKeys.scoreChartTitle.tr,
      bgColor: AppColors.analyticsScoreChartGradient,
      iconBgColor: AppColors.bgInfoSurface,
      child: Padding(
        padding: const EdgeInsets.only(top: 8),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: AppColors.neutral1,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              const SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(width: 16),
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.highestScore.tr,
                          style: AppTextStyles.xSmallRegular
                              .copyWith(color: AppColors.textHint),
                        ),
                        Text(
                          controller.highestScore.roundedPrecisionToString(0),
                          style: AppTextStyles.xxLargeMedium
                              .copyWith(color: AppColors.text1st),
                        )
                      ]),
                  const SizedBox(width: 24),
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.lowestScore.tr,
                          style: AppTextStyles.xSmallRegular
                              .copyWith(color: AppColors.textHint),
                        ),
                        Text(
                          controller.lowestScore.roundedPrecisionToString(0),
                          style: AppTextStyles.xxLargeMedium
                              .copyWith(color: AppColors.text1st),
                        )
                      ]),
                  const SizedBox(width: 24),
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.newestScore.tr,
                          style: AppTextStyles.xSmallRegular
                              .copyWith(color: AppColors.textHint),
                        ),
                        const SizedBox(height: 4),
                        SizedBox(
                          width: 14,
                          height: 14,
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(4)),
                          ),
                        )
                      ])
                ],
              ),
              const SizedBox(height: 8),
              SizedBox(
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: Text(LocaleKeys.score.tr,
                      textAlign: TextAlign.left,
                      style: AppTextStyles.xxSmallMedium
                          .copyWith(color: AppColors.textHint)),
                ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                height: 198,
                child: GetBuilder<AnalyticsController>(
                  id: AnalyticsConstants.scoreChartKey,
                  builder: (_) {
                    return BarChart(
                      mainBarData(),
                    );
                  },
                ),
              ),
              const SizedBox(height: 4),
              SizedBox(
                width: double.infinity,
                child: Text(
                  LocaleKeys.testTime.tr,
                  style: AppTextStyles.xxSmallMedium
                      .copyWith(color: AppColors.textHint),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }

  BarChartData mainBarData() {
    return BarChartData(
      maxY: 100,
      barTouchData: BarTouchData(
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (_) => AppColors.neutral9,
            tooltipHorizontalAlignment: FLHorizontalAlignment.center,
            tooltipMargin: controller.generateTooltipMargin(),
            tooltipPadding: EdgeInsets.fromLTRB(6, 6, 6, 4),
            tooltipRoundedRadius: 6,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              return BarTooltipItem(
                  '${LocaleKeys.score.tr}: ${rod.toY.roundedPrecisionToString(0)}',
                  AppTextStyles.xSmallMedium
                      .copyWith(color: AppColors.textInverseTitle));
            },
          ),
          enabled: true,
          handleBuiltInTouches: false,
          touchCallback: (event, response) {
            if (response != null &&
                response.spot != null &&
                event is FlTapUpEvent) {
              final x = response.spot!.touchedBarGroup.x;
              final isShowing = controller.scoreChartTouchedIndex == x;
              if (isShowing) {
                controller.onScoreChartTouched(-1);
              } else {
                controller.onScoreChartTouched(x);
              }
            }
          },
          mouseCursorResolver: (event, response) {
            return response == null || response.spot == null
                ? MouseCursor.defer
                : SystemMouseCursors.click;
          }),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: getTitles,
            reservedSize: 38,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: leftTitles,
              interval: 20),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: DashedBorder(
            top: BorderSide(color: AppColors.borderLine),
            bottom: BorderSide(color: AppColors.borderLine),
            dashSpace: 7,
            dashWidth: 7),
      ),
      barGroups: showingGroups(),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        horizontalInterval: 20,
        getDrawingHorizontalLine: (value) => FlLine(
          color: AppColors.borderLine,
          strokeWidth: 1,
          dashArray: [7, 7],
        ),
      ),
    );
  }

  Widget getTitles(double value, TitleMeta meta) {
    final index = controller.results.length > 7
        ? controller.results.length - (7 - (value + 1))
        : value + 1;
    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 16,
      child: Text(
        index.toStringAsFixed(0),
        style: AppTextStyles.xxSmallMedium.copyWith(color: AppColors.textHint),
      ),
    );
  }

  Widget leftTitles(double value, TitleMeta meta) {
    return SideTitleWidget(
      axisSide: AxisSide.right,
      // space: 16,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Text(
          value.toStringAsFixed(0),
          style:
              AppTextStyles.xxSmallMedium.copyWith(color: AppColors.textHint),
        ),
      ),
    );
  }

  List<BarChartGroupData> showingGroups() => List.generate(7, (i) {
        Color barColor = HexColor('#E6EAEF');
        if (controller.results.length < 7) {
          if (controller.results.length == i + 1) barColor = AppColors.primary;
        } else {
          if (controller.scoreChartData.length == i + 1)
            barColor = AppColors.primary;
        }
        return makeGroupData(i, controller.scoreChartData[i],
            barColor: barColor,
            isTouched: controller.scoreChartTouchedIndex == i);
      });

  BarChartGroupData makeGroupData(
    int x,
    double y, {
    bool isTouched = false,
    Color? barColor,
    double width = 28,
    List<int> showTooltips = const [],
  }) {
    return BarChartGroupData(
      x: x,
      barRods: [
        BarChartRodData(
          toY: y,
          width: width,
          borderRadius: BorderRadius.circular(32),
          color: barColor,
        ),
      ],
      showingTooltipIndicators: isTouched ? [0] : [],
    );
  }
}
