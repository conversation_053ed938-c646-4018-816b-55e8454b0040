import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';

class StatisticCard extends StatelessWidget {
  const StatisticCard({
    super.key,
    required this.child,
    required this.icon,
    this.iconBgColor,
    this.titleDescription,
    required this.title,
    required this.bgColor,
    this.enableBlur = false,
  });

  final String icon;
  final Color? iconBgColor;
  final String title;
  final List<Color> bgColor;
  final Widget child;
  final Widget? titleDescription;
  final bool enableBlur;

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        child: SizedBox(
          width: double.infinity,
          child: ClipPath(
            clipper: StatisticCardClipper(),
            child: DecoratedBox(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                      colors: bgColor,
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter)),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (Global.isPremium == false && enableBlur)
                    Blur(
                      borderRadius: BorderRadius.circular(16),
                      colorOpacity:
                          Global.isPremium == false && enableBlur ? 0.5 : 0,
                      blur: Global.isPremium == false && enableBlur ? 7.86 : 0,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 12),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width: 40,
                                  height: 40,
                                  child: DecoratedBox(
                                    decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: iconBgColor ?? Colors.white),
                                    child: Center(
                                        child: Image.asset(icon, width: 22)),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        title,
                                        style: AppTextStyles.xLargeMedium,
                                      ),
                                      if (titleDescription != null)
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 4),
                                          child: titleDescription!,
                                        ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                            child
                          ],
                        ),
                      ),
                    )
                  else
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 12),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              SizedBox(
                                width: 40,
                                height: 40,
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: iconBgColor ?? Colors.white),
                                  child: Center(
                                      child: Image.asset(icon, width: 22)),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      title,
                                      style: AppTextStyles.xLargeMedium,
                                    ),
                                    if (titleDescription != null)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: titleDescription!,
                                      ),
                                  ],
                                ),
                              )
                            ],
                          ),
                          child
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ));
  }
}

class StatisticCardClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final Path path = Path();
    path.moveTo(0, size.height);
    path.lineTo(0, 15);
    path.quadraticBezierTo(0, 0, 15, 0);
    path.lineTo(size.width / 2 - 60, 0);
    path.cubicTo(
      size.width / 2 - 60 + 12,
      0,
      size.width / 2 - 60 + 12,
      8,
      size.width / 2 - 60 + 22,
      8,
    );
    path.lineTo((size.width / 2 + 60 - 22), 8);
    path.cubicTo(
      size.width / 2 + 60 - 12,
      8,
      size.width / 2 + 60 - 12,
      0,
      size.width / 2 + 60,
      0,
    );
    path.lineTo(size.width - 15, 0);
    path.quadraticBezierTo(size.width, 0, size.width, 15);
    path.lineTo(size.width, size.height - 15);
    path.quadraticBezierTo(
      size.width,
      size.height,
      size.width - 15,
      size.height,
    );
    path.lineTo(15, size.height);
    path.quadraticBezierTo(
      0,
      size.height,
      0,
      size.height - 15,
    );
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}
