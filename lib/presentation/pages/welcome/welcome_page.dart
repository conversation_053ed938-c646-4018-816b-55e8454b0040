import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scroll_wheel_date_picker/scroll_wheel_date_picker.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/date_picker.dart';
import 'package:scrumpass_exam_simulator/app/widgets/idle_detector.dart';
import 'package:scrumpass_exam_simulator/app/widgets/textfield/custom_text_field.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/welcome/welcome_controler.dart';

class WelcomePage extends GetView<WelcomeControler> {
  const WelcomePage();
  @override
  Widget build(BuildContext context) {
    return IdleDetectorWidget(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(new FocusNode());
          },
          child: Container(
            height: MediaQuery.sizeOf(context).height,
            width: MediaQuery.sizeOf(context).width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppColors.onbroadPrimaryBg,
                  AppColors.onbroadSecondaryBg,
                ],
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 0,
                  left: 0,
                  child: Assets.images.onboardBg.image(),
                ),
                Positioned(
                  top: 56,
                  left: 16,
                  child: InkWell(
                    onTap: () {
                      controller.dismissKeyboard();
                      Get.back();
                    },
                    child: Assets.images.arrowLeft.svg(),
                  ),
                ),
                Positioned(
                  top: 95,
                  left: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.welcomeTo.tr,
                        style: AppTextStyles.xxxLargeThin
                            .copyWith(color: AppColors.white),
                      ),
                      Container(
                        width: MediaQuery.sizeOf(context).width,
                        child: Text(
                          Constants.appName,
                          style: AppTextStyles.xxxLargeSemiBold
                              .copyWith(color: AppColors.white),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      padding: EdgeInsets.only(bottom: 16),
                      width: Get.width,
                      decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          )),
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      "✏️  ${LocaleKeys.nameInputLabel.tr}",
                                      style: AppTextStyles.largeBold,
                                    ),
                                  ],
                                ),
                                SizedBox(height: 16),
                                CustomTextField(
                                  controller: controller.username,
                                  autoFocus: true,
                                  hint: LocaleKeys.writeNameHere.tr,
                                ),
                                SizedBox(height: 32),
                                Text(
                                  "🗓️  ${LocaleKeys.dateInputLabel.tr}",
                                  style: AppTextStyles.largeBold,
                                ),
                                SizedBox(height: 16),
                                CustomTextField(
                                  controller: controller.examDate,
                                  focusNode: controller.examDateFocusNode,
                                  maxLine: 1,
                                  ontap: () {},
                                  readOnly: true,
                                  suffixIcon: Icon(
                                    Icons.calendar_today_rounded,
                                    size: 20,
                                    color: AppColors.textHint,
                                  ),
                                ),
                                SizedBox(height: 32),
                                GetBuilder<WelcomeControler>(
                                  id: WellComeConstants.usernameKey,
                                  builder: (_) {
                                    return PrimaryButton(
                                      text: LocaleKeys.confirm.tr,
                                      isEnable:
                                          controller.username.text.isNotEmpty,
                                      onTap: () async {
                                        await controller.saveUserData();
                                      },
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                          Obx(
                            () {
                              if (controller.isExamDateFocused.value) {
                                controller.animationController.forward();
                              } else {
                                controller.animationController.reverse();
                              }

                              return AnimatedSize(
                                duration: Duration(milliseconds: 500),
                                curve: Curves.fastLinearToSlowEaseIn,
                                child: SlideTransition(
                                  position: Tween<Offset>(
                                    begin: Offset(0, 1),
                                    end: Offset(0, 0),
                                  ).animate(
                                    CurvedAnimation(
                                      parent: controller.animationController,
                                      curve: Curves.easeInOut,
                                    ),
                                  ),
                                  child: controller.isExamDateFocused.value
                                      ? Container(
                                          height: 280,
                                          margin: EdgeInsets.only(
                                              top: 16, bottom: 16),
                                          child: DatePicker(
                                            initDate: controller.examDateValue,
                                            onDateChanged: (value) {
                                              controller.examDateChange(value);
                                            },
                                          ))
                                      : SizedBox(),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
