import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/widgets/scroll_to_top.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_question_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/review/widgets/question_tab_content.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/review/widgets/review_header.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/review/widgets/test_tab_content.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/sticky_header.dart';

class ReviewPage extends StatelessWidget {
  const ReviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        final ReviewController controller = Get.put(ReviewController());
        controller.getResultData();
        final _ = controller.watchTab.value;
        return Scaffold(
          extendBody: true,
          extendBodyBehindAppBar: true,
          floatingActionButton: Obx(() => AnimatedOpacity(
                opacity: controller.showScrollToTop.value ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 500),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 68),
                  child: ScrollToTop(
                    onTap: controller.scrollToTop,
                  ),
                ),
              )),
          body: Obx(
            () => controller.isLoading.value
                ? Column(
                    children: [
                      ReviewHeader(),
                      Obx(
                        () => controller.currentTab.value == ReviewTab.test
                            ? TestTabContent()
                            : Container(
                                height: Get.height,
                                child: QuestionTabContent(),
                              ),
                      ),
                    ],
                  )
                : Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          colors: AppColors.reviewHeaderGradient,
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter),
                    ),
                    child: NestedScrollView(
                      physics: NeverScrollableScrollPhysics(),
                      controller: controller.currentTab.value == ReviewTab.test
                          ? null
                          : controller.scrollController,
                      headerSliverBuilder: (context, _) => [
                        SliverToBoxAdapter(child: ReviewHeader()),
                        if (controller.currentTab.value == ReviewTab.question)
                          Obx(
                            () => SliverPersistentHeader(
                              pinned: true,
                              key: ValueKey(controller.showScrollToTop.value),
                              delegate: StickyWidgetDelegate(
                                height:
                                    controller.showScrollToTop.value ? 90 : 50,
                                child: GetBuilder<ReviewQuestionController>(
                                  init: ReviewQuestionController(
                                    results: controller.doneResults,
                                    listQuestions: controller.questions,
                                  ),
                                  builder: (questionController) {
                                    return QuestionTabbar(
                                        questionController: questionController);
                                  },
                                ),
                              ),
                            ),
                          ),
                      ],
                      body: Container(
                        color: AppColors.white,
                        child: controller.currentTab.value == ReviewTab.test
                            ? controller.results.isEmpty
                                ? TestTabContent()
                                : Column(
                                    children: [
                                      Expanded(
                                        child: SingleChildScrollView(
                                          key: PageStorageKey('testTabScroll'),
                                          controller:
                                              controller.scrollController,
                                          child: Container(
                                            child: Column(
                                              children: [
                                                TestTabContent(),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 100),
                                    ],
                                  )
                            : Container(
                                height: Get.height,
                                child: QuestionTabContent(),
                              ),
                      ),
                    ),
                  ),
          ),
        );
      },
    );
  }
}
