import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_question_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/filter_content.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/result_content.dart';

class QuestionTabContent extends GetView<ReviewController> {
  const QuestionTabContent({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReviewQuestionController>(
        init: ReviewQuestionController(
          results: controller.doneResults,
          listQuestions: controller.questions,
        ),
        builder: (questionController) {
          return QuestionTabbarView(questionController: questionController);
        });
  }
}

class QuestionTabbarView extends StatelessWidget {
  const QuestionTabbarView({
    super.key,
    required this.questionController,
  });

  final ReviewQuestionController questionController;

  @override
  Widget build(BuildContext context) {
    return TabBarView(
      controller: questionController.tabController,
      children: questionController.filters.map((e) {
        switch (e) {
          case ResultFilter.all:
            return FilterContent(
              questions: questionController.questions,
              addBottomSpace: true,
              useObjectIndex: false,
              type: e,
              isInExam: false,
            );
          case ResultFilter.correct:
            return FilterContent(
              questions: questionController.corrects,
              addBottomSpace: true,
              useObjectIndex: false,
              type: e,
              isInExam: false,
            );
          case ResultFilter.incorrect:
            return FilterContent(
              questions: questionController.incorrects,
              addBottomSpace: true,
              useObjectIndex: false,
              type: e,
              isInExam: false,
            );
          case ResultFilter.bookmarked:
            return FilterContent(
              questions: questionController.bookmarked,
              addBottomSpace: true,
              useObjectIndex: false,
              type: e,
              isInExam: false,
            );
          case ResultFilter.notDone:
            return FilterContent(
              questions: questionController.unanswered,
              addBottomSpace: true,
              useObjectIndex: false,
              type: e,
              isInExam: false,
            );
        }
      }).toList(),
    );
  }
}

class QuestionTabbar extends StatelessWidget {
  const QuestionTabbar({
    super.key,
    required this.questionController,
  });

  final ReviewQuestionController questionController;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 4),
      child: TabBar(
        tabs: questionController.filters.map((e) {
          switch (e) {
            case ResultFilter.all:
              return Tab(
                  text:
                      '${e.reviewTabText} (${questionController.questions.length})',
                  height: 38);
            case ResultFilter.correct:
              return Tab(
                  text:
                      '${e.reviewTabText} (${questionController.corrects.length})',
                  height: 38);
            case ResultFilter.incorrect:
              return Tab(
                  text:
                      '${e.reviewTabText} (${questionController.incorrects.length})',
                  height: 38);
            case ResultFilter.bookmarked:
              return Tab(
                  text:
                      '${e.reviewTabText} (${questionController.bookmarked.length})',
                  height: 38);
            case ResultFilter.notDone:
              return Tab(
                  text:
                      '${e.reviewTabText} (${questionController.unanswered.length})',
                  height: 38);
          }
        }).toList(),
        controller: questionController.tabController,
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: EdgeInsets.zero,
        isScrollable: true,
        indicator: TabBarIndicator(color: AppColors.primary, radius: 0),
        labelColor: AppColors.primary,
        labelStyle: AppTextStyles.smallSemiBold,
        unselectedLabelStyle: AppTextStyles.smallMedium,
        unselectedLabelColor: AppColors.textHint,
        dividerColor: Colors.transparent,
        tabAlignment: TabAlignment.start,
        labelPadding: EdgeInsets.symmetric(horizontal: 12),
        splashFactory: NoSplash.splashFactory,
      ),
    );
  }
}
