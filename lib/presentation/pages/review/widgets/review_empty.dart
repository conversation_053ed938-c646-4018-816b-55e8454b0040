import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';

class ReviewEmptyScreen extends StatelessWidget {
  const ReviewEmptyScreen({
    super.key,
    required this.controller,
  });

  final ReviewController controller;

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Assets.images.emptyIcon.image(width: 241, height: 160),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            LocaleKeys.reviewNoResultText.tr,
            style:
                AppTextStyles.smallMedium.copyWith(color: AppColors.textHint),
          ),
        ),
        const SizedBox(height: 28),
        SizedBox(
            width: 210,
            child: PrimaryButton(
              text: LocaleKeys.reviewNoResultBtn.tr,
              onTap: controller.onTapStartTest,
            )),
        const SizedBox(height: 100),
      ],
    ));
  }
}
