import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/widgets/fake_tab_tour.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/product_tour_widget.dart';

class ReviewHeader extends StatelessWidget {
  const ReviewHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 160,
      child: DecoratedBox(
        decoration: BoxDecoration(
          gradient: LinearGradient(
              colors: AppColors.reviewHeaderGradient,
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter),
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned(
              top: 0,
              right: 0,
              child: Assets.images.reviewCorner.image(),
            ),
            Positioned(
                bottom: -6,
                right: 12,
                child: Assets.images.documentReviewHeader
                    .image(width: 238, height: 150)),
            Positioned(
                bottom: 0,
                left: 0,
                child: Assets.images.houseReviewHeader
                    .image(width: 165, height: 87)),
            ReviewTabbar(),
            Positioned(
              top: 65,
              left: 16,
              child: Text(
                LocaleKeys.reviewTab.tr,
                style: AppTextStyles.xLargeBold
                    .copyWith(color: AppColors.textInverseTitle),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ReviewTabbar extends GetView<ReviewController> {
  const ReviewTabbar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final TourController tourController = Get.find<TourController>();
    return Positioned(
      bottom: -1,
      child: SizedBox(
        height: 50,
        width: Get.width,
        child: Obx(() => tourController.isTourReviewActive.value
            ? fakeReviewTab(controller, tourController)
            : controller.currentTab.value == ReviewTab.test
                ? Stack(
                    children: [
                      Positioned(
                        right: 0,
                        bottom: 0,
                        child: InActiveTab(
                          icon: Assets.images.notebookBookmark.svg(),
                          text: LocaleKeys.reviewQuestionTab.tr,
                          onTap: () {
                            controller.onChangeTab(ReviewTab.question);
                          },
                        ),
                      ),
                      Positioned(
                        left: 0,
                        bottom: 0,
                        child: ActiveTab(
                          icon: Assets.images.diploma
                              .svg(color: AppColors.primary),
                          text: LocaleKeys.reviewTestsTab.tr,
                          onTap: () {
                            controller.onChangeTab(ReviewTab.test);
                          },
                        ),
                      )
                    ],
                  )
                : Stack(
                    children: [
                      Positioned(
                        left: 0,
                        bottom: 0,
                        child: InActiveTab(
                          icon: Assets.images.diplomaGrey.svg(),
                          text: LocaleKeys.reviewTestsTab.tr,
                          isRight: false,
                          onTap: () {
                            controller.onChangeTab(ReviewTab.test);
                          },
                        ),
                      ),
                      Positioned(
                        right: 0,
                        bottom: 0,
                        child: ActiveTab(
                          icon: Assets.images.notebookBookmark
                              .svg(color: AppColors.primary),
                          text: LocaleKeys.reviewQuestionTab.tr,
                          onTap: () {
                            controller.onChangeTab(ReviewTab.question);
                          },
                        ),
                      ),
                    ],
                  )),
      ),
    );
  }
}

class ActiveTab extends StatelessWidget {
  const ActiveTab(
      {super.key, required this.text, required this.icon, required this.onTap});

  final String text;
  final SvgPicture icon;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: onTap,
      child: SizedBox(
        width: Get.width * 0.552,
        height: 44,
        child: DecoratedBox(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30)),
                color: Colors.white),
            child: Center(
                child: Row(mainAxisSize: MainAxisSize.min, children: [
              icon,
              const SizedBox(width: 8),
              Text(
                text,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style:
                    AppTextStyles.baseBold.copyWith(color: AppColors.primary),
              )
            ]))),
      ),
    );
  }
}

class InActiveTab extends StatelessWidget {
  const InActiveTab(
      {super.key,
      required this.text,
      required this.icon,
      this.isRight = true,
      required this.onTap});

  final String text;
  final SvgPicture icon;
  final bool isRight;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: onTap,
      child: SizedBox(
        width: Get.width * 0.53,
        height: 40,
        child: DecoratedBox(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30)),
                color: AppColors.btn2ndDefault),
            child: Center(
                child: Padding(
              padding: EdgeInsets.only(
                  top: 8, left: isRight ? 30 : 0, right: isRight ? 0 : 30),
              child: Row(mainAxisSize: MainAxisSize.min, children: [
                icon,
                const SizedBox(width: 8),
                Text(
                  text,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.smallRegular
                      .copyWith(color: AppColors.textHint),
                )
              ]),
            ))),
      ),
    );
  }
}
