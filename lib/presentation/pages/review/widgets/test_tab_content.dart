import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/app/extensions/int.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/other/exam_params.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/review/widgets/review_empty.dart';

class TestTabContent extends GetView<ReviewController> {
  const TestTabContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => controller.isLoading.value
        ? const SizedBox()
        : controller.results.isEmpty
            ? ReviewEmptyScreen(controller: controller)
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: _buildList(),
              ));
  }

  List<Widget> _buildList() {
    List<Widget> list = [];
    controller.resultsByDay.forEach((key, value) {
      list.add(DateHeader(date: key));
      final listItem = ListView.separated(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) => ResultItem(
                result: value[index],
              ),
          separatorBuilder: (_, __) => Divider(
                color: AppColors.borderLine,
                height: 1,
                thickness: 1,
              ),
          itemCount: value.length);
      list.add(listItem);
    });
    return list;
  }
}

class DateHeader extends StatelessWidget {
  const DateHeader({super.key, required this.date});

  final String date;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(color: AppColors.neutral1),
      child: SizedBox(
        width: double.infinity,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            date,
            style: AppTextStyles.smallSemiBold
                .copyWith(color: AppColors.textLabel),
          ),
        ),
      ),
    );
  }
}

class ResultItem extends StatelessWidget {
  const ResultItem({super.key, required this.result});

  final ResultEntity result;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (Get.find<TourController>().finishTourReview.value == false) return;
        if (result.status == ResultStatus.done) {
          Get.toNamed(RouterName.resultDetail,
              arguments: result, parameters: {'showBackBtn': true.toString()});
        } else {
          Get.toNamed(RouterName.doTest,
              arguments: ExamParams(
                  numQuestion: result.questions.length,
                  testType: result.type,
                  testName: result.testName,
                  result: result));
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            SizedBox(
              width: 48,
              height: 48,
              child: DecoratedBox(
                decoration: BoxDecoration(
                    color: result.type.bgColor,
                    borderRadius: BorderRadius.circular(6)),
                child: Center(
                  child: SvgPicture.asset(result.type.icon),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                          child: Text(
                        Utils.parseTestPracticeName(result.testName),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.smallSemiBold
                            .copyWith(color: AppColors.text1st),
                      )),
                      if (result.status == ResultStatus.notDone)
                        DecoratedBox(
                          decoration: BoxDecoration(
                              color: AppColors.bgWarningSurface,
                              borderRadius: BorderRadius.circular(12)),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            child: Text(
                              LocaleKeys.inProgress.tr,
                              style: AppTextStyles.xSmallMedium
                                  .copyWith(color: AppColors.textWarning),
                            ),
                          ),
                        ),
                      if (result.pass != null)
                        Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                                color: result.pass!
                                    ? AppColors.bgSuccessSurface
                                    : AppColors.bgErrorSurface,
                                borderRadius: BorderRadius.circular(12)),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              child: Text(
                                result.pass!
                                    ? LocaleKeys.passed.tr
                                    : LocaleKeys.failed.tr,
                                style: AppTextStyles.xSmallMedium.copyWith(
                                    color: result.pass!
                                        ? AppColors.textSuccess
                                        : AppColors.textError),
                              ),
                            ),
                          ),
                        )
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Assets.images.time.svg(),
                      const SizedBox(width: 2),
                      Text(
                        result.timeDoTest.toTimeTextString,
                        style: AppTextStyles.xSmallRegular
                            .copyWith(color: AppColors.textHint),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Text(
                          '|',
                          style: AppTextStyles.xxSmallRegular
                              .copyWith(color: AppColors.textHint),
                        ),
                      ),
                      Assets.images.notebook.svg(),
                      const SizedBox(width: 4),
                      Text(
                        result.status == ResultStatus.done
                            ? '${LocaleKeys.overall.tr}: ${result.score.roundedPrecisionToString(0)}%'
                            : '${LocaleKeys.overall.tr}: --',
                        style: AppTextStyles.xSmallRegular
                            .copyWith(color: AppColors.textHint),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
