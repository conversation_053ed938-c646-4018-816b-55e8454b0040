import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/int.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/secondary_button.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_mock_test.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/exam_appbar.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/mock_test_progress_bar.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/question_box.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/test_progress_bar.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ExamPage extends GetView<ExamController> {
  const ExamPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.examBg,
      appBar: ExamAppbar(),
      body: SafeArea(bottom: false, child: ExamContent(controller: controller)),
    );
  }
}

class ExamContent extends StatelessWidget {
  const ExamContent({
    super.key,
    required this.controller,
  });

  final ExamController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() => Skeletonizer(
          enabled: controller.isLoading.value,
          child: Column(
            children: [
              Expanded(
                  child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TimeAnswered(
                        isMockTest: controller.testType == TestType.fullTest),
                    SizedBox(
                        height:
                            controller.testType == TestType.fullTest ? 8 : 17),
                    Skeleton.replace(
                      replacement: Bone.button(
                        width: double.infinity,
                        height: 18,
                      ),
                      child: GetBuilder<ExamController>(
                          id: ExamConstants.progressBar,
                          builder: (_) => Padding(
                                padding: controller.testType ==
                                        TestType.fullTest
                                    ? EdgeInsets.zero
                                    : const EdgeInsets.symmetric(horizontal: 9),
                                child: controller.testType == TestType.fullTest
                                    ? MockTestProgressBar(
                                        value: controller.remainingProgress,
                                      )
                                    : PracticeTestProgressBar(
                                        value: controller.doneQuestions,
                                        maxValue: controller.numQuestion),
                              )),
                    ),
                    SizedBox(
                        height:
                            controller.testType == TestType.fullTest ? 16 : 23),
                    Flexible(child: QuestionBox()),
                  ],
                ),
              )),
              DecoratedBox(
                decoration: BoxDecoration(
                  color: AppColors.examFooterBg,
                ),
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: 16, top: 16, right: 16, bottom: 50),
                  child: Row(
                    children: [
                      Flexible(
                        child: SecondaryButton(
                          text: LocaleKeys.previous.tr,
                          onTap: () {
                            controller.onPrevQuestion();
                          },
                        ),
                        flex: 1,
                      ),
                      const SizedBox(width: 16),
                      Flexible(
                        child: GetBuilder<ExamController>(
                          id: ExamConstants.nextBtn,
                          builder: (_) {
                            return PrimaryButton(
                                text: controller.lastQuestionSubmit
                                    ? LocaleKeys.submit.tr
                                    : LocaleKeys.next.tr,
                                onTap: () {
                                  controller.onNextQuestion();
                                });
                          },
                        ),
                        flex: 1,
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ));
  }
}

class TimeAnswered extends GetView<ExamController> {
  const TimeAnswered({
    super.key,
    this.isMockTest = false,
  });

  final bool isMockTest;

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      Skeleton.replace(
          replacement: Bone.circle(size: 20),
          child: Assets.images.clockCircle.svg()),
      const SizedBox(width: 4),
      Expanded(
        child: Obx(() => Text.rich(TextSpan(children: [
              TextSpan(
                text:
                    '${isMockTest ? LocaleKeys.remaining.tr : LocaleKeys.time.tr}: ',
                style: AppTextStyles.smallRegular
                    .copyWith(color: AppColors.timeRowText),
              ),
              TextSpan(
                text: controller.getTime,
                style: AppTextStyles.smallSemiBold,
              ),
            ]))),
      ),
      GetBuilder<ExamController>(
        id: ExamConstants.answeredHeader,
        builder: (_) {
          return Text.rich(TextSpan(children: [
            TextSpan(
              text: '${LocaleKeys.answered.tr}: ',
              style: AppTextStyles.smallRegular
                  .copyWith(color: AppColors.timeRowText),
            ),
            TextSpan(
              text: '${controller.doneQuestions}/${controller.numQuestion}',
              style: AppTextStyles.smallSemiBold,
            ),
          ]));
        },
      )
    ]);
  }
}
