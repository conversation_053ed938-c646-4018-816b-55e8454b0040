import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/widgets/radio.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/explanation_box.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/question_box.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SingleSelectOptionsWidget extends GetView<ExamController> {
  const SingleSelectOptionsWidget(
      {super.key, required this.question, required this.isInExam});

  final QuestionEntity question;
  final bool isInExam;
  @override
  Widget build(BuildContext context) {
    final options = question.options ?? [];
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListView.separated(
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) => GestureDetector(
                    onTap: () {
                      controller.onSelectOption(options[index]);
                    },
                    child: GetBuilder<ExamController>(
                      id: ExamConstants.optionList,
                      builder: (_) {
                        return AbsorbPointer(
                          child: SingleOption(
                            status: question.isShowResult &&
                                    controller.trainingMode != TrainingMode.exam
                                ? options[index].resultStatus
                                : options[index].status,
                            option: options[index],
                            index: index,
                            isInExam: isInExam,
                          ),
                        );
                      },
                    ),
                  ),
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemCount: options.length),
          ExplanationBox(
            text: question.description,
            isInExam: isInExam,
          )
        ],
      ),
    );
  }
}

class SingleOption extends StatelessWidget {
  SingleOption(
      {super.key,
      required this.status,
      required this.option,
      required this.index,
      required this.isInExam});

  final OptionStatus status;
  final OptionEntity option;
  final int index;
  final bool isInExam;

  @override
  Widget build(BuildContext context) {
    switch (status) {
      case OptionStatus.correct:
        return DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.green1,
              border: Border.all(color: AppColors.green5, width: 1),
              borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top: isInExam ? SettingController.getMarginIcon() : 0),
                  child: Assets.images.radioboxGreen.svg(width: 24),
                ),
                const SizedBox(width: 8),
                OptionWidget(
                  textColor: AppColors.green5,
                  index: index,
                  text: option.qOption ?? '',
                  isInExam: isInExam,
                )
              ],
            ),
          ),
        );

      case OptionStatus.notSelectCorrect:
        return DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.green1,
              border: Border.all(color: AppColors.green5, width: 1),
              borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top:
                          isInExam ? SettingController.getMarginIcon() : 0 + 1),
                  child: SizedBox(
                    width: 18,
                    height: 18,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: AppColors.icSuccess)),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                OptionWidget(
                  textColor: AppColors.green5,
                  index: index,
                  text: option.qOption ?? '',
                  isInExam: isInExam,
                )
              ],
            ),
          ),
        );

      case OptionStatus.wrong:
        return DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.red1,
              border: Border.all(color: AppColors.red3, width: 1),
              borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top: isInExam ? SettingController.getMarginIcon() : 0),
                  child: Assets.images.radioboxRed.svg(width: 24),
                ),
                const SizedBox(width: 8),
                OptionWidget(
                  textColor: AppColors.red5,
                  index: index,
                  text: option.qOption ?? '',
                  includeMargin: true,
                  isInExam: isInExam,
                )
              ],
            ),
          ),
        );

      case OptionStatus.selected:
        return DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.baseBlue1,
              border: Border.all(color: AppColors.baseBlue3, width: 1),
              borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top: isInExam ? SettingController.getMarginIcon() : 0),
                  child: AnimatedRadioButton(
                      isSelected: true, onChanged: (value) {}),
                ),
                const SizedBox(width: 8),
                OptionWidget(
                  textColor: AppColors.baseBlueBorder,
                  index: index,
                  text: option.qOption ?? '',
                  isInExam: isInExam,
                )
              ],
            ),
          ),
        );

      default:
        return Padding(
          padding: const EdgeInsets.all(6),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(
                    top: isInExam ? SettingController.getMarginIcon() : 0),
                child: Skeleton.replace(
                    replacement: Bone.circle(size: 20),
                    child: AnimatedRadioButton(
                        isSelected: false, onChanged: (value) {})),
              ),
              const SizedBox(width: 8),
              OptionWidget(
                index: index,
                text: option.qOption ?? '',
                isInExam: isInExam,
              )
            ],
          ),
        );
    }
  }
}
