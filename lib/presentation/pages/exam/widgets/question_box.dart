import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/image_viewer_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/multi_option.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/single_option.dart';
import 'package:skeletonizer/skeletonizer.dart';

class QuestionBox extends GetView<ExamController> {
  const QuestionBox({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => DecoratedBox(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: AppColors.border1st)),
          child: SizedBox(
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _Header(controller: controller),
                Flexible(
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    controller: controller.questionScrollController,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 12),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Html(
                            shrinkWrap: true,
                            data:
                                '${controller.currentQuestion.getQuestionText()}',
                            style: {
                              "body": Style(
                                  fontSize: FontSize(Global.getExamFontSize),
                                  fontWeight: FontWeight.w700,
                                  fontFamily: 'Inter',
                                  color: AppColors.examQuestion,
                                  margin: Margins.zero,
                                  padding: HtmlPaddings.zero,
                                  lineHeight: const LineHeight(1.5)),
                              "p": Style(
                                margin: Margins.zero,
                                padding: HtmlPaddings.zero,
                              ),
                              "img": Style(
                                width: Width(
                                    MediaQuery.of(context).size.width - 50,
                                    Unit.px),
                              )
                            },
                            extensions: [
                              OnImageTapExtension(
                                onImageTap: (src, imgAttributes, element) {
                                  if (src != null && context.mounted) {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (_) =>
                                            ImageViewerPage(src: src),
                                      ),
                                    );
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        Divider(
                          height: 1,
                          color: AppColors.examDivider,
                          thickness: 1,
                          indent: 12,
                          endIndent: 12,
                        ),
                        const SizedBox(height: 16),
                        if (controller.currentQuestion.type ==
                            QuestionType.single)
                          SingleSelectOptionsWidget(
                            question: controller.currentQuestion,
                            isInExam: true,
                          )
                        else
                          MultiSelectOptionsWidget(
                            question: controller.currentQuestion,
                            isInExam: true,
                          ),
                        const SizedBox(height: 14),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}

class _Header extends StatelessWidget {
  const _Header({
    required this.controller,
  });

  final ExamController controller;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
          border: Border(
              left: BorderSide(color: AppColors.border1st),
              top: BorderSide(color: AppColors.border1st),
              right: BorderSide(color: AppColors.border1st)),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(4), topRight: Radius.circular(4)),
          color: AppColors.examBoxHeader),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Expanded(
              child: Text(
                LocaleKeys.questionIndex.tr
                    .replaceAll(
                        '\$1', (controller.questionIndex.value + 1).toString())
                    .replaceAll('\$2', controller.numQuestion.toString())
                    .toUpperCase(),
                style: AppTextStyles.xSmallSemiBold
                    .copyWith(color: AppColors.examBoxHeaderTitle),
              ),
            ),
            GetBuilder<ExamController>(
              id: ExamConstants.bookmark,
              builder: (_) {
                return Skeleton.replace(
                  replacement: Icon(
                    Icons.bookmark,
                    size: 20,
                  ),
                  child: GestureDetector(
                    child: controller.currentQuestion.isBookmark
                        ? Assets.images.bookmarked.svg()
                        : Assets.images.bookmark.svg(),
                    onTap: controller.onBookmark,
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}

class OptionWidget extends StatelessWidget {
  OptionWidget({
    super.key,
    this.textColor,
    required this.index,
    required this.text,
    this.includeMargin = false,
    required this.isInExam,
  });
  final Color? textColor;
  final int index;
  final String text;
  final bool includeMargin;
  final bool isInExam;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(top: includeMargin ? 1.0 : 0),
        child: RichText(
          text: TextSpan(
            text: Constants.examOptionAlphabet[index],
            style: AppTextStyles.smallBold.copyWith(
              color: textColor ?? AppColors.examOptionAlphabet,
              fontSize: isInExam ? (Global.getExamFontSize - 2) : 14,
            ),
            children: [
              TextSpan(
                text: Utils.parseHtmlString(text.replaceAll('\n', ' ')),
                style: AppTextStyles.smallMedium.copyWith(
                  height: 20 / 14,
                  color: textColor ?? AppColors.examOption,
                  fontSize: isInExam ? (Global.getExamFontSize - 2) : 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
