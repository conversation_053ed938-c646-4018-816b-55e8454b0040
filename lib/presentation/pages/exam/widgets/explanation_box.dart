import 'package:blur/blur.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/image_viewer_page.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/premium_button.dart';

class ExplanationBox extends GetView<ExamController> {
  const ExplanationBox({super.key, this.text, this.isInExam = false});

  final String? text;
  final bool isInExam;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ExamController>(
      id: ExamConstants.explanationBox,
      builder: (_) {
        if (controller.trainingMode == TrainingMode.practice &&
            controller.currentQuestion.status == QuestionStatus.answered) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 20, top: 14),
            child: Stack(alignment: Alignment.center, children: [
              DottedBorder(
                borderType: BorderType.RRect,
                radius: Radius.circular(4),
                padding: EdgeInsets.symmetric(horizontal: 1, vertical: 1),
                color: AppColors.baseBlue2,
                dashPattern: [8, 5],
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: AppColors.bgInfoSurface,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Assets.images.lightbulbMinimalistic.svg(
                                height: isInExam
                                    ? (Global.getExamFontSize + 6)
                                    : 24),
                            const SizedBox(width: 4),
                            Padding(
                              padding: EdgeInsets.only(top: 3),
                              child: Text(
                                LocaleKeys.detailExplanation.tr,
                                style: AppTextStyles.smallBold.copyWith(
                                    color: AppColors.elementTextNormal,
                                    fontSize: isInExam
                                        ? (Global.getExamFontSize - 2)
                                        : 14),
                              ),
                            )
                          ],
                        ),
                        Global.isPremium == false
                            ? Container(
                                height: 120,
                                margin: EdgeInsets.only(top: 6),
                                child: Container(
                                  width: double.infinity,
                                  height: double.infinity,
                                  color: AppColors.baseBlue1,
                                ),
                              )
                            : Container(
                                margin: EdgeInsets.only(top: 6),
                                child: Blur(
                                  colorOpacity:
                                      Global.isPremium == false ? 0.5 : 0,
                                  blur: Global.isPremium == false ? 7.86 : 0,
                                  blurColor: AppColors.bgInfoSurface,
                                  child: Html(
                                    data: text,
                                    style: {
                                      "body": Style(
                                          fontSize: FontSize(isInExam
                                              ? (Global.getExamFontSize - 2)
                                              : 14),
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter',
                                          color: AppColors.textHint,
                                          margin: Margins.zero,
                                          padding:
                                              HtmlPaddings.only(bottom: 12),
                                          lineHeight:
                                              const LineHeight(20 / 14)),
                                      "p": Style(
                                        margin: Margins.zero,
                                        padding: HtmlPaddings.zero,
                                      ),
                                      "img": Style(
                                          width: Width(
                                              MediaQuery.of(context)
                                                      .size
                                                      .width -
                                                  50,
                                              Unit.px))
                                    },
                                    extensions: [
                                      OnImageTapExtension(
                                        onImageTap:
                                            (src, imgAttributes, element) {
                                          if (src != null && context.mounted) {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (_) =>
                                                    ImageViewerPage(src: src),
                                              ),
                                            );
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              )
                      ],
                    ),
                  ),
                ),
              ),
              if (Global.isPremium == false) PremiumButton()
            ]),
          );
        }

        return const SizedBox();
      },
    );
  }
}
