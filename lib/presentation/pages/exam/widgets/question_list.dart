import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';

class QuestionList extends GetView<ExamController> {
  const QuestionList({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Indicator(),
          const SizedBox(height: 16),
          Container(
            constraints: BoxConstraints(maxHeight: Get.height * 0.65),
            child: Question<PERSON>rapper(controller: controller),
          ),
          const SizedBox(height: 32),
          PrimaryButton(
            text: LocaleKeys.submitExam.tr,
            onTap: () {
              controller.onSubmitTest();
            },
          )
        ],
      ),
    );
  }
}

class Indicator extends StatelessWidget {
  const Indicator({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IndicatorItem(
          title: LocaleKeys.doing.tr,
          color: AppColors.baseBlue1,
          border: AppColors.baseBlueBorder,
        ),
        IndicatorItem(
            title: LocaleKeys.bookmark.tr,
            color: AppColors.yellow3,
            border: AppColors.yellow3),
        IndicatorItem(
          title: LocaleKeys.done.tr,
          color: AppColors.bgSuccess,
          border: AppColors.bgSuccess,
        ),
        IndicatorItem(
            title: LocaleKeys.notDone.tr,
            color: Colors.white,
            border: AppColors.border1st)
      ],
    );
  }
}

class IndicatorItem extends StatelessWidget {
  const IndicatorItem(
      {super.key,
      required this.title,
      required this.color,
      required this.border});

  final String title;
  final Color color;
  final Color border;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: DecoratedBox(
            decoration: BoxDecoration(
                color: color,
                border: Border.all(color: border),
                borderRadius: BorderRadius.circular(4)),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          title,
          style: AppTextStyles.xSmallMedium
              .copyWith(color: AppColors.elementTextNormal),
        )
      ],
    );
  }
}

class QuestionWrapper extends StatelessWidget {
  const QuestionWrapper({super.key, required this.controller});

  final ExamController controller;

  @override
  Widget build(BuildContext context) {
    List<Widget> list = [];
    final questions = controller.questions;
    for (var i = 0; i < questions.length; i++) {
      list.add(GestureDetector(
          onTap: () {
            controller.onChangeQuestion(i);
          },
          child: QuestionItem(
              index: i + 1, status: questions[i].maptoListStatus())));
    }
    return SingleChildScrollView(
      child: Wrap(
        spacing: 10,
        runSpacing: 16,
        children: list,
      ),
    );
  }
}

class QuestionItem extends StatelessWidget {
  const QuestionItem({super.key, required this.index, required this.status});

  final int index;
  final QuestionListStatus status;

  @override
  Widget build(BuildContext context) {
    DecoratedBox box;
    switch (status) {
      case QuestionListStatus.doing:
        box = DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.baseBlue1,
              border: Border.all(color: AppColors.baseBlueBorder, width: 2),
              borderRadius: BorderRadius.circular(10)),
          child: Center(
            child: Text(
              index.toString(),
              style: AppTextStyles.smallMedium
                  .copyWith(color: AppColors.baseBlueBorder),
            ),
          ),
        );
        break;
      case QuestionListStatus.bookmarked:
        box = DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.yellow3,
              border: Border.all(color: AppColors.yellow3),
              borderRadius: BorderRadius.circular(10)),
          child: Center(
            child: Text(
              index.toString(),
              style: AppTextStyles.smallMedium
                  .copyWith(color: AppColors.questionListIndex),
            ),
          ),
        );
        break;
      case QuestionListStatus.done:
        box = DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.bgSuccess,
              border: Border.all(color: AppColors.bgSuccess),
              borderRadius: BorderRadius.circular(10)),
          child: Center(
            child: Text(
              index.toString(),
              style: AppTextStyles.smallMedium
                  .copyWith(color: AppColors.textBtn1st),
            ),
          ),
        );
        break;
      case QuestionListStatus.notDone:
        box = DecoratedBox(
          decoration: BoxDecoration(
              border: Border.all(color: AppColors.border1st),
              borderRadius: BorderRadius.circular(10)),
          child: Center(
            child: Text(
              index.toString(),
              style: AppTextStyles.smallMedium
                  .copyWith(color: AppColors.questionListIndex),
            ),
          ),
        );
        break;
      default:
        box = DecoratedBox(
          decoration: BoxDecoration(),
        );
    }
    return GestureDetector(
      child: SizedBox(
        width: 39,
        height: 36,
        child: box,
      ),
    );
  }
}
