import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';

class PracticeTestProgressBar extends StatefulWidget {
  final int value;
  final int maxValue;

  const PracticeTestProgressBar({
    Key? key,
    required this.value,
    required this.maxValue,
  }) : super(key: key);

  @override
  _PracticeTestProgressBarState createState() =>
      _PracticeTestProgressBarState();
}

class _PracticeTestProgressBarState extends State<PracticeTestProgressBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = Tween<double>(begin: 0, end: widget.value / widget.maxValue)
        .animate(_controller);
    _controller.forward();
  }

  @override
  void didUpdateWidget(covariant PracticeTestProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    _controller.reset();
    _animation = Tween<double>(
            begin: oldWidget.value / oldWidget.maxValue,
            end: widget.value / widget.maxValue)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Stack(
          children: [
            // ... các phần còn lại của thanh tiến trình
            LinearProgressIndicator(
              minHeight: 4,
              value: _animation.value,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.green2),
              backgroundColor: AppColors.bgMain01,
            ),
            // Vẽ các mốc
            CustomPaint(
              size: const Size(double.infinity, 4),
              painter: ProgressPainter(
                  value: widget.value, maxValue: widget.maxValue),
            ),
          ],
        );
      },
    );
  }
}

class ProgressPainter extends CustomPainter {
  final int value;
  final int maxValue;

  ProgressPainter({required this.value, required this.maxValue});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.bgMain01
      ..strokeWidth = 18.0;

    // Vẽ các mốc chia hết cho 5
    for (int i = 0; i <= maxValue; i += 5) {
      final x = (i / maxValue) * size.width;
      paint.strokeWidth = 18.0;
      canvas.drawCircle(Offset(x, 2), 9, paint);

      textPaint(AppColors.neutral5, x, i == 0 ? "1" : i.toString(), canvas);
    }

    // Vẽ các mốc chia hết cho 5 đã qua
    for (int i = 0; i <= maxValue; i += 5) {
      if (i <= value) {
        final x = (i / maxValue) * size.width;
        final color = AppColors.green2;
        paint.color = color;
        canvas.drawCircle(Offset(x, 2), 9, paint);

        textPaint(AppColors.green7, x, i == 0 ? "1" : i.toString(), canvas);
      }
    }

    // Vẽ mốc tiếp theo (màu xanh dương)
    final nextValue = (value ~/ 5 + 1) * 5;
    if (nextValue <= maxValue) {
      // vẽ nền
      final x = (nextValue / maxValue) * size.width;
      final bgPaint = Paint()..color = AppColors.bgInfoSurface;
      canvas.drawCircle(Offset(x, 2), 9, bgPaint);

      // vẽ border
      final color = AppColors.baseBlue4;
      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;

      canvas.drawCircle(Offset(x, 2), 9, paint);

      // vẽ text
      textPaint(AppColors.baseBlue5, x, nextValue.toString(), canvas);
    }
  }

  void textPaint(Color color, double x, String text, Canvas canvas) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: AppTextStyles.baseBold.copyWith(
          color: color,
          fontSize: 8,
          fontWeight: FontWeight.w500,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    final textOffset = Offset(
      text.length == 1 ? x - 2.5 : x - 4.5,
      0 - 3,
    );

    textPainter.paint(canvas, textOffset);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
