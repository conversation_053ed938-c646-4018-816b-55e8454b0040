import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';

class ExamAppbar extends GetView<ExamController>
    implements PreferredSizeWidget {
  const ExamAppbar({super.key, this.height = 52});

  final double height;

  @override
  Size get preferredSize => Size.fromHeight(height);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
          gradient: LinearGradient(
              colors: AppColors.examAppbarGradient,
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter)),
      child: <PERSON>umn(
        children: [
          Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 7),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    controller.onBack();
                  },
                  child: Assets.images.arrowLeft.svg(),
                ),
                Expanded(
                  child: Text(
                    Utils.parseTestPracticeName(controller.testName),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: AppTextStyles.titleBold,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    controller.openQuestionList();
                  },
                  child: Assets.images.function.svg(),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
