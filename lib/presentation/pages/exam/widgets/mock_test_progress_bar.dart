import 'package:flutter/material.dart';
import 'package:flutter_animation_progress_bar/flutter_animation_progress_bar.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';

class MockTestProgressBar extends StatelessWidget {
  const MockTestProgressBar({super.key, required this.value});

  final double value;

  @override
  Widget build(BuildContext context) {
    return FAProgressBar(
      currentValue: value,
      maxValue: 1,
      size: 6,
      borderRadius: BorderRadius.circular(4),
      backgroundColor: AppColors.mockProgressBarBg,
      progressColor: value > 0.5
          ? AppColors.green5
          : value > 0.25
              ? AppColors.orange5
              : AppColors.red5,
    );
  }
}
