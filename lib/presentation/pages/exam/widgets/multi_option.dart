import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/widgets/checkbox.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/explanation_box.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/question_box.dart';

class MultiSelectOptionsWidget extends GetView<ExamController> {
  const MultiSelectOptionsWidget(
      {super.key, required this.question, required this.isInExam});

  final QuestionEntity question;
  final bool isInExam;
  @override
  Widget build(BuildContext context) {
    final options = question.options ?? [];
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        children: [
          ListView.separated(
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    controller.onSelectOption(options[index]);
                  },
                  child: GetBuilder<ExamController>(
                    id: ExamConstants.optionList,
                    builder: (_) {
                      return AbsorbPointer(
                        child: MultipleOption(
                          status: question.isShowResult &&
                                  controller.trainingMode != TrainingMode.exam
                              ? options[index].resultStatus
                              : options[index].status,
                          index: index,
                          option: options[index],
                          isInExam: isInExam,
                        ),
                      );
                    },
                  ),
                );
              },
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemCount: options.length),
          ExplanationBox(
            text: question.description,
            isInExam: isInExam,
          )
        ],
      ),
    );
  }
}

class MultipleOption extends StatelessWidget {
  MultipleOption({
    super.key,
    required this.status,
    required this.option,
    required this.index,
    required this.isInExam,
  });

  final OptionStatus status;
  final OptionEntity option;
  final int index;
  final bool isInExam;

  @override
  Widget build(BuildContext context) {
    switch (status) {
      case OptionStatus.correct:
        return DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.green1,
              border: Border.all(color: AppColors.green5, width: 1),
              borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top: isInExam ? SettingController.getMarginIcon() : 0),
                  child: CheckboxWidget(
                    value: true,
                    onChanged: (value) {
                      print('aa');
                    },
                    activeColor: AppColors.icSuccess,
                  ),
                ),
                const SizedBox(width: 8),
                OptionWidget(
                  textColor: AppColors.green5,
                  index: index,
                  text: option.qOption ?? '',
                  isInExam: isInExam,
                )
              ],
            ),
          ),
        );

      case OptionStatus.notSelectCorrect:
        return DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.green1,
              border: Border.all(color: AppColors.green5, width: 1),
              borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top: isInExam ? SettingController.getMarginIcon() : 0),
                  child: CheckboxWidget(
                    value: false,
                    onChanged: (value) {},
                    borderColor: AppColors.icSuccess,
                  ),
                ),
                const SizedBox(width: 8),
                OptionWidget(
                  textColor: AppColors.green5,
                  index: index,
                  text: option.qOption ?? '',
                  isInExam: isInExam,
                )
              ],
            ),
          ),
        );

      case OptionStatus.wrong:
        return DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.red1,
              border: Border.all(color: AppColors.red3, width: 1),
              borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top: isInExam ? SettingController.getMarginIcon() : 0),
                  child: Assets.images.checkboxRedPng.image(width: 20),
                ),
                const SizedBox(width: 8),
                OptionWidget(
                  textColor: AppColors.red5,
                  index: index,
                  text: option.qOption ?? '',
                  isInExam: isInExam,
                )
              ],
            ),
          ),
        );

      case OptionStatus.selected:
        return DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.baseBlue1,
              border: Border.all(color: AppColors.baseBlue3, width: 1),
              borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top: isInExam ? SettingController.getMarginIcon() : 0),
                  child: CheckboxWidget(
                    value: true,
                    onChanged: (value) {},
                    activeColor: AppColors.icBtn2ndDefault,
                  ),
                ),
                const SizedBox(width: 8),
                OptionWidget(
                  textColor: AppColors.baseBlueBorder,
                  index: index,
                  text: option.qOption ?? '',
                  isInExam: isInExam,
                )
              ],
            ),
          ),
        );

      default:
        return Padding(
          padding: const EdgeInsets.all(6),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                    top: isInExam ? SettingController.getMarginIcon() : 0),
                child: CheckboxWidget(value: false, onChanged: (value) {}),
              ),
              const SizedBox(width: 8),
              OptionWidget(
                index: index,
                text: option.qOption ?? '',
                isInExam: isInExam,
              )
            ],
          ),
        );
    }
  }
}
