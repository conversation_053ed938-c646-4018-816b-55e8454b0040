import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';

class ImageViewerPage extends StatelessWidget {
  final String? src;
  const ImageViewerPage({Key? key, required this.src}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          PhotoView(
            imageProvider: CachedNetworkImageProvider(src ?? ""),
            backgroundDecoration: const BoxDecoration(color: Colors.black),
          ),
          Positioned(
              top: 12.spMin,
              left: 12.spMin,
              child: GestureDetector(
                child: Icon(
                  Icons.close_rounded,
                  color: Colors.white,
                  size: 36.spMin,
                ),
                onTap: () {
                  Get.back();
                },
              ))
        ],
      ),
    );
  }
}
