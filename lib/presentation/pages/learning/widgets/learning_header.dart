import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/datetime.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_action.dart';
import 'package:scrumpass_exam_simulator/app/widgets/date_picker.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/basic_log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/system_log_event.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';

class LearningHeader extends GetView<LearningController> {
  const LearningHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Text(Constants.appName, style: AppTextStyles.largeBold)),
            BasicLogEvent(
              eventName: EventLogConstants.clickChooseATestDate,
              params: BasicLogEventParams(
                  eventName: 'click_choose_a_test_date',
                  screenLocation: EventLogConstants.learningScreen,
                  locationType: LocationType.screen,
                  locationName: EventLogConstants.learningScreen),
              child: GestureDetector(
                onTap: () {
                  if (Get.find<TourController>().ignorePointer.value == true)
                    return;
                  Popup.instance.showBottomSheet(BottomSheetAction(
                    title: LocaleKeys.bottomsheetTestDateTitle.tr,
                    child: DatePicker(
                      onDateChanged: controller.onChangeTestDate,
                      initDate: controller.testDate,
                    ),
                    onDone: controller.saveTestDate,
                    eventLocationName: EventLogConstants.btm_chooseatestdate,
                  ));
                },
                child: GetBuilder<LearningController>(
                  id: LearningConstants.testDateKey,
                  builder: (_) {
                    return controller.testDate == null
                        ? SelectTestDate()
                        : TestDate(testDate: controller.testDate!);
                  },
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Flexible(
                    child: GetBuilder<LearningController>(
                      id: LearningConstants.usernameKey,
                      builder: (_) {
                        return Text.rich(
                          TextSpan(
                            text: '${LocaleKeys.hello.tr} ',
                            style: AppTextStyles.smallRegular,
                            children: [
                              TextSpan(
                                  text: Global.getUsername,
                                  style: AppTextStyles.smallBold)
                            ],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        );
                      },
                    ),
                  ),
                  if (Global.isPremium)
                    Padding(
                      padding: const EdgeInsets.only(left: 4, bottom: 2),
                      child: Assets.images.king1.svg(),
                    ),
                ],
              ),
            ),
            SizedBox(width: 16),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Assets.images.a3dFire1.svg(),
                const SizedBox(width: 4),
                SystemLogEvent(
                  eventName: EventLogConstants.viewDetailStreak,
                  child: GetBuilder<LearningController>(
                    id: LearningConstants.streakKey,
                    builder: (_) {
                      return Text.rich(TextSpan(
                          text: '${controller.streak} ',
                          style: AppTextStyles.smallBold,
                          children: [
                            TextSpan(
                                text: LocaleKeys.dayStreak.tr,
                                style: AppTextStyles.smallRegular),
                          ]));
                    },
                  ),
                ),
                // const SizedBox(width: 4),
                // Assets.images.altArrowRight.svg(),
              ],
            )
          ],
        )
      ],
    );
  }
}

class TestDate extends StatelessWidget {
  const TestDate({super.key, required this.testDate});

  final DateTime testDate;
  @override
  Widget build(BuildContext context) {
    final remainingDate = testDate.diffFromNow;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Assets.images.calendar.svg(color: AppColors.primary),
        const SizedBox(width: 4),
        Text(
          '$remainingDate ${LocaleKeys.daysLeft.tr}',
          style: AppTextStyles.smallMedium.copyWith(color: AppColors.primary),
        )
      ],
    );
  }
}

class SelectTestDate extends StatelessWidget {
  const SelectTestDate({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Assets.images.calendar.svg(),
        const SizedBox(width: 4),
        Text(
          LocaleKeys.chooseTestDate.tr,
          style: AppTextStyles.smallMedium.copyWith(color: AppColors.primary),
        )
      ],
    );
  }
}
