import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/practice/practice_tab.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/review/widgets/review_header.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/product_tour_widget.dart';

Widget fakeTabTour(LearningController controller, int mockLenght) {
  if (controller.tourController.isTourPrimaryActive.value) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          children: [
            Expanded(
              child: ProductTourWidget(
                index: 2,
                title: LocaleKeys.practiceMode.tr,
                description: LocaleKeys.productTourPracticeMode.tr,
                icon: Container(
                  margin: EdgeInsets.only(right: 4),
                  child: Image.asset(
                    Assets.images.write.path,
                    height: 24,
                  ),
                ),
                percent: (100 / 6) * 2,
                positionIndicator: TourController.positionLeft,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.all(2),
                  child: Container(
                    height: 33,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        controller.tabs[0].title,
                        style: AppTextStyles.smallMedium
                            .copyWith(color: AppColors.white),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 2),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Container(
                  height: 33,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      controller.tabs[1].title,
                      style: AppTextStyles.smallMedium.copyWith(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  } else if (controller.tourController.isTourMockActive.value) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.all(2),
                child: Container(
                  height: 33,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      controller.tabs[0].title,
                      style: AppTextStyles.smallMedium.copyWith(),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 2),
            Expanded(
              child: ProductTourWidget(
                index: 8,
                hideIndicator: true,
                isPause: true,
                description: LocaleKeys.productTourMock.tr,
                icon: Image.asset(
                  Assets.images.edu.path,
                  width: 24,
                ),
                title: "${LocaleKeys.mockMode.tr}",
                positionIndicator: TourController.positionRight,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.all(2),
                  child: Container(
                    height: 33,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        controller.tabs[1].title,
                        style: AppTextStyles.smallMedium
                            .copyWith(color: AppColors.white),
                      ),
                    ),
                  ),
                ),
                tourAction: () {
                  controller.tourController.isTourMockActive.value = false;
                  controller.tourController.completeTourMock();
                },
              ),
            ),
          ],
        ),
      ),
    );
  } else {
    return SizedBox();
  }
}

Widget fakeReviewTab(
    ReviewController controller, TourController tourController) {
  return Obx(() => tourController.test.value == false
      ? Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Positioned(
              left: 0,
              bottom: 0,
              child: ProductTourWidget(
                index: 10,
                description: LocaleKeys.productTourReview.tr,
                title: "${LocaleKeys.testReview.tr}",
                icon: Image.asset(
                  Assets.images.testReview.path,
                  width: 24,
                ),
                hideIndicator: true,
                positionIndicator: TourController.positionLeft,
                review: true,
                tourAction: () {
                  tourController.test.value = true;
                  controller.onChangeTab(ReviewTab.question);
                },
                child: ActiveTab(
                  icon: Assets.images.diploma.svg(color: AppColors.primary),
                  text: LocaleKeys.reviewTestsTab.tr,
                  onTap: () {},
                ),
              ),
            ),
            Positioned(
              right: 0,
              bottom: 0,
              child: InActiveTab(
                icon: Assets.images.notebookBookmark.svg(),
                text: LocaleKeys.reviewQuestionTab.tr,
                onTap: () {},
              ),
            ),
          ],
        )
      : Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Positioned(
              left: 0,
              bottom: 0,
              child: InActiveTab(
                icon: Assets.images.diplomaGrey.svg(),
                text: LocaleKeys.reviewTestsTab.tr,
                isRight: false,
                onTap: () {},
              ),
            ),
            Positioned(
              right: 0,
              bottom: 0,
              child: ProductTourWidget(
                index: 11,
                title: "${LocaleKeys.questionReview.tr}",
                description: LocaleKeys.productTourQuestion.tr,
                icon: Image.asset(
                  Assets.images.questionReview.path,
                  width: 24,
                ),
                hideIndicator: true,
                isPause: true,
                review: true,
                positionIndicator: TourController.positionRight,
                tourAction: () {
                  controller.onChangeTab(ReviewTab.test);
                  tourController.completeTourReview();
                  tourController.test.value = false;
                },
                tourBackAction: () {
                  tourController.test.value = false;
                  controller.onChangeTab(ReviewTab.test);
                },
                child: ActiveTab(
                  icon: Assets.images.notebookBookmark
                      .svg(color: AppColors.primary),
                  text: LocaleKeys.reviewQuestionTab.tr,
                  onTap: () {},
                ),
              ),
            ),
          ],
        ));
}
