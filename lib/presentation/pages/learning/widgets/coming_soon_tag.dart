import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class ComingSoonTag extends StatelessWidget {
  const ComingSoonTag({super.key});

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: HexColor("E90731"), // background
        border: Border.all(
          color: HexColor("D81B1B"), // border
          width: 1,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
        child: Text(
          LocaleKeys.comingSoon.tr,
          style: AppTextStyles.xSmallMedium.copyWith(
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
