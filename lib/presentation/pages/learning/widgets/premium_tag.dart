import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';

class PremiumTagWidget extends StatelessWidget {
  const PremiumTagWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
        decoration: BoxDecoration(
            color: HexColor("FFFCE0"),
            border: Border.all(
                color: HexColor("A49D5F")),
            borderRadius:
                BorderRadius.circular(20)),
        child: Padding(
          padding: const EdgeInsets.symmetric(
              vertical: 2, horizontal: 8),
          child: Text(
            'Premium',
            style: AppTextStyles.xSmallMedium
                .copyWith(
                    color: HexColor("857000")),
          ),
        ));
  }
}