import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/widgets/slider.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/widgets/premium_tag.dart';

class QuickTestSlider extends StatelessWidget {
  const QuickTestSlider({super.key, this.onValueChange, required this.value});

  final void Function(double)? onValueChange;

  final double value;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          PremiumTag(),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: SliderWidget(
                value: value,
                min: 10,
                max: 30,
                interval: 10,
                stepSize: 10,
                onValueChange: onValueChange,
              )),
        ],
      ),
    );
  }
}

class PremiumTag extends StatelessWidget {
  const PremiumTag({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(width: 70),
        PremiumTagWidget(),
        PremiumTagWidget(),
      ],
    );
  }
}
