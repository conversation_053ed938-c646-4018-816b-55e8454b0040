import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/labeled_selection_widget.dart';
import 'package:scrumpass_exam_simulator/app/widgets/slider.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/domain_subject/domain_subject_controller.dart';

class DomainSubjectList extends StatelessWidget {
  const DomainSubjectList({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DomainSubjectController>(
      id: DomainSubjectConstants.domainSubjectKey,
      builder: (controller) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.domainUse.tr,
                    style: AppTextStyles.baseSemiBold.copyWith(
                      color: AppColors.text1st,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      controller.onSelect(!controller.showUnSelectAll.value, 0);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 8,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.btn2ndDefault,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        controller.showUnSelectAll.value
                            ? LocaleKeys.unSelectAll.tr
                            : LocaleKeys.selectAll.tr,
                        style: AppTextStyles.smallMedium.copyWith(
                          color: AppColors.textBtn2nd,
                        ),
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(height: 16),
              Wrap(
                spacing: 12,
                runSpacing: 12,
                children: List.generate(
                  controller.domainSubjects.length - 1,
                  (index) {
                    final item = controller.domainSubjects[index + 1];
                    return LabeledSelectionWidget(
                      label: item.title,
                      value: item.isSelected,
                      onTap: (value) {
                        controller.onSelect(value, index + 1);
                      },
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  LocaleKeys.setUpQuestionLimit.tr,
                  style: AppTextStyles.baseSemiBold.copyWith(
                    color: AppColors.text1st,
                  ),
                ),
              ),
              Container(
                width: Get.width,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.borderLine,
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: SliderWidget(
                    value: controller.questionAmount.toDouble(),
                    min: 10,
                    max: 30,
                    interval: 10,
                    stepSize: 10,
                    onValueChange: controller.onQuestionAmountChange,
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
