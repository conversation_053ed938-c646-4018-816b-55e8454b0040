// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'domain_subject_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DomainSubject {
  String get title => throw _privateConstructorUsedError;
  bool get isSelected => throw _privateConstructorUsedError;
  TestDomain get testDomain => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DomainSubjectCopyWith<DomainSubject> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DomainSubjectCopyWith<$Res> {
  factory $DomainSubjectCopyWith(
          DomainSubject value, $Res Function(DomainSubject) then) =
      _$DomainSubjectCopyWithImpl<$Res, DomainSubject>;
  @useResult
  $Res call({String title, bool isSelected, TestDomain testDomain});
}

/// @nodoc
class _$DomainSubjectCopyWithImpl<$Res, $Val extends DomainSubject>
    implements $DomainSubjectCopyWith<$Res> {
  _$DomainSubjectCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? isSelected = null,
    Object? testDomain = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      testDomain: null == testDomain
          ? _value.testDomain
          : testDomain // ignore: cast_nullable_to_non_nullable
              as TestDomain,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DomainSubjectImplCopyWith<$Res>
    implements $DomainSubjectCopyWith<$Res> {
  factory _$$DomainSubjectImplCopyWith(
          _$DomainSubjectImpl value, $Res Function(_$DomainSubjectImpl) then) =
      __$$DomainSubjectImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String title, bool isSelected, TestDomain testDomain});
}

/// @nodoc
class __$$DomainSubjectImplCopyWithImpl<$Res>
    extends _$DomainSubjectCopyWithImpl<$Res, _$DomainSubjectImpl>
    implements _$$DomainSubjectImplCopyWith<$Res> {
  __$$DomainSubjectImplCopyWithImpl(
      _$DomainSubjectImpl _value, $Res Function(_$DomainSubjectImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? isSelected = null,
    Object? testDomain = null,
  }) {
    return _then(_$DomainSubjectImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      testDomain: null == testDomain
          ? _value.testDomain
          : testDomain // ignore: cast_nullable_to_non_nullable
              as TestDomain,
    ));
  }
}

/// @nodoc

class _$DomainSubjectImpl implements _DomainSubject {
  _$DomainSubjectImpl(
      {required this.title, this.isSelected = false, required this.testDomain});

  @override
  final String title;
  @override
  @JsonKey()
  final bool isSelected;
  @override
  final TestDomain testDomain;

  @override
  String toString() {
    return 'DomainSubject(title: $title, isSelected: $isSelected, testDomain: $testDomain)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DomainSubjectImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected) &&
            (identical(other.testDomain, testDomain) ||
                other.testDomain == testDomain));
  }

  @override
  int get hashCode => Object.hash(runtimeType, title, isSelected, testDomain);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DomainSubjectImplCopyWith<_$DomainSubjectImpl> get copyWith =>
      __$$DomainSubjectImplCopyWithImpl<_$DomainSubjectImpl>(this, _$identity);
}

abstract class _DomainSubject implements DomainSubject {
  factory _DomainSubject(
      {required final String title,
      final bool isSelected,
      required final TestDomain testDomain}) = _$DomainSubjectImpl;

  @override
  String get title;
  @override
  bool get isSelected;
  @override
  TestDomain get testDomain;
  @override
  @JsonKey(ignore: true)
  _$$DomainSubjectImplCopyWith<_$DomainSubjectImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
