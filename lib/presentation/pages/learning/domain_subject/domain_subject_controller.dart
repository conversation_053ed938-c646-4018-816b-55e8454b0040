import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/domain_subject/domain_subject_model.dart';

class DomainSubjectController extends GetxController {
  DomainSubjectController();

  int questionAmount = 10;
  final showUnSelectAll = false.obs;

  final List<DomainSubject> selectedDomainSubjects = [];

  final List<DomainSubject> domainSubjects = [
    DomainSubject(title: "All", testDomain: TestDomain.all),
    DomainSubject(
      title: TestDomain.scrumEvent.text,
      testDomain: TestDomain.scrumEvent,
    ),
    DomainSubject(
      title: TestDomain.scrumArtifact.text,
      testDomain: TestDomain.scrumArtifact,
    ),
    DomainSubject(
      title: TestDomain.scrumTeamAndOrganization.text,
      testDomain: TestDomain.scrumTeamAndOrganization,
    ),
    DomainSubject(
      title: TestDomain.leadershipAndManagementSkills.text,
      testDomain: TestDomain.leadershipAndManagementSkills,
    ),
    DomainSubject(
      title: TestDomain.productDevelopmentAndManagement.text,
      testDomain: TestDomain.productDevelopmentAndManagement,
    ),
  ];

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  List<TestDomain> onDone() {
    selectedDomainSubjects
        .addAll(domainSubjects.where((element) => element.isSelected));
    if (domainSubjects.any((element) =>
        element.isSelected && element.testDomain == TestDomain.all)) {
      selectedDomainSubjects.clear();
      selectedDomainSubjects.add(
          domainSubjects.firstWhere((e) => e.testDomain == TestDomain.all));
    }
    final result = selectedDomainSubjects.map((e) => e.testDomain).toList();
    return result;
  }

  void onSelect(bool? value, int index) {
    if (index == 0) {
      showUnSelectAll.value = value ?? false;
      selectAll(value ?? false);
    } else {
      domainSubjects[index] =
          domainSubjects[index].copyWith(isSelected: value ?? false);
    }
    bool selectAllState = domainSubjects
        .where((element) => element.title != 'All')
        .every((element) => element.isSelected);
    if (selectAllState) {
      selectAll(true);
    }
    bool unSelectAllState = domainSubjects
        .where((element) => element.title != 'All')
        .every((element) => element.isSelected == false);
    if (unSelectAllState) {
      selectAll(false);
    }
    showUnSelectAll.value = selectAllState;
    update([DomainSubjectConstants.domainSubjectBottomsheetKey]);
  }

  void selectAll(bool value) {
    for (var i = 0; i < domainSubjects.length; i++) {
      domainSubjects[i] = domainSubjects[i].copyWith(isSelected: value);
    }
    update([DomainSubjectConstants.domainSubjectBottomsheetKey]);
  }

  bool isHasSelected() {
    for (var i = 0; i < domainSubjects.length; i++) {
      if (domainSubjects[i].testDomain == TestDomain.all) {
        continue;
      }
      if (domainSubjects[i].isSelected) {
        return true;
      }
    }
    return false;
  }

  onQuestionAmountChange(double value) {
    questionAmount = value.toInt();
    update([DomainSubjectConstants.domainSubjectBottomsheetKey]);
  }
}

class DomainSubjectConstants {
  static const domainSubjectKey = "domainSubject";
  static const domainSubjectBottomsheetKey = "domainSubjectBottomsheet";
}
