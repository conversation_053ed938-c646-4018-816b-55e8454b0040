import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_action.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/other/exam_params.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/domain_subject/domain_subject.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/domain_subject/domain_subject_controller.dart';

class DomainSubjectBottomsheet extends StatelessWidget {
  const DomainSubjectBottomsheet({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DomainSubjectController>(
      id: DomainSubjectConstants.domainSubjectBottomsheetKey,
      init: DomainSubjectController(),
      initState: (_) {},
      builder: (controller) {
        return BottomSheetAction(
          title: LocaleKeys.chooseAQuestionPack.tr,
          child: DomainSubjectList(),
          onDone: () {
            final result = controller.onDone();
            final questionAmount = controller.questionAmount;
            Get.back();

            Get.toNamed(RouterName.doTest,
                arguments: ExamParams(
                  numQuestion: questionAmount,
                  testType: TestType.domain,
                  testName: TestType.domain.testTypeKey,
                  domain: result,
                ));
          },
          isEnableAction: controller.isHasSelected(),
          eventLocationName: EventLogConstants.btm_chooseaquestionbank,
        );
      },
    );
  }
}
