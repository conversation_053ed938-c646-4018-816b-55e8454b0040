import 'package:flutter/material.dart';
import 'package:flutter_animation_progress_bar/flutter_animation_progress_bar.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/app/extensions/int.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/system_log_event.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';

class PassingRate extends GetView<LearningController> {
  const PassingRate({super.key});

  @override
  Widget build(BuildContext context) {
    return controller.results.length < 10
        ? const PassRateWithoutStatistic()
        : const PassRateWithStatistic();
  }
}

class PassRateWithStatistic extends GetView<LearningController> {
  const PassRateWithStatistic({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SystemLogEvent(
      eventName: EventLogConstants.viewDetailPassingRate,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(16),
          ),
          image: DecorationImage(
              image: AssetImage(Assets.images.passrateFullBg.path),
              fit: BoxFit.fill),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                      child: Text(
                    LocaleKeys.passingRate.tr,
                    style: AppTextStyles.baseMedium
                        .copyWith(color: AppColors.textInverseTitle),
                  )),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      LocaleKeys.overall.tr,
                      style: AppTextStyles.smallRegular
                          .copyWith(color: Colors.white),
                    ),
                  ),
                  Text(
                    '${controller.passRate.roundedPrecisionToString(0)}%',
                    style: AppTextStyles.xLargeBold
                        .copyWith(color: AppColors.textInverseTitle),
                  )
                ],
              ),
              FAProgressBar(
                currentValue: controller.passRate,
                maxValue: 100,
                size: 16,
                animatedDuration: 0.seconds,
                borderRadius: BorderRadius.circular(3),
                backgroundColor: AppColors.progressBarBg.withOpacity(0.22),
                progressGradient: controller.passRate >= 75
                    ? LinearGradient(colors: AppColors.blueGradientProgress)
                    : controller.passRate >= 85
                        ? LinearGradient(
                            colors: AppColors.greenGradientProgress)
                        : LinearGradient(
                            colors: AppColors.yellowGradientProgress),
              ),
              const SizedBox(height: 8),
              Text(
                controller.passRate >= 75
                    ? LocaleKeys.doingGreat.tr
                    : controller.passRate >= 85
                        ? LocaleKeys.readyTakeExam.tr
                        : LocaleKeys.needPracticeMore.tr,
                style:
                    AppTextStyles.xSmallRegular.copyWith(color: Colors.white),
              ),
              StatisticsBox()
            ],
          ),
        ),
      ),
    );
  }
}

class PassRateWithoutStatistic extends GetView<LearningController> {
  const PassRateWithoutStatistic({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(16),
        ),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: AppColors.passrateGradientBorder,
        ),
      ),
      padding: EdgeInsets.all(2),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(16),
          ),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.passrateGradient,
          ),
        ),
        child: Stack(
          children: [
            // Positioned(
            //     child: SizedBox(
            //   width: Get.mediaQuery.size.width,
            //   height: 125,
            //   child: Assets.images.passrateSmallBg.image(fit: BoxFit.fill),
            // )),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                          child: Text(
                        LocaleKeys.passingRate.tr,
                        style: AppTextStyles.baseMedium
                            .copyWith(color: Colors.white),
                      )),
                      // Assets.images.altArrowRight.svg(color: Colors.white)
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text.rich(TextSpan(
                      text: '${controller.results.length}/10',
                      style: AppTextStyles.xLargeBold
                          .copyWith(color: Colors.white),
                      children: [
                        TextSpan(
                            text: ' ${LocaleKeys.testDone.tr}',
                            style: AppTextStyles.smallRegular
                                .copyWith(color: Colors.white))
                      ])),
                  FAProgressBar(
                    currentValue: controller.results.length.toDouble(),
                    maxValue: 10,
                    size: 15,
                    animatedDuration: 0.seconds,
                    borderRadius: BorderRadius.circular(3),
                    backgroundColor: AppColors.progressBarBg.withOpacity(0.22),
                    progressGradient:
                        LinearGradient(colors: AppColors.blueGradientProgress),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    LocaleKeys.finishTestRequire.tr,
                    style: AppTextStyles.xxSmallRegular
                        .copyWith(color: Colors.white),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class StatisticsBox extends GetView<LearningController> {
  const StatisticsBox({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: AppColors.passRateStatisticsBg.withOpacity(0.4),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          child: Row(
            children: [
              Flexible(
                  fit: FlexFit.tight,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        StatisticItem(
                            label: LocaleKeys.avgScore.tr,
                            value:
                                '${controller.averageScore.roundedPrecisionToString(0)}%'),
                        const SizedBox(height: 8),
                        StatisticItem(
                            label: LocaleKeys.latestScore.tr,
                            value:
                                '${controller.latestScore.roundedPrecisionToString(0)}%'),
                      ])),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: SizedBox(
                  height: 15,
                  child: VerticalDivider(
                    width: 2,
                    thickness: 2,
                    color: HexColor("D0E2F1"),
                  ),
                ),
              ),
              Flexible(
                  fit: FlexFit.tight,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        StatisticItem(
                            label: LocaleKeys.totalTime.tr,
                            value: controller.totalTime.hourMinuteString),
                        const SizedBox(height: 8),
                        StatisticItem(
                            label: LocaleKeys.avgTimeQuestion.tr,
                            value: controller.timeEachQuestion
                                .round()
                                .shortTextString),
                      ])),
            ],
          ),
        ),
      ),
    );
  }
}

class StatisticItem extends StatelessWidget {
  const StatisticItem({super.key, required this.label, required this.value});

  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyles.smallRegular
              .copyWith(color: AppColors.textInverseTitle.withOpacity(0.7)),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.smallBold
              .copyWith(color: AppColors.textInverseTitle),
        ),
      ],
    );
  }
}
