import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/passing_rate/passing_rate.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/practice/practice.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/similar_app/similar_app.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/widgets/learning_header.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/premium_banner.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/product_tour_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';

class LearningPage extends StatelessWidget {
  final ScrollController scrollController;
  const LearningPage({super.key, required this.scrollController});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LearningController>(
        init: LearningController(),
        autoRemove: false,
        builder: (controller) {
          return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppColors.homeGradient,
                  stops: [0.0, 0.9, 1.0],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: 0,
                    right: 10,
                    child: Assets.images.homeLight1.image(),
                  ),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Assets.images.homeLight2.image(),
                  ),
                  SafeArea(
                      child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        LearningHeader(),
                        Obx(() => controller.isLoading.value
                            ? Padding(
                                padding: const EdgeInsets.only(top: 16),
                                child: Skeletonizer.zone(
                                    child: Bone.button(
                                        width: double.infinity,
                                        height: 200,
                                        borderRadius:
                                            BorderRadius.circular(16))),
                              )
                            : Padding(
                                padding: const EdgeInsets.only(top: 16),
                                child: ProductTourWidget(
                                  index: 0,
                                  title: LocaleKeys.welcomeTo.tr + " Exam Pass",
                                  description: LocaleKeys.productTourNav.tr,
                                  icon: Container(
                                    padding: EdgeInsets.only(right: 4),
                                    child: Image.asset(
                                      Assets.images.hand.path,
                                      height: 24,
                                    ),
                                  ),
                                  tourAction: () async {
                                    await Get.find<HomeController>()
                                        .handleMoveTour(
                                      offset: Global.isPremium ? 140 : 240,
                                    );
                                  },
                                  child: PassingRate(),
                                ),
                              )),
                        if (Global.isPremium == false) ...{
                          const SizedBox(height: 12),
                          PremiumBanner(
                            screenLocation: EventLogConstants.learningScreen,
                          ),
                        },
                        const SizedBox(height: 16),
                        Practice(),
                        const SizedBox(height: 10),
                        GetBuilder<LearningController>(
                          id: LearningConstants.relativeAppKey,
                          builder: (_) {
                            return SimilarApp(
                              screenLocation: EventLogConstants.learningScreen,
                              relativeApps: controller.relativeApps,
                            );
                          },
                        ),
                        const TabBarSpace()
                      ],
                    ),
                  )),
                ],
              ));
        });
  }
}
