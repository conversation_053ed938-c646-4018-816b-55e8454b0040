import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/basic_log_event.dart';
import 'package:scrumpass_exam_simulator/domain/entities/relative_app.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/get_reward.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:store_redirect/store_redirect.dart';

class SimilarApp extends StatelessWidget {
  const SimilarApp(
      {super.key, required this.screenLocation, required this.relativeApps});

  final String screenLocation;

  final List<RelativeAppEntity> relativeApps;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (relativeApps.isNotEmpty)
          SizedBox(
            width: double.infinity,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Text(
                        LocaleKeys.similarAppTitle.tr,
                        style: AppTextStyles.largeSemiBold,
                        textAlign: TextAlign.left,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 115,
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return BasicLogEvent(
                              eventName: EventLogConstants.clickOtherCerts,
                              params: BasicLogEventParams(
                                  eventName: EventLogConstants.clickOtherCerts,
                                  screenLocation:
                                      EventLogConstants.learningScreen,
                                  locationType: LocationType.screen,
                                  locationName:
                                      EventLogConstants.learningScreen),
                              child: SimilarAppItem(
                                item: relativeApps[index],
                                hasPaddingRight:
                                    index == relativeApps.length - 1
                                        ? true
                                        : false,
                              ));
                        },
                        itemCount: relativeApps.length,
                        scrollDirection: Axis.horizontal,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        // const SizedBox(height: 16),
        // GetReward(screenLocation: screenLocation),
      ],
    );
  }
}

class SimilarAppItem extends StatelessWidget {
  const SimilarAppItem(
      {super.key, required this.item, this.hasPaddingRight = false});

  final RelativeAppEntity item;
  final bool hasPaddingRight;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        StoreRedirect.redirect(
          androidAppId: item.androidId,
          iOSAppId: item.iosId,
        );
      },
      child: Padding(
        padding: EdgeInsets.only(left: 12, right: hasPaddingRight ? 12 : 0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            DecoratedBox(
              decoration:
                  BoxDecoration(borderRadius: BorderRadius.circular(10)),
              child: SizedBox(
                width: 76,
                height: 76,
                child: CachedNetworkImage(
                  imageUrl: item.icon ?? '',
                  placeholder: (context, url) => Skeletonizer.zone(
                    child: Bone.square(
                      size: 76,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  errorWidget: (context, url, error) => Icon(Icons.error),
                ),
              ),
            ),
            const SizedBox(height: 4),
            SizedBox(
              width: 76,
              child: Text(
                item.name ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyles.xSmallMedium
                    .copyWith(color: AppColors.textHint),
              ),
            )
          ],
        ),
      ),
    );
  }
}
