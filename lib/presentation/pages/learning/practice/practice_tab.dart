

import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

enum PracticeTab { practice, mock }

extension TabTitleExt on PracticeTab {
  String get title {
    switch (this) {
      case PracticeTab.practice:
        return LocaleKeys.practice.tr;
      case PracticeTab.mock:
        return LocaleKeys.mockTest.tr;
    }
  }

  String get eventName {
    switch (this) {
      case PracticeTab.practice:
        return EventLogConstants.clickPractice;
      case PracticeTab.mock:
        return EventLogConstants.clickMockTest;
    }
  }
}
