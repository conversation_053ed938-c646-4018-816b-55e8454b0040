import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_empty.dart';
import 'package:scrumpass_exam_simulator/app/widgets/dash_divider.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/image_viewer_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/explanation_widget.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/report_question_widget.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/result_multi_option.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/result_single_option.dart';

class FilterContent extends StatelessWidget {
  const FilterContent({
    super.key,
    required this.questions,
    this.addBottomSpace = false,
    this.useObjectIndex = true,
    required this.type,
    this.isInExam = true,
  });

  final List<QuestionResult> questions;
  final bool addBottomSpace;

  /// dùng index trong QuestionResult
  final bool useObjectIndex;
  final ResultFilter type;
  final bool isInExam;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
      child: questions.isEmpty
          ? EmptyState(type: type)
          : ListView.separated(
              shrinkWrap: true,
              clipBehavior: Clip.none,
              padding: EdgeInsets.zero,
              physics: const ClampingScrollPhysics(),
              itemBuilder: (context, index) =>
                  addBottomSpace && index == questions.length
                      ? const SizedBox(height: 100)
                      : ResultQuestionItem(
                          question: questions[index],
                          index: useObjectIndex ? null : index,
                          isInExam: isInExam,
                        ),
              separatorBuilder: (context, index) => const SizedBox(height: 20),
              itemCount:
                  addBottomSpace ? questions.length + 1 : questions.length),
    );
  }
}

class EmptyState extends StatelessWidget {
  const EmptyState({super.key, required this.type});

  final ResultFilter type;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Assets.images.emptyIcon.image(width: 241, height: 160),
          const SizedBox(height: 16),
          Text(
            "${type.emptyText}",
            style:
                AppTextStyles.smallMedium.copyWith(color: AppColors.textHint),
          ),
          const SizedBox(height: 120),
        ],
      ),
    );
  }
}

class ResultQuestionItem extends StatelessWidget {
  const ResultQuestionItem(
      {super.key, required this.question, this.index, required this.isInExam});
  final bool isInExam;
  final QuestionResult question;
  final int? index;

  @override
  Widget build(BuildContext context) {
    return Stack(clipBehavior: Clip.none, children: [
      DecoratedBox(
        decoration: BoxDecoration(
            color: question.hasAnySelect == false
                ? AppColors.resultItemInCorrectBg
                : question.isCorrect == true
                    ? AppColors.resultItemCorrectBg
                    : AppColors.resultItemInCorrectBg,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
                color: question.hasAnySelect == false
                    ? AppColors.resultItemInCorrectBorder
                    : question.isCorrect == true
                        ? AppColors.resultItemCorrectBorder
                        : AppColors.resultItemInCorrectBorder)),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Html(
                      data:
                          '${index == null ? (question.indexInTest ?? 0) + 1 : (index! + 1)}. ${question.getQuestionText()}',
                      style: {
                        "body": Style(
                            fontFamily: 'Inter',
                            color: AppColors.examQuestion,
                            margin: Margins.zero,
                            padding: HtmlPaddings.zero,
                            fontWeight: FontWeight.bold,
                            fontSize: FontSize(14),
                            lineHeight: const LineHeight(1.4)),
                        "p": Style(
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                        ),
                        "img": Style(
                          width: Width(
                              MediaQuery.of(context).size.width - 50, Unit.px),
                        ),
                      },
                      extensions: [
                        OnImageTapExtension(
                          onImageTap: (src, imgAttributes, element) {
                            if (src != null && context.mounted) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (_) => ImageViewerPage(src: src),
                                ),
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      Popup.instance.showBottomSheet(BottomSheetEmpty(
                        title: "${LocaleKeys.report.tr} ❗️",
                        child: ReportQuestionWidget(
                          question: question,
                        ),
                      ));
                    },
                    child: Assets.images.dangerReport.svg(),
                  )
                ],
              ),
              const SizedBox(height: 16),
              if (question.type == QuestionType.single)
                ResultSingleOptionsWidget(
                  question: question,
                  isInExam: isInExam,
                ),
              if (question.type == QuestionType.multiple)
                ResultMultiOptionsWidget(
                  question: question,
                  isInExam: isInExam,
                ),
              const SizedBox(height: 16),
              DashDivider(color: AppColors.border1st),
              ResultExplanationWidget(text: question.description ?? '')
            ],
          ),
        ),
      ),
      if (question.isBookmark ?? false)
        Positioned(
          child: Assets.images.resultQuestionBookmark.svg(),
          top: -3,
          left: 12,
        )
    ]);
  }
}
