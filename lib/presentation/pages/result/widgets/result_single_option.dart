import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/single_option.dart';

class ResultSingleOptionsWidget extends StatelessWidget {
  const ResultSingleOptionsWidget(
      {super.key, required this.question, required this.isInExam});
  final bool isInExam;
  final QuestionResult question;

  @override
  Widget build(BuildContext context) {
    final options = question.options ?? [];
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: ListView.separated(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) => SingleOption(
                status: options[index].resultStatus,
                option: options[index],
                index: index,
                isInExam: isInExam,
              ),
          separatorBuilder: (context, index) => const SizedBox(height: 12),
          itemCount: options.length),
    );
  }
}
