import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/app/extensions/int.dart';
import 'package:scrumpass_exam_simulator/app/utils/datetime.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/result_detail/result_detail_controller.dart';

class ResultCard extends GetView<ResultDetailController> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Assets.images.resultCardItem2.image(
              height: 32,
              color: AppColors.resutltCardGradient[0],
            ),
            _MainContent(),
          ],
        ),
        Positioned(
          top: 6,
          left: 0.5,
          child: SizedBox(
            width: Get.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                    width: 56,
                    height: 56,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: controller.testType.bgColor),
                      child: Center(
                        child: SvgPicture.asset(controller.testType.icon),
                      ),
                    )),
              ],
            ),
          ),
        )
      ],
    );
  }
}

class _MainContent extends StatelessWidget {
  const _MainContent();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: DecoratedBox(
        decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: AppColors.resutltCardGradient,
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(12)),
        child: Stack(
          children: [
            Positioned(
                top: 0,
                left: 0,
                child: Assets.images.resultCardItem1.image(height: 85)),
            Positioned(
                bottom: -95,
                right: -12,
                child: Assets.images.resultCardItem.image(width: 160)),
            Column(
              children: [
                const SizedBox(height: 34),
                _Header(),
                const SizedBox(height: 16),
                _StatisticBar(),
                const SizedBox(height: 16),
                _StatisticRow(),
                const SizedBox(height: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _StatisticRow extends GetView<ResultDetailController> {
  const _StatisticRow();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _StatisticItem(
              label: LocaleKeys.overall.tr,
              count: controller.overallScore,
              color: AppColors.resultOverall),
          _StatisticItem(
              label: LocaleKeys.correct.tr,
              count: controller.corrects.length.toString(),
              color: AppColors.resultCorrect),
          _StatisticItem(
              label: LocaleKeys.incorrect.tr,
              count: controller.incorrects.length.toString(),
              color: AppColors.red2),
          _StatisticItem(
              label: LocaleKeys.unanswered.tr,
              count: controller.notDone.length.toString(),
              color: AppColors.neutral2),
        ],
      ),
    );
  }
}

class _StatisticItem extends StatelessWidget {
  const _StatisticItem({
    required this.label,
    required this.count,
    required this.color,
  });

  final String label;
  final String count;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(3),
          ),
        ),
        SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTextStyles.xSmallRegular
                  .copyWith(color: AppColors.textInverseTitle),
            ),
            Text(
              count,
              style: AppTextStyles.smallBold
                  .copyWith(color: AppColors.textInverseTitle),
            )
          ],
        ),
      ],
    );
  }
}

class _StatisticBar extends GetView<ResultDetailController> {
  const _StatisticBar();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Container(
        height: 12,
        child: Row(
          children: [
            if (controller.corrects.isNotEmpty)
              _BarItem(
                  color: AppColors.resultCorrect,
                  value: controller.corrects.length),
            if (controller.incorrects.isNotEmpty)
              _BarItem(
                  color: AppColors.red2, value: controller.incorrects.length),
            if (controller.notDone.isNotEmpty)
              _BarItem(
                  color: AppColors.neutral2, value: controller.notDone.length),
          ],
        ),
      ),
    );
  }
}

class _BarItem extends StatelessWidget {
  const _BarItem({required this.color, required this.value});

  final Color color;
  final int value;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: value,
      child: Padding(
        padding: const EdgeInsets.only(right: 2),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(3),
          ),
          child: Container(),
        ),
      ),
    );
  }
}

class _Header extends GetView<ResultDetailController> {
  const _Header();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 6),
        Text(
          Utils.parseTestPracticeName(controller.testName),
          style: AppTextStyles.baseSemiBold
              .copyWith(color: AppColors.textInverseTitle),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Assets.images.clockCircle
                .svg(color: AppColors.icInverse, width: 16),
            const SizedBox(width: 6),
            Text(
              '${controller.timeDoTest.toTimeTextString} • ${controller.time.dateString}',
              style: AppTextStyles.xSmallMedium.copyWith(color: Colors.white),
            )
          ],
        )
      ],
    );
  }
}
