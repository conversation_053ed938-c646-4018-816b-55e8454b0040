import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/repositories/report_repo_impl.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/send_report_question_usecase.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

//Đoạn code cần thay thế
class ReportQuestionWidget extends StatefulWidget {
  final QuestionResult question;
  const ReportQuestionWidget({Key? key, required this.question})
      : super(key: key);

  @override
  State<ReportQuestionWidget> createState() => _ReportQuestionWidgetState();
}

class _ReportQuestionWidgetState extends State<ReportQuestionWidget> {
  final localService = Get.find<LocalService>();
  FocusNode _focusNode = FocusNode();
  Color _borderColor = Colors.grey;
  TextEditingController reportTextController = TextEditingController();
  RxBool status = false.obs;
  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    Get.lazyPut(() => ReportRepoImpl());
    Get.lazyPut(() => SendReportQuestionUsecase(Get.find<ReportRepoImpl>()));
    _focusNode.addListener(() {
      setState(() {
        _borderColor =
            _focusNode.hasFocus ? AppColors.icBtn2ndDefault : Colors.grey;
      });
    });
    reportTextController.addListener(() {
      final textLength = reportTextController.text.trim().length;
      if (textLength > 0) {
        status.value = true;
      } else {
        status.value = false;
      }
    });
  }

  @override
  void dispose() {
    reportTextController.dispose();
    super.dispose();
  }

  String removeHtmlTags(String input) {
    final withoutTags = input.replaceAll(RegExp(r'<[^>]*>|<\/[^>]*>'), '');
    return withoutTags;
  }

  Future<AppData?> _getAppData() async {
    return localService.getAppData();
  }

  Future<void> handleSendReport(
      QuestionResult question, String reportdetail) async {
    final appData = await _getAppData();
    String debugid = appData?.debugId ?? '';
    final response = await Get.find<SendReportQuestionUsecase>()
        .execute(SendReportQuestionParams(
      qid: question.qid!,
      reportdetail: reportdetail.trim(),
      debugid: debugid,
    ));
    Get.back();
    Future.delayed(Duration(milliseconds: 500), () {
      if (response == "Success") {
        showSuccessPopup();
      } else {
        Popup.instance.showSnackBar(
          message: LocaleKeys.defaultErrorMsg,
          type: SnackbarType.error,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Column(
        children: [
          Container(
            constraints: BoxConstraints(
              maxHeight: 180,
            ),
            child: SingleChildScrollView(
              child: RichText(
                text: TextSpan(
                    text: "❓${LocaleKeys.question.tr}:",
                    style: AppTextStyles.largeSemiBold.copyWith(height: 1.4),
                    children: [
                      TextSpan(
                        text: " ${removeHtmlTags(widget.question.question!)}",
                        style: AppTextStyles.largeRegular,
                      ),
                    ]),
              ),
            ),
          ),
          SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: _borderColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.only(bottom: 8, right: 4),
              child: Scrollbar(
                controller: _scrollController,
                thumbVisibility: true,
                radius: Radius.circular(8),
                child: TextField(
                  focusNode: _focusNode,
                  autofocus: true,
                  maxLines: 3,
                  controller: reportTextController,
                  maxLength: 250,
                  scrollController: _scrollController,
                  style: AppTextStyles.baseRegular
                      .copyWith(decorationThickness: 0),
                  cursorColor: AppColors.primary,
                  decoration: InputDecoration(
                    floatingLabelBehavior: FloatingLabelBehavior.always,
                    contentPadding: EdgeInsets.only(
                        left: 16, right: 16, top: 8, bottom: 16),
                    border: InputBorder.none,
                    label: RichText(
                      text: TextSpan(
                          text: LocaleKeys.report.tr,
                          style: AppTextStyles.smallMedium,
                          children: [
                            TextSpan(
                              text: " *",
                              style: AppTextStyles.smallMedium
                                  .copyWith(color: AppColors.textError),
                            ),
                          ]),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Obx(
            () => Container(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: PrimaryButton(
                text: LocaleKeys.send.tr,
                isEnable: status.value,
                onTap: () async {
                  await handleSendReport(
                      widget.question, reportTextController.text);
                },
              ),
            ),
          )
        ],
      ),
    );
  }

  Future showSuccessPopup() async {
    return await Get.dialog(Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
                colors: AppColors.successReportBgGradient,
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 8),
              Assets.images.reportSuccess.image(width: 327),
              const SizedBox(height: 10),
              SizedBox(
                width: double.infinity,
                child: Text(LocaleKeys.reportSuccessTitle.tr,
                    textAlign: TextAlign.center,
                    style: AppTextStyles.xLargeBold.copyWith(
                      color: AppColors.elementTextNormal,
                    )),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: Text(LocaleKeys.reportSuccessDescription.tr,
                    textAlign: TextAlign.center,
                    style: AppTextStyles.smallMedium.copyWith(
                      color: AppColors.popupTitle,
                    )),
              ),
              const SizedBox(height: 24),
              PrimaryButton(text: 'OK', width: 164, onTap: () => Get.back()),
            ],
          ),
        ),
      ),
    ));
  }
}
//end

/* class ReportQuestionWidget extends GetView<ReportModalController> {
  @override
  ReportModalController get controller => Get.put(ReportModalController());

  final QuestionResult question;
  const ReportQuestionWidget({Key? key, required this.question})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Column(
        children: [
          RichText(
            text: TextSpan(
                text: "❓ ${LocaleKeys.question}:",
                style: AppTextStyles.largeSemiBold.copyWith(height: 1.4),
                children: [
                  TextSpan(
                    text: " ${controller.removeHtmlTags(question.question!)}",
                    style: AppTextStyles.largeRegular,
                  ),
                ]),
          ),
          SizedBox(height: 16),
          Obx(
            () => Container(
              decoration: BoxDecoration(
                border: Border.all(color: controller.borderColor.value),
                borderRadius: BorderRadius.circular(4),
              ),
              child: TextField(
                focusNode: controller.focusNode,
                maxLines: 3,
                controller: controller.reportController,
                style: AppTextStyles.baseRegular,
                decoration: InputDecoration(
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  contentPadding: EdgeInsets.all(16),
                  border: InputBorder.none,
                  label: RichText(
                    text: TextSpan(
                        text: LocaleKeys.report,
                        style: AppTextStyles.smallMedium,
                        children: [
                          TextSpan(
                            text: " *",
                            style: AppTextStyles.smallMedium
                                .copyWith(color: AppColors.textError),
                          ),
                        ]),
                  ),
                ),
              ),
            ),
          ),
          Obx(
            () => Container(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: PrimaryButton(
                text: LocaleKeys.send,
                isEnable: controller.status.value,
                onTap: () async {
                  await controller.handleSendReport(
                      question, controller.reportController.text);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
} */
