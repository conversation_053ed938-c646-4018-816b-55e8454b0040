import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/result_detail/result_detail_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/filter_content.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/result_card.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/widgets/result_card_mock.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/sticky_header.dart';

class ResultContent extends GetView<ResultDetailController> {
  const ResultContent();

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverToBoxAdapter(child: const SizedBox(height: 10)),
          SliverToBoxAdapter(
              child: controller.result.isMockTest
                  ? ResultCardMock()
                  : ResultCard()),
          SliverToBoxAdapter(child: const SizedBox(height: 8)),
          SliverPersistentHeader(
            pinned: true,
            delegate: StickyTabBarDelegate(
              TabBar(
                tabs: controller.filters.map((e) {
                  switch (e) {
                    case ResultFilter.all:
                      return Tab(text: e.tabText, height: 60);
                    case ResultFilter.correct:
                      return Tab(
                          text: '${e.tabText} (${controller.corrects.length})',
                          height: 60);
                    case ResultFilter.incorrect:
                      return Tab(
                          text:
                              '${e.tabText} (${controller.incorrects.length})',
                          height: 60);
                    case ResultFilter.bookmarked:
                      return Tab(
                          text:
                              '${e.tabText} (${controller.bookmarked.length})',
                          height: 60);
                    case ResultFilter.notDone:
                      return Tab(
                          text: '${e.tabText} (${controller.notDone.length})',
                          height: 60);
                  }
                }).toList(),
                controller: controller.tabController,
                indicatorSize: TabBarIndicatorSize.label,
                indicatorPadding: EdgeInsets.zero,
                isScrollable: true,
                indicator: TabBarIndicator(color: AppColors.primary, radius: 0),
                labelColor: AppColors.primary,
                labelStyle: AppTextStyles.smallSemiBold,
                unselectedLabelStyle: AppTextStyles.smallMedium,
                unselectedLabelColor: AppColors.textHint,
                dividerColor: Colors.transparent,
                tabAlignment: TabAlignment.start,
                labelPadding: EdgeInsets.symmetric(horizontal: 12),
              ),
            ),
          ),
          //SliverToBoxAdapter(child: const SizedBox(height: 13)),
        ],
        body: TabBarView(
          controller: controller.tabController,
          children: controller.filters.map((e) {
            switch (e) {
              case ResultFilter.all:
                return FilterContent(
                    questions: controller.questions, type: e, isInExam: false);
              case ResultFilter.correct:
                return FilterContent(
                    questions: controller.corrects, type: e, isInExam: false);
              case ResultFilter.incorrect:
                return FilterContent(
                    questions: controller.incorrects, type: e, isInExam: false);
              case ResultFilter.bookmarked:
                return FilterContent(
                    questions: controller.bookmarked, type: e, isInExam: false);
              case ResultFilter.notDone:
                return FilterContent(
                    questions: controller.notDone, type: e, isInExam: false);
            }
          }).toList(),
        ),
      ),
    );
  }
}

class TabBarIndicator extends Decoration {
  final BoxPainter _painter;

  TabBarIndicator({required Color color, required double radius})
      : _painter = _TabBarIndicator(color, radius);

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) => _painter;
}

class _TabBarIndicator extends BoxPainter {
  final Paint _paint;
  final double radius;

  _TabBarIndicator(Color color, this.radius)
      : _paint = Paint()
          ..color = color
          ..isAntiAlias = true;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Offset customOffset = offset +
        Offset(configuration.size!.width / 2,
            configuration.size!.height - radius - 5);

    canvas.drawRRect(
        RRect.fromRectAndCorners(
          Rect.fromCenter(
              center: customOffset,
              width: configuration.size!.width,
              height: 2),
          topLeft: Radius.circular(radius),
          topRight: Radius.circular(radius),
        ),
        _paint);
  }
}
