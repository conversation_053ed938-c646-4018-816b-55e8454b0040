import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/multi_option.dart';

class ResultMultiOptionsWidget extends StatelessWidget {
  const ResultMultiOptionsWidget({
    super.key,
    required this.question,
    required this.isInExam,
  });

  final QuestionResult question;
  final bool isInExam;
  @override
  Widget build(BuildContext context) {
    final options = question.options ?? [];
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: ListView.separated(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return MultipleOption(
              status: options[index].resultStatus,
              index: index,
              option: options[index],
              isInExam: isInExam,
            );
          },
          separatorBuilder: (context, index) => const SizedBox(height: 12),
          itemCount: options.length),
    );
  }
}
