import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/app/extensions/int.dart';
import 'package:scrumpass_exam_simulator/app/utils/datetime.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/app/widgets/gradient_border_container.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/result_detail/result_detail_controller.dart';

class ResultCardMock extends GetView<ResultDetailController> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: GradientBorderContainer(
          bgGradient: LinearGradient(
              colors: controller.result.pass == true
                  ? AppColors.mockPassBgGradient
                  : AppColors.mockFailBgGradient,
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter),
          gradient: LinearGradient(
              colors: controller.result.pass == true
                  ? AppColors.mockPassBorderGradient
                  : AppColors.mockFailBorderGradient,
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter),
          radius: 16,
          boxShadow: [
            BoxShadow(
                color: HexColor("2578C4").withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2))
          ],
          child: _MainContent(pass: controller.result.pass ?? false)),
    );
  }
}

class _MainContent extends StatelessWidget {
  const _MainContent({required this.pass});

  final bool pass;

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            _Header(
              pass: pass,
            ),
            const SizedBox(height: 20),
            _StatisticBar(),
            const SizedBox(height: 12),
            _StatisticRow(),
          ],
        ));
  }
}

class _StatisticRow extends GetView<ResultDetailController> {
  const _StatisticRow();

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _StatisticItem(
            label: LocaleKeys.overall.tr,
            count: controller.overallScore,
            color: AppColors.resultOverall),
        _StatisticItem(
            label: LocaleKeys.correct.tr,
            count: controller.corrects.length.toString(),
            color: AppColors.resultCorrect),
        _StatisticItem(
            label: LocaleKeys.incorrect.tr,
            count: controller.incorrects.length.toString(),
            color: AppColors.red2),
        _StatisticItem(
            label: LocaleKeys.unanswered.tr,
            count: controller.notDone.length.toString(),
            color: AppColors.neutral2),
      ],
    );
  }
}

class _StatisticItem extends StatelessWidget {
  const _StatisticItem({
    required this.label,
    required this.count,
    required this.color,
  });

  final String label;
  final String count;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 20,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.xSmallRegular
                      .copyWith(color: AppColors.textHint),
                ),
                Text(
                  count,
                  style: AppTextStyles.smallBold
                      .copyWith(color: AppColors.neutral9),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _StatisticBar extends GetView<ResultDetailController> {
  const _StatisticBar();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 12,
      child: Row(
        children: [
          if (controller.corrects.isNotEmpty)
            _BarItem(
                color: AppColors.resultCorrect,
                value: controller.corrects.length),
          if (controller.incorrects.isNotEmpty)
            _BarItem(
                color: AppColors.red2, value: controller.incorrects.length),
          if (controller.notDone.isNotEmpty)
            _BarItem(
                color: AppColors.neutral2, value: controller.notDone.length),
        ],
      ),
    );
  }
}

class _BarItem extends StatelessWidget {
  const _BarItem({required this.color, required this.value});

  final Color color;
  final int value;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: value,
      child: Padding(
        padding: const EdgeInsets.only(right: 2),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(3),
          ),
          child: Container(),
        ),
      ),
    );
  }
}

class _Header extends GetView<ResultDetailController> {
  const _Header({required this.pass});

  final bool pass;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: SizedBox(
              width: 40,
              height: 40,
              child: DecoratedBox(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: controller.testType.bgColor),
                child: Center(
                  child: SvgPicture.asset(controller.testType.icon),
                ),
              )),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 9),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Utils.parseTestPracticeName(controller.result.testName),
                  style:
                      AppTextStyles.baseBold.copyWith(color: AppColors.text1st),
                ),
                const SizedBox(height: 4),
                Text(
                  '${controller.timeDoTest.toTimeTextString} • ${controller.time.dateString}',
                  style: AppTextStyles.xSmallMedium
                      .copyWith(color: AppColors.textDisabled),
                )
              ],
            ),
          ),
        ),
        SizedBox(
          width: 64,
          height: 58,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              controller.result.pass == true
                  ? Assets.images.examPassed.image(width: 64, height: 58)
                  : Assets.images.examFailed.image(width: 64, height: 58),
              Positioned(
                  top: 8,
                  child: Text(
                    controller.result.score.roundedPrecisionToString(0),
                    style: AppTextStyles.baseBold.copyWith(
                        fontSize: 19,
                        color: controller.result.pass == true
                            ? AppColors.mockScorePassText
                            : AppColors.mockScoreFailText),
                  ))
            ],
          ),
        )
      ],
    );
  }
}
