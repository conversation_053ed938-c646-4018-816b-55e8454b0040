import 'package:blur/blur.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/image_viewer_page.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/premium_button.dart';

class ResultExplanationWidget extends StatefulWidget {
  const ResultExplanationWidget({super.key, required this.text});

  final String text;

  @override
  State<ResultExplanationWidget> createState() =>
      _ResultExplanationWidgetState();
}

class _ResultExplanationWidgetState extends State<ResultExplanationWidget> {
  final ExpandableController controller = ExpandableController();
  double turns = 0.0;

  void _changeRotation() {
    setState(() {
      if (turns == 0.0) {
        turns += 0.5;
      } else {
        turns -= 0.5;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ExpandablePanel(
      controller: controller,
      theme: ExpandableThemeData(
          hasIcon: false,
          tapHeaderToExpand: false,
          headerAlignment: ExpandablePanelHeaderAlignment.center),
      collapsed: const SizedBox(),
      header: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          controller.toggle();
          _changeRotation();
        },
        child: SizedBox(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  controller.expanded
                      ? LocaleKeys.hideExplanation.tr
                      : LocaleKeys.showExplanation.tr,
                  style: AppTextStyles.smallSemiBold
                      .copyWith(color: AppColors.textLink),
                ),
                const SizedBox(width: 4),
                AnimatedRotation(
                  turns: turns,
                  duration: const Duration(milliseconds: 300),
                  child: RotatedBox(
                      quarterTurns: 2, child: Assets.images.altArrowUp.svg()),
                ),
              ],
            ),
          ),
        ),
      ),
      expanded: Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Global.isPremium == false
                ? Container(
                    margin: EdgeInsets.only(top: 6),
                    height: 120,
                    child: Blur(
                      colorOpacity: 0.4,
                      blur: 7.86,
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: Colors.transparent,
                      ),
                    ),
                  )
                : Html(
                    data: widget.text,
                    shrinkWrap: true,
                    style: {
                      "body": Style(
                          fontSize: FontSize(14),
                          fontWeight: FontWeight.w500,
                          fontFamily: 'Inter',
                          color: AppColors.textHint,
                          margin: Margins.zero,
                          padding: HtmlPaddings.only(bottom: 0),
                          lineHeight: const LineHeight(20 / 14)),
                      "p": Style(
                          padding: HtmlPaddings.zero, margin: Margins.zero),
                      "img": Style(
                        width: Width(
                            MediaQuery.of(context).size.width - 50, Unit.px),
                      ),
                    },
                    extensions: [
                      OnImageTapExtension(
                        onImageTap: (src, imgAttributes, element) {
                          if (src != null && context.mounted) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => ImageViewerPage(src: src),
                              ),
                            );
                          }
                        },
                      ),
                    ],
                  ),
          ),
          if (Global.isPremium == false) PremiumButton()
        ],
      ),
    );
  }
}
