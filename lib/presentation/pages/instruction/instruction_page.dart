import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/instruction/instruction_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/image_viewer_page.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/page_header.dart';

class InstructionPage extends StatelessWidget {
  const InstructionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: InstructionController(),
      builder: (controller) => Scaffold(
        body: Container(
          width: Get.width,
          height: Get.height,
          child: Stack(
            children: [
              Positioned(
                top: 0,
                child: PageHeader(),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: Assets.images.light.svg(),
              ),
              Positioned(
                right: 18,
                top: 32,
                child: Assets.images.goldenStar.svg(
                  width: 18,
                  height: 18,
                ),
              ),
              Positioned(
                right: 140,
                top: 18,
                child: Assets.images.goldenStar.svg(
                  width: 16,
                  height: 16,
                ),
              ),
              Positioned(
                top: 60,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    width: MediaQuery.sizeOf(context).width - 100,
                    child: Text(
                      Utils.parseTestPracticeName(controller.testName),
                      style: AppTextStyles.xLargeBold
                          .copyWith(color: AppColors.textInverseTitle),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: Assets.images.light.svg(),
              ),
              SingleChildScrollView(
                child: Container(
                  height: (MediaQuery.of(Get.context!).orientation ==
                          Orientation.landscape)
                      ? Get.height
                      : Get.height - 110,
                  margin: EdgeInsets.only(top: 110),
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(32),
                      topRight: Radius.circular(32),
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Assets.images.blueFlag.svg(width: 18),
                          SizedBox(width: 8),
                          Text(
                            LocaleKeys.examDetail.tr,
                            style: AppTextStyles.largeBold.copyWith(
                              color: AppColors.textTitle,
                            ),
                          ),
                        ],
                      ),
                      ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.only(top: 16, bottom: 20),
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: controller.infoItems.length,
                        itemBuilder: (context, index) =>
                            quizDetailsWidget(controller.infoItems[index]),
                        separatorBuilder: (context, index) => SizedBox(
                          height: 14,
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: Get.width,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: AppColors.borderLine,
                                          width: 1),
                                      borderRadius: BorderRadius.circular(12),
                                      color: AppColors.neutral1,
                                    ),
                                    clipBehavior: Clip.hardEdge,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          width: Get.width,
                                          color: AppColors.neutral2,
                                          padding: const EdgeInsets.all(8.0),
                                          child: Center(
                                            child: Text(
                                              LocaleKeys.instruction.tr,
                                              style: AppTextStyles.baseBold
                                                  .copyWith(
                                                color: AppColors.textTitle,
                                              ),
                                            ),
                                          ),
                                        ),
                                        Container(
                                          margin: EdgeInsets.all(8),
                                          padding: EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 16),
                                          decoration: BoxDecoration(
                                            color: AppColors.bgWarningSurface,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Row(
                                            children: [
                                              Assets.images.warning
                                                  .svg(width: 18),
                                              SizedBox(width: 8),
                                              Text(
                                                "${LocaleKeys.pleaseRead.tr}!",
                                                style: AppTextStyles
                                                    .smallRegular
                                                    .copyWith(
                                                  color: AppColors.textWarning,
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                        Container(
                                          margin: EdgeInsets.symmetric(
                                            vertical: 0,
                                            horizontal: 12,
                                          ),
                                          constraints: BoxConstraints(
                                            maxHeight:
                                                constraints.maxHeight - 109,
                                          ),
                                          child: SingleChildScrollView(
                                            child: Transform.translate(
                                              offset: Offset(0, -8),
                                              child: Html(
                                                shrinkWrap: true,
                                                data: controller.description
                                                    .replaceAll(
                                                        "Please read carefully.<br />",
                                                        ""),
                                                style: {
                                                  "body": Style(
                                                    fontSize: FontSize(14),
                                                    fontWeight: FontWeight.w500,
                                                    fontFamily: 'Inter',
                                                    color: AppColors.textHint,
                                                    margin: Margins.zero,
                                                    padding: HtmlPaddings.only(
                                                        bottom: 12),
                                                    lineHeight:
                                                        const LineHeight(1.5),
                                                  ),
                                                  "p": Style(
                                                    margin: Margins.zero,
                                                    padding: HtmlPaddings.zero,
                                                  ),
                                                  "img": Style(
                                                    width: Width(
                                                        MediaQuery.of(context)
                                                                .size
                                                                .width -
                                                            50,
                                                        Unit.px),
                                                  )
                                                },
                                                extensions: [
                                                  OnImageTapExtension(
                                                    onImageTap: (src,
                                                        imgAttributes,
                                                        element) {
                                                      if (src != null &&
                                                          context.mounted) {
                                                        Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                            builder: (_) =>
                                                                ImageViewerPage(
                                                                    src: src),
                                                          ),
                                                        );
                                                      }
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                      SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsets.only(top: 16, bottom: 24),
                        child: PrimaryButton(
                          text: LocaleKeys.startExam.tr,
                          onTap: () => Get.toNamed(RouterName.doTest,
                              arguments: Get.arguments),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: 60,
                left: 22,
                child: GestureDetector(
                  child: Assets.images.arrowLeft.svg(
                    colorFilter: ColorFilter.mode(
                      Colors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                  onTap: () => Get.back(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget quizDetailsWidget(ExamInfoItem detail) {
    return Container(
      height: 45,
      decoration: BoxDecoration(
        color: detail.bgColor,
        borderRadius: BorderRadius.circular(12),
        border: Border(
          left: BorderSide(
            width: 2,
            color: detail.borderColor,
          ),
        ),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Row(
          children: [
            detail.icon,
            SizedBox(width: 8),
            Expanded(
                child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  detail.title,
                  style: AppTextStyles.smallMedium.copyWith(
                    color: AppColors.textHint,
                  ),
                ),
                Text(
                  detail.subtitle,
                  style: AppTextStyles.smallBold.copyWith(
                    color: detail.textColor,
                  ),
                )
              ],
            ))
          ],
        ),
      ),
    );
  }
}
