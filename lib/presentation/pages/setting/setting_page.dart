import 'package:expandable/expandable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/datetime.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/system_log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/switch.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/similar_app/similar_app.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/widgets/app_info.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/widgets/app_setting.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/widgets/contact_items.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/widgets/support_banner.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/widgets/user_setting.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/premium_banner.dart';

class SettingPage extends StatelessWidget {
  const SettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        init: SettingController(),
        builder: (controller) {
          return DecoratedBox(
            decoration: BoxDecoration(color: AppColors.bgMain01),
            child: Stack(children: [
              Positioned(
                  top: 0,
                  right: 0,
                  child: SizedBox(
                    width: 276,
                    height: 276,
                    child: Assets.images.settingBgIcon.image(),
                  )),
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 14),
                        Text(LocaleKeys.settings.tr,
                            style: AppTextStyles.xLargeBold),
                        const SizedBox(height: 16),
                        if (!Global.isPremium) ...{
                          PremiumBanner(
                            screenLocation: EventLogConstants.settingsScreen,
                          ),
                          const SizedBox(height: 16)
                        },
                        GetBuilder<SettingController>(
                          id: SettingConstants.userSettingKey,
                          builder: (_) {
                            return UserSetting();
                          },
                        ),
                        const SizedBox(height: 20),
                        GetBuilder<SettingController>(
                          id: SettingConstants.appSettingKey,
                          builder: (_) {
                            return AppSetting();
                          },
                        ),
                        const SizedBox(height: 20),
                        SupportBanner(),
                        const SizedBox(height: 20),
                        GetBuilder<SettingController>(
                          id: SettingConstants.relativeAppKey,
                          builder: (_) {
                            return SimilarApp(
                              screenLocation: EventLogConstants.settingsScreen,
                              relativeApps: controller.relativeApps,
                            );
                          },
                        ),
                        const SizedBox(height: 20),
                        AppInfo(),
                        const SizedBox(height: 24),
                        ContactItems(),
                        const SizedBox(height: 20),
                        const TabBarSpace(),
                      ],
                    ),
                  ),
                ),
              )
            ]),
          );
        });
  }
}

class SettingBox extends StatelessWidget {
  const SettingBox({
    super.key,
    required this.items,
  });

  final List<SettingItemModel> items;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.bgWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: BoxShadows.settingBoxShadow,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: ListView.separated(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (context, index) {
              if (items[index] is ReminderItemModel) {
                final item = items[index] as ReminderItemModel;
                return ReminderWidget(item: item);
              }
              return SettingItem(item: items[index]);
            },
            separatorBuilder: (context, index) => const SizedBox(height: 8),
            itemCount: items.length),
      ),
    );
  }
}

class SettingItem extends StatelessWidget {
  const SettingItem({
    super.key,
    required this.item,
  });

  final SettingItemModel item;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: item.onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            item.icon,
            const SizedBox(width: 6),
            Text(item.title,
                style: AppTextStyles.smallRegular
                    .copyWith(color: AppColors.textHint)),
            SizedBox(width: 16),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (item.prefixValueIcon != null) ...{
                    item.prefixValueIcon!,
                    SizedBox(width: 4.w),
                  },
                  Flexible(
                    child: item.valueWidget ??
                        Text(
                      item.value ?? '',
                      style: AppTextStyles.smallMedium
                          .copyWith(color: AppColors.text1st),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
            ),
            if (item.hasArrow) Assets.images.altArrowRight.svg()
          ],
        ),
      ),
    );
  }
}

class ReminderWidget extends GetView<SettingController> {
  const ReminderWidget({super.key, required this.item});

  final ReminderItemModel item;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: ExpandablePanel(
        controller: controller.expandableController,
        theme: ExpandableThemeData(hasIcon: false, tapHeaderToExpand: false),
        collapsed: const SizedBox(),
        header: GestureDetector(
          onTap: item.onTap,
          child: Row(
            children: [
              item.icon,
              const SizedBox(width: 4),
              Expanded(
                  child: Text(item.title,
                      style: AppTextStyles.smallRegular
                          .copyWith(color: AppColors.textHint))),
              if (item.switchValue)
                Text(
                  controller.reminderTime!.timeString,
                  style: AppTextStyles.smallMedium
                      .copyWith(color: AppColors.text1st),
                ),
              const SizedBox(width: 8),
              GetBuilder<SettingController>(
                id: SettingConstants.reminderSwitchKey,
                builder: (_) {
                  return SwitchWidget(
                      value: item.switchValue,
                      onChanged: (value) {
                        item.onTapSwitch?.call(value);
                      });
                },
              )
            ],
          ),
        ),
        expanded: SystemLogEvent(
          eventName: EventLogConstants.clickRepeat,
          child: GestureDetector(
            onTap: () => controller.openReminderBottomSheet(null),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  const SizedBox(width: 24),
                  Expanded(
                      child: Text(
                    LocaleKeys.repeat.tr,
                    style: AppTextStyles.smallRegular
                        .copyWith(color: AppColors.textHint),
                  )),
                  Text(
                    controller.dayReminders.length == 7
                        ? LocaleKeys.daily.tr
                        : '${controller.dayReminders.length} ${LocaleKeys.daysPerWeek.tr}',
                    style: AppTextStyles.smallMedium
                        .copyWith(color: AppColors.text1st),
                  ),
                  const SizedBox(width: 4),
                  Assets.images.altArrowRight.svg()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class SettingItemModel {
  SettingItemModel({
    required this.title,
    this.value,
    this.hasArrow = true,
    required this.icon,
    this.onTap,
    this.id,
    this.eventName,
    this.prefixValueIcon,
    this.valueWidget,
  });
  final String title;
  final String? value;
  final bool hasArrow;
  final SvgPicture icon;
  final SvgPicture? prefixValueIcon;
  final Function()? onTap;
  final String? id;
  final String? eventName;
  final Widget? valueWidget;
}

class ReminderItemModel extends SettingItemModel {
  ReminderItemModel({
    required super.title,
    required super.icon,
    required super.onTap,
    this.onTapSwitch,
    this.switchValue = false,
  });

  final Function(bool)? onTapSwitch;
  final bool switchValue;
}
