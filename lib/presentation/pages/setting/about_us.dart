import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/back_button.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class AboutUsPage extends StatelessWidget {
  const AboutUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          LocaleKeys.aboutUs.tr,
          style: AppTextStyles.xLargeBold.copyWith(color: AppColors.textTitle),
        ),
        centerTitle: true,
        leading: BackButtonWidget(),
        leadingWidth: 40,
        backgroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Html(
            data: LocaleKeys.aboutUsContent.tr,
            style: {
              "body": Style(
                  fontSize: FontSize(14),
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color: AppColors.aboutUsContent,
                  margin: Margins.zero,
                  padding: HtmlPaddings.only(bottom: 12),
                  lineHeight: const LineHeight(20 / 14)),
              "p": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
            },
          ),
        ),
      ),
    );
  }
}
