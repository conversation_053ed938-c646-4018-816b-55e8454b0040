import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/system_log_event.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:url_launcher/url_launcher.dart';

class SupportBanner extends StatelessWidget {
  const SupportBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return SystemLogEvent(
      eventName: EventLogConstants.clickSupportBanner,
      child: InkWell(
        onTap: () {
          launchUrl(Uri.parse(Constants.messenger),
              mode: LaunchMode.platformDefault);
        },
        child: DecoratedBox(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: AppColors.bgSupportBanner,
              boxShadow: BoxShadows.settingBoxShadow),
          child: SizedBox(
            width: double.infinity,
            child: Stack(
              children: [
                Positioned(
                  right: 0,
                  top: 0,
                  child: Assets.images.supportBannerIcon.image(width: 343),
                ),
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(LocaleKeys.needSupport.tr,
                          style: AppTextStyles.baseBold
                              .copyWith(color: AppColors.text1st)),
                      const SizedBox(height: 12),
                      DecoratedBox(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: AppColors.bgContactBtn),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  LocaleKeys.contactUs.tr,
                                  style: AppTextStyles.smallRegular.copyWith(
                                      color: AppColors.textInverseTitle),
                                ),
                                const SizedBox(width: 4),
                                Assets.images.arrowRight.svg(
                                  colorFilter: ColorFilter.mode(
                                    AppColors.icInverse,
                                    BlendMode.srcIn,
                                  ),
                                )
                              ],
                            ),
                          ))
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
