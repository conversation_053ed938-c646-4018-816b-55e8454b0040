import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/datetime.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_action.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_empty.dart';
import 'package:scrumpass_exam_simulator/app/widgets/date_picker.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/setting_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/widgets/bottomsheet/change_name.dart';

class UserSetting extends GetView<SettingController> {
  const UserSetting({super.key});

  @override
  Widget build(BuildContext context) {
    final items = [
      SettingItemModel(
        icon: Assets.images.userRounded.svg(),
        title: LocaleKeys.name.tr,
        eventName: EventLogConstants.clickName,
        value: Global.getUsername,
        onTap: () {
          Popup.instance.showBottomSheet(BottomSheetEmpty(
            title: LocaleKeys.bottomsheetNameTitle.tr,
            child: ChangeName(
              textController: controller.textController,
            ),
          ));
        },
      ),
      SettingItemModel(
        icon: Assets.images.diplomaVerified.svg(),
        title: LocaleKeys.cert.tr,
        value: Constants.certName,
        hasArrow: false,
      ),
      SettingItemModel(
        icon: Assets.images.timeCalendar.svg(),
        title: LocaleKeys.testDate.tr,
        value: controller.testDate?.dateString,
        eventName: EventLogConstants.clickChooseATestDate,
        onTap: () {
          Popup.instance.showBottomSheet(BottomSheetAction(
            title: LocaleKeys.bottomsheetTestDateTitle.tr,
            child: DatePicker(
              onDateChanged: controller.onChangeTestDate,
              initDate: controller.testDate,
            ),
            onDone: controller.saveTestDate,
            eventLocationName: EventLogConstants.btm_chooseatestdate,
          ));
        },
      ),
      SettingItemModel(
          icon: Assets.images.crownLine.svg(),
          prefixValueIcon:
              Global.isPremium ? Assets.images.king1.svg(width: 16) : null,
          title: LocaleKeys.account.tr,
          value: Global.isPremium ? LocaleKeys.premium.tr : LocaleKeys.free.tr,
          eventName: EventLogConstants.viewAccountStatus,
          hasArrow: Global.isPremium ? false : true,
          valueWidget: Global.isPremium
              ? RichText(
                  text: TextSpan(
                  children: [
                    TextSpan(
                      text: LocaleKeys.premium.tr,
                      style: AppTextStyles.smallMedium,
                    ),
                    TextSpan(
                      text:
                          ' (${Global.getSubscriptionType != null ? Global.getSubscriptionType?.displayText : ''})',
                      style: AppTextStyles.smallMedium
                          .copyWith(color: AppColors.textBtnDisabled),
                    ),
                  ],
                ))
              : null,
          onTap: () {
            if (Global.isPremium == false) {
              Get.toNamed(RouterName.premium);
            }
          }),
    ];
    return SettingBox(items: items);
  }
}
