import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/setting_page.dart';

class AppInfo extends GetView<SettingController> {
  const AppInfo({super.key});

  @override
  Widget build(BuildContext context) {
    final items = [
      SettingItemModel(
        icon: Assets.images.usersGroupTwoRounded.svg(),
        title: LocaleKeys.aboutUs.tr,
        eventName: EventLogConstants.clickAboutUs,
        onTap: () {
          Get.toNamed(RouterName.aboutUs);
        },
      ),
      SettingItemModel(
        icon: Assets.images.starsMinimalistic.svg(),
        title: LocaleKeys.ratingApp.tr,
        onTap: controller.openAppReview,
        eventName: EventLogConstants.clickRatingApp,
      ),
      SettingItemModel(
        icon: Assets.images.share.svg(),
        title: LocaleKeys.sharingApp.tr,
        onTap: () {
          controller.openShare(context);
        },
        eventName: EventLogConstants.clickSharingApp,
      ),
      SettingItemModel(
        icon: Assets.images.shieldPlus.svg(),
        title: LocaleKeys.privacyPolicy.tr,
        onTap: controller.openPrivacyPolicy,
        eventName: EventLogConstants.clickPrivacyPolicy,
      ),
      SettingItemModel(
        icon: Assets.images.smartphone2.svg(),
        title: LocaleKeys.version.tr,
        hasArrow: false,
        value: Global.appVersion.substring(0, Global.appVersion.length - 2),
        eventName: EventLogConstants.clickVersion,
      ),
    ];
    return SettingBox(items: items);
  }
}
