import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/social_media_log_event.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactItems extends StatelessWidget {
  const ContactItems({super.key});

  @override
  Widget build(BuildContext context) {
    final items = [
      _Item(
          channel: SocialMediaChannel.facebook,
          onTap: () {
            launchUrl(Uri.parse(Constants.facebook),
                mode: LaunchMode.externalApplication);
          },
          icon: Assets.images.facebook1.svg(width: 24)),
      _Item(
          channel: SocialMediaChannel.messenger,
          onTap: () {
            launchUrl(Uri.parse(Constants.messenger),
                mode: LaunchMode.platformDefault);
          },
          icon: Assets.images.messenger1.svg(width: 24)),
      _Item(
          channel: SocialMediaChannel.website,
          onTap: () {
            launchUrl(Uri.parse(Constants.website),
                mode: LaunchMode.externalApplication);
          },
          icon: Assets.images.global.svg(width: 24)),
      _Item(
          channel: SocialMediaChannel.facebook,
          onTap: () {
            launchUrl(Uri.parse(Constants.facebookGroup),
                mode: LaunchMode.externalApplication);
          },
          icon: Assets.images.settingUsersGroupTwoRounded.svg(width: 24)),
      _Item(
          channel: SocialMediaChannel.email,
          onTap: () {
            launchUrl(Uri(scheme: 'mailto', path: Constants.mail),
                mode: LaunchMode.externalApplication);
          },
          icon: Assets.images.letter.svg(width: 24)),
    ];
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
            width: 21,
            child: Divider(
              height: 1,
              color: AppColors.border1st,
            )),
        const SizedBox(width: 7),
        SizedBox(
          height: 34,
          child: ListView.separated(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) {
                return items[index];
              },
              separatorBuilder: (context, index) => const SizedBox(width: 14),
              itemCount: items.length),
        ),
        const SizedBox(width: 7),
        SizedBox(
            width: 21,
            child: Divider(
              height: 1,
              color: AppColors.border1st,
            )),
      ],
    );
  }
}

class _Item extends StatelessWidget {
  const _Item({
    required this.onTap,
    required this.icon,
    required this.channel,
  });

  final Function() onTap;
  final SvgPicture icon;
  final SocialMediaChannel channel;

  @override
  Widget build(BuildContext context) {
    return SocialMediaLogEvent(
      eventName: EventLogConstants.viewSocialMedia,
      params: SocialMediaLogEventParams(mediaChannel: channel),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(5),
          child: icon,
        ),
      ),
    );
  }
}
