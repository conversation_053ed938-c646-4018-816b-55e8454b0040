import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/selection_bottomsheet.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_empty.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/setting_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/widgets/bottomsheet/font_size.dart';

class AppSetting extends GetView<SettingController> {
  const AppSetting({super.key});

  @override
  Widget build(BuildContext context) {
    final items = [
      SettingItemModel(
        icon: Assets.images.finetuning.svg(),
        title: LocaleKeys.trainingMode.tr,
        value: controller.trainingMode.value.text,
        eventName: EventLogConstants.clickTrainingMode,
        onTap: () async {
          final value =
              await Popup.instance.showBottomSheet(BottomSheetSelectionWidget(
            title: LocaleKeys.bottomsheetTrainingModeTitle.tr,
            beforeWidget: TrainingInfo(),
            data: TrainingMode.values
                .map((e) => BottomSheetModel(
                      title: e.title,
                      value: e,
                      isSelected: controller.trainingMode.value == e,
                      description: e.description,
                      bgColor: e.color,
                      titleStyle: AppTextStyles.smallSemiBold,
                      eventName: e.eventName,
                    ))
                .toList(),
          ));
          if (value != null) {
            controller.saveTrainingMode(value);
          }
        },
      ),
      SettingItemModel(
        icon: Assets.images.language1.svg(),
        title: LocaleKeys.language.tr,
        value: controller.locale.value.text,
        eventName: EventLogConstants.clickLanguage,
        onTap: () async {
          final value =
              await Popup.instance.showBottomSheet(BottomSheetSelectionWidget(
                  ignoreLog: true,
                  title: LocaleKeys.bottomsheetLanguageTitle.tr,
                  data: Language.values
                      .map((e) => BottomSheetModel(
                            title: e.text,
                            value: e,
                            isSelected: controller.locale.value == e,
                            iconAsset: e.icon,
                            eventName: EventLogConstants.selectTypeLanguage,
                          ))
                      .toList()));
          if (value != null) {
            controller.saveLanguage(value);
          }
        },
      ),
      SettingItemModel(
          icon: Assets.images.text.svg(),
          title: LocaleKeys.textSize.tr,
          value: controller.fontSize.toStringNoTrailingZeros(),
          eventName: EventLogConstants.clickTextSize,
          onTap: () async {
            final result =
                await Popup.instance.showBottomSheet(BottomSheetEmpty(
              title: LocaleKeys.bottomsheetFontSizeTitle.tr,
              child: FontSizeWidget(
                value: controller.fontSize,
              ),
            ));
            if (result != null) {
              controller.saveFontSize(result);
            }
          }),
      ReminderItemModel(
          switchValue: controller.isActiveReminder,
          icon: Assets.images.bell.svg(),
          title: LocaleKeys.studyReminder.tr,
          onTap: () async {
            controller.openReminderBottomSheet(null);
          },
          onTapSwitch: controller.dayReminders.isEmpty
              ? controller.openReminderBottomSheet
              : controller.onToggleReminder),
    ];
    return SettingBox(items: items);
  }
}

class TrainingInfo extends StatelessWidget {
  const TrainingInfo({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Assets.images.danger.svg(),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              LocaleKeys.trainingModeInfo.tr,
              style: AppTextStyles.smallRegular
                  .copyWith(color: AppColors.selectionTitle),
            ),
          )
        ],
      ),
    );
  }
}
