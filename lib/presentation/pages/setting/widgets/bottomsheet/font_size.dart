import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/slider.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';

class FontSizeWidget extends StatefulWidget {
  const FontSizeWidget({super.key, required this.value});

  final double value;

  @override
  State<FontSizeWidget> createState() => _FontSizeWidgetState();
}

class _FontSizeWidgetState extends State<FontSizeWidget> {
  late double _value;

  @override
  void initState() {
    _value = widget.value;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          GetBuilder<SettingController>(
            id: SettingConstants.textSizeKey,
            builder: (controller) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                child: Text(
                  LocaleKeys.fontSizeText.tr,
                  textAlign: TextAlign.center,
                  style: AppTextStyles.xLargeMedium.copyWith(fontSize: _value),
                ),
              );
            },
          ),
          SliderWidget(
              value: _value,
              onValueChange: (value) {
                setState(() {
                  _value = value;
                });
              },
              min: 14,
              max: 24,
              interval: 2,
              stepSize: 2),
          const SizedBox(height: 20),
          PrimaryButton(
            text: LocaleKeys.confirm.tr,
            onTap: () => Get.back(result: _value),
          )
        ],
      ),
    );
  }
}
