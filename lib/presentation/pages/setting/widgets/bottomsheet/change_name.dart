import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/basic_log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/system_log_event.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';

class ChangeName extends StatelessWidget {
  const ChangeName({super.key, required this.textController});

  final TextEditingController textController;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          SystemLogEvent(
            eventName: EventLogConstants.typeName,
            child: TextField(
              controller: textController,
              autofocus: true,
              style:
                  AppTextStyles.baseRegular.copyWith(color: AppColors.text1st),
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.textHint, width: 1),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.textHint, width: 1),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      BorderSide(color: AppColors.icBtn2ndDefault, width: 1),
                ),
                contentPadding: EdgeInsets.all(16),
                hintText: LocaleKeys.placeholderName.tr,
              ),
            ),
          ),
          const SizedBox(height: 24),
          GetBuilder<SettingController>(
            id: SettingConstants.usernameBottomsheetKey,
            builder: (controller) {
              return BasicLogEvent(
                eventName: EventLogConstants.clickBtnConfirm,
                params: BasicLogEventParams(
                  eventName: EventLogConstants.clickBtnConfirm,
                  screenLocation: EventLogConstants.settingsScreen,
                  locationType: LocationType.bottomsheet,
                  locationName: EventLogConstants.btm_changename,
                ),
                child: PrimaryButton(
                  text: LocaleKeys.confirm.tr,
                  onTap: () {
                    controller.saveUserName();
                    Get.back();
                  },
                  isEnable: textController.text.isNotEmpty,
                ),
              );
            },
          )
        ],
      ),
    );
  }
}
