import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/checkbox.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/system_log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/switch.dart';
import 'package:scrumpass_exam_simulator/app/widgets/time_picker.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/other/reminder_params.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/reminder_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';

class ReminderBottomSheet extends GetView<SettingController> {
  ReminderBottomSheet({super.key});

  final reminderController = ReminderController();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReminderController>(
      init: reminderController,
      id: ReminderConstants.reminderBottomSheetKey,
      initState: (_) {
        reminderController.initData(ReminderParams(
            selectedDays: controller.dayReminders,
            reminderTime: controller.reminderTime));
      },
      builder: (reminderController) => Column(
        children: [
          SizedBox(
            width: 244,
            child: TimePicker(
              onDateChanged: (date) {
                reminderController.onChangeTime(date);
              },
              initDate: reminderController.reminderTime,
            ),
          ),
          Divider(
            color: AppColors.borderLine,
            height: 1,
            thickness: 1,
            indent: 16,
            endIndent: 16,
          ),
          GetBuilder<ReminderController>(
            id: ReminderConstants.reminderRepeatBottomSheetKey,
            builder: (_) => Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                          child: Text(
                        LocaleKeys.repeat.tr,
                        style: AppTextStyles.smallBold
                            .copyWith(color: AppColors.text1st),
                      )),
                      Text(
                        LocaleKeys.daily.tr,
                        style: AppTextStyles.smallMedium,
                      ),
                      const SizedBox(width: 8),
                      Obx(
                        () => SystemLogEvent(
                          eventName: EventLogConstants.selectDailyRepeat,
                          child: SwitchWidget(
                              value: reminderController.isDaily.value,
                              onChanged: reminderController.onToggleDaily),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  GridView.count(
                    shrinkWrap: true,
                    crossAxisCount: 2,
                    physics: NeverScrollableScrollPhysics(),
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                    childAspectRatio: 166 / 44,
                    children: ReminderDay.values
                        .map((e) => SystemLogEvent(
                              eventName: EventLogConstants.selectDay,
                              child: DayItem(
                                item: ReminderDayModel(
                                    day: e,
                                    isSelected: reminderController.dayReminders
                                        .contains(e)),
                                onChanged: reminderController.onTapReminderDate,
                              ),
                            ))
                        .toList(),
                  ),
                  const SizedBox(height: 16),
                  SystemLogEvent(
                    eventName: EventLogConstants.clickBtnConfirm,
                    child: PrimaryButton(
                      text: LocaleKeys.confirm.tr,
                      onTap: reminderController.onConfirm,
                      isEnable: reminderController.dayReminders.length > 0,
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}

class DayItem extends StatelessWidget {
  const DayItem({super.key, required this.item, required this.onChanged});

  final ReminderDayModel item;
  final Function(ReminderDayModel) onChanged;

  @override
  Widget build(BuildContext context) {
    return Obx(() => GestureDetector(
          onTap: () {
            _onChanged(null);
          },
          child: DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                  color: item.isSelected.value
                      ? AppColors.baseBlue6
                      : AppColors.borderLine,
                  width: 1.5),
              color: item.isSelected.value ? AppColors.baseBlue1 : null,
            ),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  CheckboxWidget(
                    value: item.isSelected.value,
                    onChanged: _onChanged,
                    activeColor: AppColors.baseBlue6,
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  Text(
                    item.day.text,
                    style: AppTextStyles.smallRegular
                        .copyWith(color: AppColors.selectionTitle),
                  )
                ],
              ),
            ),
          ),
        ));
  }

  void _onChanged(bool? value) {
    item.isSelected.value = !item.isSelected.value;
    onChanged.call(item);
  }
}
