import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/idle_detector.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/analytics_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/learning_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/review/review_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/setting_page.dart';
import 'package:scrumpass_exam_simulator/presentation/widgets/product_tour_widget.dart';

class HomePage extends GetView<HomeController> {
  const HomePage();

  @override
  HomeController get controller => Get.put(HomeController());

  @override
  Widget build(BuildContext context) {
    return ProductTourScaffold(
      controller: controller,
      child: IdleDetectorWidget(
        child: Scaffold(
          backgroundColor: Colors.white,
          body: Stack(
            children: [
              Column(
                children: [
                  Expanded(
                    child: TabBarView(
                      controller: controller.tabController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        LearningPage(
                          scrollController: controller.scrollController,
                        ),
                        ReviewPage(),
                        AnalyticsPage(),
                        SettingPage(),
                      ],
                    ),
                  ),
                  Divider(
                    color: AppColors.borderLine,
                    height: 1,
                    thickness: 1,
                  )
                ],
              ),
              TabBarWidget(controller: controller)
            ],
          ),
        ),
      ),
    );
  }
}

class TabBarWidget extends StatelessWidget {
  const TabBarWidget({
    super.key,
    required this.controller,
  });

  final HomeController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => IgnorePointer(
        ignoring: controller.tourController.ignorePointer.value,
        child: Align(
            alignment: FractionalOffset.bottomCenter,
            child: ClipRRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: 10.0,
                  sigmaY: 10.0,
                ),
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: AppColors.tabbarBg,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 22),
                    child: TabBar(
                      controller: controller.tabController,
                      onTap: controller.onTabChanged,
                      isScrollable: false,
                      physics: const NeverScrollableScrollPhysics(),
                      indicatorColor: Colors.transparent,
                      padding: const EdgeInsets.all(0),
                      labelPadding: const EdgeInsets.all(0),
                      indicatorPadding: const EdgeInsets.all(2),
                      dividerColor: Colors.transparent,
                      labelColor: AppColors.primary,
                      labelStyle: AppTextStyles.xSmallMedium,
                      unselectedLabelStyle: AppTextStyles.xSmallRegular,
                      unselectedLabelColor: AppColors.textHint,
                      tabs: controller.tabs
                          .map(
                            (item) => Obx(() => Tab(
                                  text: item.title,
                                  icon: SvgPicture.asset(
                                    width: 24,
                                    height: 24,
                                    item.iconPath(
                                        controller.selectedTab.value == item),
                                    colorFilter: ColorFilter.mode(
                                      item.iconColor(
                                          controller.selectedTab.value == item),
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                )),
                          )
                          .toList(),
                    ),
                  ),
                ),
              ),
            )),
      ),
    );
  }
}

class TabBarSpace extends StatelessWidget {
  const TabBarSpace({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox(height: 74);
  }
}
