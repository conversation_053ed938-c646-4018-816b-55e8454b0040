import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

enum HomeTab { learning, review, analytic, setting }

extension TabTitleExt on HomeTab {
  String get title {
    switch (this) {
      case HomeTab.learning:
        return LocaleKeys.learningTab.tr;
      case HomeTab.review:
        return LocaleKeys.reviewTab.tr;
      case HomeTab.analytic:
        return LocaleKeys.analyticsTab.tr;
      case HomeTab.setting:
        return LocaleKeys.settingTab.tr;
    }
  }

  String iconPath(bool isSelected) {
    switch (this) {
      case HomeTab.learning:
        return isSelected
            ? Assets.images.squareAcademicCapSvg.path
            : Assets.images.lineSquareAcademicCap.path;
      case HomeTab.review:
        return isSelected
            ? Assets.images.bookReview.path
            : Assets.images.bookSvg.path;
      case HomeTab.analytic:
        return isSelected
            ? Assets.images.chart2Svg.path
            : Assets.images.chart3Svg.path;
      case HomeTab.setting:
        return isSelected
            ? Assets.images.fillSettings.path
            : Assets.images.settingsSvg.path;
    }
  }

  Color iconColor(bool isSelected) {
    return isSelected ? AppColors.primary : AppColors.textHint;
  }
}
