import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/radio.dart';
import 'package:scrumpass_exam_simulator/domain/entities/subscripiton.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/premium/premium_controller.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SubcriptionItems extends GetView<PremiumController> {
  const SubcriptionItems({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx(() => Skeletonizer(
            enabled: controller.isLoading.value,
            child: ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) => Obx(() => SubscriptionItem(
                      onTap: () {
                        controller.onTapItem(index);
                      },
                      isSelected: index == controller.selectedIndex.value,
                      subscripitonEntity: controller.subscriptions[index],
                    )),
                separatorBuilder: (context, index) =>
                    const SizedBox(height: 20),
                itemCount: controller.subscriptions.length),
          )),
    );
  }
}

class SubscriptionItem extends StatelessWidget {
  const SubscriptionItem(
      {super.key,
      required this.isSelected,
      required this.subscripitonEntity,
      required this.onTap});

  final bool isSelected;
  final SubscriptionEntity subscripitonEntity;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          ConstrainedBox(
            constraints: const BoxConstraints(minHeight: 64),
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.selectedSubcriptionItemBg
                    : AppColors.neutral1,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                    color: isSelected
                        ? AppColors.selectedSubcriptionItemBorder
                        : AppColors.neutral5,
                    width: isSelected ? 1.5 : 1),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  children: [
                    AnimatedRadioButton(
                      isSelected: isSelected,
                      selectedColor: AppColors.yellow5,
                      notSelectedColor: AppColors.yellow5,
                      onChanged: (value) {},
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                        child: Text(
                      subscripitonEntity.duration,
                      style: AppTextStyles.baseBold
                          .copyWith(color: AppColors.elementTextNormal),
                    )),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${subscripitonEntity.displayPrice} ${subscripitonEntity.concurrency}',
                          style: AppTextStyles.smallBold
                              .copyWith(color: AppColors.elementTextNormal),
                        ),
                        if (subscripitonEntity.oldPrice != 0) ...{
                          const SizedBox(height: 4),
                          Text(
                            '${subscripitonEntity.displayOldPrice} ${subscripitonEntity.concurrency}',
                            style: AppTextStyles.xSmallRegular.copyWith(
                                color: AppColors.textHint,
                                decoration: TextDecoration.lineThrough),
                          ),
                        }
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            top: -10,
            left: 16,
            child: DecoratedBox(
              decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.selectedSubcriptionItemTag
                      : AppColors.notSelectedSubcriptionItemTag,
                  borderRadius: BorderRadius.circular(32)),
              child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Text(
                    subscripitonEntity.tag,
                    style: AppTextStyles.xxSmallRegular
                        .copyWith(color: AppColors.textWhite),
                  )),
            ),
          ),
        ],
      ),
    );
  }
}
