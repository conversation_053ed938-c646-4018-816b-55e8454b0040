import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class PremiumHeader extends StatelessWidget {
  const PremiumHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: Column(
              children: [
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: Text(
                    LocaleKeys.premiumHeader.tr,
                    style: AppTextStyles.xLargeBold
                        .copyWith(color: AppColors.elementTextNormal),
                  ),
                )
              ],
            ),
          ),
          Assets.images.premiumDiamon.image(height: 96, width: 96),
        ],
      ),
    );
  }
}
