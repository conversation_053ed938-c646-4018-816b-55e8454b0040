import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/premium/premium_controller.dart';

class TermPrivacy extends GetView<PremiumController> {
  const TermPrivacy({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            InkWell(
              onTap: controller.openPrivacyPolicy,
              child: Text(
                "${LocaleKeys.privacyPolicy.tr}",
                style: AppTextStyles.smallMedium
                    .copyWith(color: AppColors.textLink),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12),
              child: DecoratedBox(
                  decoration: BoxDecoration(
                      color: AppColors.icBtn2ndDefault, shape: BoxShape.circle),
                  child: SizedBox(
                    width: 4,
                    height: 4,
                  )),
            ),
            InkWell(
              onTap: controller.openTermsOfUse,
              child: Text(
                "${LocaleKeys.termsOfUse.tr}",
                style: AppTextStyles.smallMedium
                    .copyWith(color: AppColors.textLink),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12),
              child: DecoratedBox(
                  decoration: BoxDecoration(
                      color: AppColors.icBtn2ndDefault, shape: BoxShape.circle),
                  child: SizedBox(
                    width: 4,
                    height: 4,
                  )),
            ),
            InkWell(
              onTap: controller.restorePurchase,
              child: Text(
                LocaleKeys.restorePurchase.tr,
                style: AppTextStyles.smallMedium
                    .copyWith(color: AppColors.textLink),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
