import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class HowItWorksSection extends StatelessWidget {
  const HowItWorksSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Assets.images.premiumBr.image(width: 20),
              const SizedBox(width: 4),
              Expanded(
                  child: Text(
                LocaleKeys.howItWorks.tr,
                style: AppTextStyles.smallBold
                    .copyWith(color: AppColors.elementTextNormal),
              ))
            ],
          ),
          const SizedBox(height: 6),
          Text(
            LocaleKeys.trialDescription.tr,
            style:
                AppTextStyles.xSmallMedium.copyWith(color: AppColors.textHint),
          )
        ],
      ),
    );
  }
}
