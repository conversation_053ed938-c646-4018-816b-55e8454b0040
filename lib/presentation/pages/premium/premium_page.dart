import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/domain/entities/subscripiton.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/premium/premium_controller.dart';
import 'package:skeletonizer/skeletonizer.dart';

import 'widgets/benefit_box.dart';
import 'widgets/how_it_works.dart';
import 'widgets/premium_header.dart';
import 'widgets/subscription_items.dart';
import 'widgets/term_privacy.dart';

class PremiumPage extends GetView<PremiumController> {
  const PremiumPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LoaderOverlay(
        useDefaultLoading: false,
        overlayWidgetBuilder: (_) => const Center(
          child: SpinKitRing(
            color: Colors.white,
            lineWidth: 3.0,
            size: 50.0,
          ),
        ),
        overlayColor: Colors.black.withOpacity(0.8),
        child: DecoratedBox(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: AppColors.generalBgHeaderGradient,
              stops: [0.0, 0.4],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                top: 0,
                right: 0,
                child: Assets.images.premiumBgItem.image(width: 153),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      controller: controller.scrollController,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(height: 60),
                          PremiumHeader(),
                          const SizedBox(height: 20),
                          BenefitBox(),
                          const SizedBox(height: 20),
                          SubcriptionItems(),
                          const SizedBox(height: 18),
                          HowItWorksSection(),
                          const SizedBox(height: 16),
                          TermPrivacy(),
                          const SizedBox(height: 8),
                        ],
                      ),
                    ),
                  ),
                  DecoratedBox(
                    decoration: BoxDecoration(color: AppColors.premiumFooterbg),
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.all(16),
                          child: Obx(() => Skeletonizer(
                                enabled: controller.isLoading.value,
                                child: PrimaryButton(
                                    text: LocaleKeys.startTrial.tr,
                                    onTap: controller.onTapPurchase),
                              )),
                        ),
                        Obx(() => Skeletonizer(
                              enabled: controller.isLoading.value,
                              child: controller.subscriptions.isNotEmpty
                                  ? RenewText(
                                      subcriptionEntity:
                                          controller.currentSubscription,
                                    )
                                  : SizedBox(),
                            )),
                        const SizedBox(height: 33),
                      ],
                    ),
                  ),
                ],
              ),
              Positioned(
                top: 0,
                left: 0,
                child: Obx(() => SizedBox(
                      width: Get.width,
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage(Assets.images.premiumHeader.path),
                            fit: BoxFit.cover,
                            alignment: Alignment.topCenter,
                            opacity: controller.headerOpacity.value,
                          ),
                        ),
                        child: Padding(
                          padding:
                              EdgeInsets.only(top: 46, left: 10, bottom: 8),
                          child: Align(
                              alignment: Alignment.topLeft,
                              child: InkWell(
                                  onTap: () => Get.back(),
                                  child: Assets.images.close.svg(width: 36))),
                        ),
                      ),
                    )),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class RenewText extends GetView<PremiumController> {
  const RenewText({super.key, required this.subcriptionEntity});

  final SubscriptionEntity subcriptionEntity;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Text(
        '${LocaleKeys.renewText.tr} ${subcriptionEntity.displayString}',
        textAlign: TextAlign.center,
        style: AppTextStyles.xSmallMedium
            .copyWith(color: AppColors.elementTextNormal),
      ),
    );
  }
}
