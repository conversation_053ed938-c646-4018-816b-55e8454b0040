import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overlay_tooltip/overlay_tooltip.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/home/<USER>';

class HomeController extends GetxController
    with GetSingleTickerProviderStateMixin {
  HomeController();

  final List<HomeTab> tabs = [
    HomeTab.learning,
    HomeTab.review,
    HomeTab.analytic,
    HomeTab.setting
  ];

  late final TabController tabController;
  ScrollController scrollController = ScrollController();
  Rx<HomeTab> selectedTab = HomeTab.learning.obs;
  final TooltipController tooltipController = TooltipController();
  late TourController tourController;
  RxBool done = false.obs;

  @override
  void onInit() {
    if (Get.isRegistered<TourController>()) {
      tourController = Get.find<TourController>();
    } else {
      tourController = Get.put(
        TourController(),
        permanent: true,
      );
    }
    if (tourController.finishPrimaryTour.value == false) {
      tourController.ignorePointer.value = true;
    }
    tabController = TabController(length: tabs.length, vsync: this);
    tabController.addListener(() {
      onTabChanged(tabController.index);
    });
    tooltipController.onDone(() {
      done.value = true;
    });

    super.onInit();
  }

  @override
  void dispose() {
    tooltipController.dismiss();
    super.dispose();
  }

  void onTabChanged(int index) {
    handleTourUseCase(index);
    selectedTab.value = tabs[index];
  }

  void backToDefaultTab() {
    selectedTab.value = HomeTab.learning;
    tabController.animateTo(0);
  }

  void handleTourUseCase(int index) {
    if (index == 1 && tourController.finishTourReview.value == false) {
      tourController.ignorePointer.value = true;
    }
    if (index == 2 &&
        tourController.finishTourAnalyst.value == false &&
        Global.isPremium) {
      tourController.ignorePointer.value = true;
    }
    if (index == 1) {
      if (!tourController.finishTourReview.value) {
        Future.delayed(Duration(milliseconds: 500), () {
          if (selectedTab.value.index == 1) {
            tourController.showTourReview();
            tooltipController.start(10);
          }
        });
      }
      if (Get.isRegistered<ReviewController>()) {
        Get.find<ReviewController>().currentTab.value = ReviewTab.test;
      }
    }
  }

  void moveScreen({required double offset}) {
    final target = offset.clamp(0.0, scrollController.position.maxScrollExtent);

    scrollController.animateTo(
      target,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  Future<void> handleMoveTour({required double offset}) async {
    moveScreen(offset: offset);
    await Future.delayed(Duration(milliseconds: 500));
  }
}
