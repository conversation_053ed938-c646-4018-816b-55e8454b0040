import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/data/repositories/app_repo_impl.dart';
import 'package:scrumpass_exam_simulator/data/repositories/test_repo_impl.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_analytic_radar_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_list_mock_test_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_relative_app_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_num_category_usecase.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';

class HomeBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GetRelativeAppUsecase(Get.find<AppRepoImpl>()));
    Get.lazyPut(() => GetNumCategoryUsecase(Get.find<AppRepoImpl>()));
    Get.lazyPut(() => GetAnalyticRadarUsecase(Get.find<AppRepoImpl>()));
    Get.lazyPut(() => GetListMockTestUsecase(Get.find<TestRepositoryImpl>()));
    Get.lazyPut(() => HomeController());
  }
}
