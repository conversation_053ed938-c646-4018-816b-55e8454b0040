import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/data/models/other/exam_params.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';

class InstructionController extends BaseController {
  InstructionController() : super(ConnectionConcrete());
  late String testName;
  late int numQues;
  late int duration;
  late int passingScore;
  late String description;
  late List<ExamInfoItem> infoItems = [];
  @override
  void onInit() {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.light,
    ));
    getData();
    super.onInit();
  }

  getData() {
    if (Get.arguments is QuizEntity) {
      final args = Get.arguments as QuizEntity;
      testName = args.quizName;
      numQues = args.noq;
      duration = args.duration;
      passingScore = args.passPercentage;
      description = args.description;
    } else if (Get.arguments is ExamParams) {
      final args = Get.arguments as ExamParams;
      testName = args.mockTest!.quizName;
      numQues = args.mockTest!.noq;
      duration = args.mockTest!.duration;
      passingScore = args.mockTest!.passPercentage;
      description = args.mockTest!.description;
    }

    infoItems = [
      ExamInfoItem(
        icon: Assets.images.question.svg(width: 28),
        bgColor: HexColor("F58D00").withOpacity(0.06),
        borderColor: HexColor("F58D00"),
        textColor: HexColor("EC6608"),
        title: LocaleKeys.question.tr,
        subtitle: numQues.toString(),
      ),
      ExamInfoItem(
        icon: Assets.images.duration.svg(width: 28),
        bgColor: HexColor("EAE6FF").withOpacity(0.41),
        borderColor: HexColor("5243AA"),
        textColor: HexColor("4347AF"),
        title: LocaleKeys.duration.tr,
        subtitle: '${duration} ${LocaleKeys.minutes.tr}',
      ),
      ExamInfoItem(
        icon: Assets.images.passingScore.svg(width: 28),
        bgColor: HexColor("13A538").withOpacity(0.06),
        borderColor: AppColors.green5,
        textColor: HexColor("13A538"),
        title: LocaleKeys.passingScore.tr,
        subtitle: 'Above ${passingScore}%',
      ),
    ];
  }

  void onTapStartTest() {
    if (Get.isRegistered<LearningController>()) {
      Get.find<LearningController>().onTapQuickTest();
    }
  }
}

class ExamInfoItem {
  final Widget icon;
  final Color borderColor;
  final Color bgColor;
  final Color textColor;
  final String title;
  final String subtitle;

  ExamInfoItem({
    required this.icon,
    required this.bgColor,
    required this.borderColor,
    required this.textColor,
    required this.title,
    required this.subtitle,
  });

  ExamInfoItem copyWith({
    Widget? icon,
    Color? borderColor,
    Color? bgColor,
    Color? textColor,
    String? title,
    String? subtitle,
  }) {
    return ExamInfoItem(
      icon: icon ?? this.icon,
      borderColor: borderColor ?? this.borderColor,
      bgColor: bgColor ?? this.bgColor,
      textColor: textColor ?? this.textColor,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
    );
  }
}
