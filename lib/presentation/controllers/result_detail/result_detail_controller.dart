import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/other/exam_params.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';

class ResultDetailController extends BaseController
    with GetTickerProviderStateMixin, _DbHandlerMixin {
  ResultDetailController() : super(ConnectionConcrete());

  late TabController tabController;

  final List<ResultFilter> avalableFilter = [];

  final List<QuestionResult> questions = [];
  final List<QuestionResult> corrects = [];
  final List<QuestionResult> incorrects = [];
  final List<QuestionResult> bookmarked = [];
  final List<QuestionResult> notDone = [];
  final List<ResultFilter> filters = [
    ResultFilter.all,
    ResultFilter.incorrect,
    ResultFilter.correct,
    ResultFilter.bookmarked,
    ResultFilter.notDone,
  ];

  bool showBackBtn = false;

  late final DateTime time;
  late final String testName;
  late final int timeDoTest;
  late final TestType testType;
  late final ResultEntity result;

  late Mappr _mapper;

  @override
  void onInit() {
    getData();
    tabController = TabController(length: filters.length, vsync: this);
    _mapper = Get.find<MapprService>().mappr;
    super.onInit();
  }

  String get overallScore => "${result.score.roundedPrecisionToString(0)}%";

  void getData() {
    final args = Get.arguments as ResultEntity;
    final params = Get.parameters;
    if (params.containsKey('showBackBtn')) {
      showBackBtn = true;
    }
    result = args;
    questions.addAll(args.questions);
    time = args.time;
    testName = args.testName;
    timeDoTest = args.timeDoTest;
    testType = args.type;
    int index = 0;
    for (var element in questions) {
      element.indexInTest = index;
      index++;
      if (element.hasAnySelect == false) {
        notDone.add(element);
      } else {
        if (element.isCorrect ?? false) {
          corrects.add(element);
        } else {
          incorrects.add(element);
        }
      }
      if (element.isBookmark ?? false) {
        bookmarked.add(element);
      }
    }
  }

  Future<void> onTryAgain() async {
    final learningController = Get.find<LearningController>();
    if (learningController.testInProgress != null) {
      final continueTest = await learningController.popupHasInprogress();
      if (continueTest == true) {
        learningController.goTestInProgress();
        return;
      } else if (continueTest == null) return;
    }
    final questionEntites =
        questions.map((e) => e.toCleanQuestionEntity()).toList();
    if (result.type == TestType.fullTest) {
      List<QuestionEntity> filterQuestionEntites =
          QuestionEntity.updateQuestionBookmarkState(questionEntites);
      if (result.mockTest.value == null) {
        if (Get.isRegistered<LearningController>()) {
          List<QuizEntity> listMocks = Get.find<LearningController>().mockTests;
          QuizEntity? mockTest = listMocks
              .firstWhereOrNull((test) => test.quizName == result.testName);
          result.mockTest.value = mockTest;
        }
      }
      Get.offAndToNamed(RouterName.instruction,
          arguments: ExamParams(
            numQuestion: questions.length,
            questions: filterQuestionEntites,
            testType: testType,
            testName: testName,
            mockTest: result.mockTest.value,
          ));
    } else {
      //filter các câu đánh dấu
      List<QuestionEntity> filterQuestionEntites =
          QuestionEntity.updateQuestionBookmarkState(questionEntites);
      Get.offAndToNamed(RouterName.doTest,
          arguments: ExamParams(
            numQuestion: questions.length,
            questions: filterQuestionEntites,
            testType: testType,
            testName: testName,
          ));
    }
  }

  void backToHome() {
    Get.offAllNamed(RouterName.home);
    if (Get.isRegistered<HomeController>()) {
      Get.find<HomeController>().backToDefaultTab();
    }
  }
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();
}
