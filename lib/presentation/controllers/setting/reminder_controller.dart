import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/other/reminder_params.dart';

class ReminderController extends GetxController {
  RxList<ReminderDay> dayReminders = <ReminderDay>[].obs;
  DateTime? reminderTime;
  RxBool isDaily = false.obs;

  @override
  void onInit() {
    super.onInit();
  }

  void initData(ReminderParams params) {
    dayReminders.addAll(params.selectedDays);
    reminderTime = params.reminderTime;
    if (dayReminders.length == 7) isDaily.value = true;
    update([ReminderConstants.reminderBottomSheetKey]);
  }

  void onToggleDaily(bool value) {
    isDaily.value = value;
    if (value) {
      dayReminders.clear();
      dayReminders.addAll([
        ReminderDay.monday,
        ReminderDay.tuesday,
        ReminderDay.wednesday,
        ReminderDay.thursday,
        ReminderDay.friday,
        ReminderDay.saturday,
        ReminderDay.sunday
      ]);
    } else {
      dayReminders.clear();
    }
    update([ReminderConstants.reminderRepeatBottomSheetKey]);
  }

  void onTapReminderDate(ReminderDayModel date) async {
    if (dayReminders.contains(date.day)) {
      dayReminders.remove(date.day);
      isDaily.value = false;
    } else {
      dayReminders.add(date.day);
      if (dayReminders.length == 7) isDaily.value = true;
    }
    update([ReminderConstants.reminderRepeatBottomSheetKey]);
  }

  void onChangeTime(DateTime time) {
    reminderTime = time;
  }

  void onConfirm() {
    final result = ReminderParams(
      selectedDays: dayReminders.toList(),
      reminderTime: reminderTime,
    );
    Get.back(result: result);
  }
}

class ReminderConstants {
  static const String reminderRepeatBottomSheetKey =
      'reminderRepeatBottomSheetKey';
  static const String reminderBottomSheetKey = 'reminderBottomSheetKey';
}
