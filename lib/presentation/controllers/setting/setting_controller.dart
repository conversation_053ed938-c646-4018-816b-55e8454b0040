import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart';
import 'package:scrumpass_exam_simulator/app/utils/datetime.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/utils/notification_service.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_empty.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/other/reminder_params.dart';
import 'package:scrumpass_exam_simulator/data/models/relative_app_response.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';
import 'package:scrumpass_exam_simulator/domain/entities/relative_app.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_relative_app_usecase.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/widgets/bottomsheet/reminder.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingController extends GetxController
    with _DbHandlerMixin, LocalNotitificationMixin {
  final textController = TextEditingController();

  String name = '';
  DateTime? testDate;
  DateTime? tempTestDate;
  double fontSize = 18;
  late double tempFontSize;
  Rx<TrainingMode> trainingMode = TrainingMode.practice.obs;
  Rx<Language> locale = Language.en.obs;
  RxList<ReminderDay> dayReminders = <ReminderDay>[].obs;
  DateTime? reminderTime;
  bool isActiveReminder = false;
  final ExpandableController expandableController = ExpandableController();
  List<RelativeAppEntity> relativeApps = [];
  late GetRelativeAppUsecase _getRelativeAppUsecase;

  @override
  void onInit() {
    super.onInit();
    _getRelativeAppUsecase = Get.find<GetRelativeAppUsecase>();
    textController.text = Global.getUsername;
    textController.addListener(() {
      update([SettingConstants.usernameBottomsheetKey]);
    });
    getRelativeApp();
    initData();
  }

  Future getRelativeApp() async {
    final response = await _getRelativeAppUsecase
        .execute(RelativeAppParams(appId: Constants.appId));

    relativeApps = Get.find<MapprService>()
        .mappr
        .convertList<RelativeAppResponse, RelativeAppEntity>(response);
    update([SettingConstants.relativeAppKey]);
  }

  void initData() async {
    final appData = await _getAppData();
    if (appData?.testDate?.isAfter(DateTime.now()) ?? false) {
      testDate = appData?.testDate;
    }
    tempTestDate = testDate;
    trainingMode.value = appData?.trainingMode ?? TrainingMode.practice;
    locale.value = appData?.locale ?? Language.en;
    isActiveReminder = appData?.reminder?.isActive ?? false;
    fontSize = appData?.testFontSize ?? 18;
    tempFontSize = fontSize;
    reminderTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        appData?.reminder?.reminderTime?.hour ?? 0,
        appData?.reminder?.reminderTime?.minute ?? 0);
    dayReminders.value = appData?.reminder?.dayReminders ?? [];
    if (isActiveReminder) expandableController.expanded = true;
    update([SettingConstants.appSettingKey, SettingConstants.userSettingKey]);
  }

  void saveUserName() async {
    final text = textController.text;
    Global.updateUsername(text);
    name = text;
    final appData = await _getAppData();
    final newData = appData?.copyWith(userName: text);
    if (newData != null) {
      _saveAppData(newData);
    }
    updateLeaningUI();
    update([SettingConstants.userSettingKey]);
  }

  Future<void> saveTestDate() async {
    testDate = tempTestDate?.toStartOfDay();
    final appData = await _getAppData();
    final newData = appData?.copyWith(testDate: testDate);
    if (newData != null) {
      await _saveAppData(newData);
      Popup.instance.showSnackBar(
        message: LocaleKeys.updateTestDateSuccess.tr,
        type: SnackbarType.success,
      );
      updateLeaningUI();
      update([SettingConstants.userSettingKey]);
    }
  }

  void updateLeaningUI() {
    if (Get.isRegistered<LearningController>()) {
      Get.find<LearningController>().updateUIFromSetting();
    }
  }

  void onChangeTestDate(DateTime date) {
    tempTestDate = date;
  }

  Future<void> saveFontSize(double value) async {
    fontSize = value;
    final appData = await _getAppData();
    final newData = appData?.copyWith(testFontSize: fontSize);
    if (newData != null) {
      Global.updateExamFontSize(fontSize);
      await _saveAppData(newData);
      update([SettingConstants.appSettingKey]);
    }
  }

  void onChangeFontSize(double value) {
    Get.find<LogFirebaseEvent>().logEvent(EventLogConstants.selectTypeSize);
    tempFontSize = value;
    update([SettingConstants.textSizeKey]);
  }

  void saveLanguage(Language value) async {
    Get.find<LogFirebaseEvent>().logEvent(EventLogConstants.clickBtnConfirm,
        params: LanguageLogEventParams(languageName: value.name).toJson());
    Get.updateLocale(value.locale);
    Global.locale = value;
    locale.value = value;
    final appData = await _getAppData();
    final newData = appData?.copyWith(locale: locale.value);
    if (newData != null) {
      await _saveAppData(newData);
      update([SettingConstants.appSettingKey]);
    }
  }

  void saveTrainingMode(TrainingMode value) async {
    trainingMode.value = value;
    final appData = await _getAppData();
    final newData = appData?.copyWith(trainingMode: trainingMode.value);
    if (newData != null) {
      await _saveAppData(newData);
      update([SettingConstants.appSettingKey]);
    }
  }

  void saveReminder(ReminderParams params) async {
    if (params.selectedDays.isEmpty) {
      return;
    }
    reminderTime = params.reminderTime;
    dayReminders.clear();
    dayReminders.addAll(params.selectedDays);
    localNotification(true, reminderTime ?? DateTime.now(), dayReminders);
    final appData = await _getAppData();
    final newData = appData?.copyWith(
      reminder: Reminder(
        dayReminders: dayReminders.toList(),
        reminderTime: reminderTime,
        isActive: true,
      ),
    );
    if (newData != null) {
      await _saveAppData(newData);
      isActiveReminder = true;
      expandableController.expanded = true;
      update([SettingConstants.appSettingKey]);
    }
  }

  void onToggleReminder(bool value) async {
    if (value) {
      Get.find<LogFirebaseEvent>()
          .logEvent(EventLogConstants.turnOnStudyReminder);
    } else {
      Get.find<LogFirebaseEvent>()
          .logEvent(EventLogConstants.turnOffStudyReminder);
    }
    isActiveReminder = value;
    expandableController.toggle();
    localNotification(value, reminderTime ?? DateTime.now(), dayReminders);
    final appData = await _getAppData();
    final newData = appData?.copyWith(
      reminder: Reminder(
        dayReminders: dayReminders.toList(),
        reminderTime: reminderTime,
        isActive: isActiveReminder,
      ),
    );
    if (newData != null) {
      await _saveAppData(newData);
      update([SettingConstants.appSettingKey]);
    }
  }

  void openReminderBottomSheet(bool? value) async {
    final value = await Popup.instance.showBottomSheet(BottomSheetEmpty(
      title: LocaleKeys.bottomsheetReminderTitle.tr,
      child: ReminderBottomSheet(),
    ));
    if (value != null) {
      saveReminder(value);
    }
  }

  void openAppReview() async {
    final InAppReview inAppReview = InAppReview.instance;
    if (await inAppReview.isAvailable()) {
      inAppReview.requestReview();
    } else {
      inAppReview.openStoreListing(appStoreId: Constants.appStoreId);
    }
  }

  void openShare(BuildContext context) {
    final box = context.findRenderObject() as RenderBox?;
    SharePlus.instance.share(
      ShareParams(
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
          text:
              "${LocaleKeys.shareText.tr.replaceAll("[certificate_name]", Constants.certName).replaceAll("[app_name]", Constants.appName)}.\n${LocaleKeys.iosLink.tr}${Constants.appStoreId}\n${LocaleKeys.androidLink.tr}${Constants.appId}"),
    );
  }

  void openPrivacyPolicy() {
    launchUrl(Uri.parse(Constants.policy), mode: LaunchMode.inAppBrowserView);
  }

  static double getMarginIcon() {
    double fontSize = Global.getExamFontSize;
    switch (fontSize) {
      case 16:
        return 3;
      case 18:
        return 4;
      case 20:
        return 5;
      case 22:
        return 6;
      case 24:
        return 7;
      default:
        return 0;
    }
  }
}

mixin LocalNotitificationMixin {
  void localNotification(
      bool isEnable, DateTime selectedTime, List<ReminderDay> selectedDays) {
    if (isEnable) {
      final now = DateTime.now();
      DateTime scheduleTime = DateTime(
          now.year, now.month, now.day, selectedTime.hour, selectedTime.minute);
      List<int> arrayDays = selectedDays.map((e) => e.id).toList();
      NotificationService().showNotification(1, LocaleKeys.notification.tr,
          LocaleKeys.notificationContent.tr, scheduleTime, arrayDays);
    } else {
      NotificationService().cancelNotification();
    }
  }
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();

  Future<AppData?> _getAppData() async {
    return localService.getAppData();
  }

  Future<void> _saveAppData(AppData appData) async {
    return localService.saveAppData(appData);
  }
}

class SettingConstants {
  static const String reminderSwitchKey = 'reminderSwitch';
  static const String relativeAppKey = 'relativeApp';
  static const String usernameKey = 'username';
  static const String usernameBottomsheetKey = 'usernameBottomsheet';
  static const String testDateKey = 'testDate';
  static const String trainingModeKey = 'trainingMode';
  static const String accountKey = 'account';
  static const String languageKey = 'language';
  static const String textSizeKey = 'textSize';
  static const String userSettingKey = 'userSetting';
  static const String appSettingKey = 'appSetting';
  static const String firstLaunchKey = 'firstLaunch';
}
