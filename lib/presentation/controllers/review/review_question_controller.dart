import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';

class ReviewQuestionController extends BaseController
    with GetTickerProviderStateMixin {
  ReviewQuestionController({required this.results, required this.listQuestions})
      : super(ConnectionConcrete()) {
    initData();
  }

  final List<ResultEntity> results;
  final List<QuestionEntity> listQuestions;

  /// các câu hỏi có thể lặp lại
  List<QuestionResult> allQuestions = [];
  List<QuestionResult> questions = [];
  List<QuestionResult> corrects = [];
  List<QuestionResult> incorrects = [];
  List<QuestionResult> bookmarked = [];
  List<QuestionResult> unanswered = [];
  final List<ResultFilter> filters = [
    ResultFilter.all,
    ResultFilter.incorrect,
    // ResultFilter.correct,
    ResultFilter.bookmarked,
    // ResultFilter.notDone,
  ];

  late TabController tabController;

  void initData() {
    getQuestionData();
    tabController = TabController(length: filters.length, vsync: this);
    tabController.addListener(() {
      onTabChanged();
    });
  }

  void getQuestionData() {
    for (var element in results) {
      questions.addAll(element.questions);
      allQuestions.addAll(questions);
      questions = questions.toSet().toList();
    }

    for (var element in allQuestions) {
      if (element.hasAnySelect) {
        if (element.isCorrect ?? false) {
          if (corrects.indexWhere((e) => e.qid == element.qid) == -1) {
            corrects.add(element);
          }
        } else {
          if (incorrects.indexWhere((e) => e.qid == element.qid) == -1) {
            incorrects.add(element);
          }
        }
      } else {
        if (unanswered.indexWhere((e) => e.qid == element.qid) == -1) {
          unanswered.add(element);
        }
      }
    }
    List<QuestionEntity> bookmarkedQuestions =
        listQuestions.where((q) => q.isBookmark == true).toList();
    for (QuestionEntity element in bookmarkedQuestions) {
      QuestionResult? latestQuestion = allQuestions.firstWhereOrNull(
          (e) => e.qid == element.qid && e.isBookmark == true);
      bookmarked.add(
        QuestionResult(
          qid: element.qid,
          question: element.question,
          description: element.description,
          options: latestQuestion?.options ?? element.options,
          status: latestQuestion?.status ?? element.status,
          isCorrect: latestQuestion?.isCorrect ?? element.isCorrect,
          type: element.type,
          isBookmark: element.isBookmark,
          indexInTest: latestQuestion?.indexInTest,
        ),
      );
    }
  }

  onTabChanged() {
    if (Get.isRegistered<ReviewController>()) {
      Get.find<ReviewController>().scrollController.animateTo(0,
          duration: const Duration(milliseconds: 400), curve: Curves.easeInOut);
    }
  }
}
