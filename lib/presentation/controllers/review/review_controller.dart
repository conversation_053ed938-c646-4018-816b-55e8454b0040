import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';

enum ReviewTab { test, question }

class ReviewController extends BaseController with _DbHandlerMixin {
  ReviewController() : super(ConnectionConcrete());

  final List<ResultEntity> results = [];
  final List<QuestionEntity> questions = [];
  final Map<String, List<ResultEntity>> resultsByDay = {};

  Rx<ReviewTab> currentTab = ReviewTab.test.obs;
  final RxInt watchTab = 0.obs;

  late final ScrollController scrollController;

  RxBool showScrollToTop = false.obs;

  List<ResultEntity> get doneResults =>
      results.where((element) => element.status == ResultStatus.done).toList();

  @override
  void onInit() {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.light,
    ));
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    super.onInit();
  }

  @override
  void onClose() {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
    ));
    super.onClose();
  }

  void _scrollListener() {
    if (scrollController.offset > 160) {
      showScrollToTop.value = true;
    } else {
      showScrollToTop.value = false;
    }
  }

  void scrollToTop() {
    scrollController.animateTo(0,
        duration: const Duration(milliseconds: 500), curve: Curves.easeInOut);
  }

  void onTapStartTest() {
    if (Get.find<TourController>().finishTourReview.value == false) return;
    if (Get.isRegistered<LearningController>()) {
      Get.find<LearningController>().onTapQuickTest(fromReviewTab: true);
    }
  }

  void getResultData() async {
    isLoading.value = true;
    final results = await _getResult();
    if (this.results.isNotEmpty)
      this.results.replaceRange(0, this.results.length, results ?? []);
    else
      this.results.addAll(results ?? []);
    final questions = await _getQuestions();
    if (this.questions.isNotEmpty)
      this.questions.replaceRange(0, this.questions.length, questions ?? []);
    else
      this.questions.addAll(questions ?? []);
    groupResults();
    isLoading.value = false;
  }

  void groupResults() {
    final List<ResultEntity> result = [];
    for (var element in results) {
      if (result.isEmpty)
        // add giá trị đầu tiên
        result.add(element);
      else {
        final date = result.first.time;
        if (Jiffy.parseFromDateTime(element.time)
            .isSame(Jiffy.parseFromDateTime(date), unit: Unit.day)) {
          result.add(element);
        } else {
          // khác ngày thì đẩy vào mảng
          final dateString =
              Jiffy.parseFromDateTime(date).format(pattern: 'dd/MMM/yyyy');
          resultsByDay[dateString] = List.from(result);
          // xoá và đẩy data ngày mới
          result.clear();
          result.add(element);
        }
      }
    }
    if (result.isNotEmpty) {
      final dateString = Jiffy.parseFromDateTime(result.first.time)
          .format(pattern: 'dd/MMM/yyyy');
      resultsByDay[dateString] = List.from(result);
    }
  }

  void onChangeTab(ReviewTab tab) {
    if (Get.isRegistered<TourController>()) {
      if (Get.find<TourController>().finishTourReview.value == false) {
        return;
      }
    }
    currentTab.value = tab;
    showScrollToTop.value = false;
    if (scrollController.hasClients) scrollController.jumpTo(0);
    if (tab == ReviewTab.test) {
      watchTab.value++;
    }
  }
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();

  Future<List<ResultEntity>>? _getResult() async {
    return localService.getResult();
  }

  Future<List<QuestionEntity>>? _getQuestions() async {
    return localService.getAllQuestions();
  }
}
