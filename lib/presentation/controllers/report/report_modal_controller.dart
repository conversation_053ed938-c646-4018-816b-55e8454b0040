import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/repositories/report_repo_impl.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/send_report_question_usecase.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class ReportModalController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final localService = Get.find<LocalService>();
  FocusNode focusNode = FocusNode();
  Rx<Color> borderColor = Colors.grey.obs;
  TextEditingController reportController = TextEditingController();
  RxBool status = false.obs;
  final ReportModalController homeController = Get.put(ReportModalController());

  String removeHtmlTags(String input) {
    final withoutTags = input.replaceAll(RegExp(r'<[^>]*>|<\/[^>]*>'), '');
    return withoutTags;
  }

  @override
  void onInit() {
    Get.lazyPut(() => ReportRepoImpl());
    Get.lazyPut(() => SendReportQuestionUsecase(Get.find<ReportRepoImpl>()));
    focusNode.addListener(() {
      borderColor.value =
          focusNode.hasFocus ? AppColors.icBtn2ndDefault : Colors.grey;
    });
    reportController.addListener(() {
      final textLength = reportController.text.trim().length;
      if (textLength > 0 && textLength < 300) {
        status.value = true;
      } else {
        status.value = false;
      }
    });
    super.onInit();
  }

  @override
  void onClose() {
    reportController.dispose();
  }

  Future<AppData?> _getAppData() async {
    return localService.getAppData();
  }

  Future<void> handleSendReport(
      QuestionResult question, String reportdetail) async {
    final appData = await _getAppData();
    String debugid = appData?.debugId ?? '';
    final response = await Get.find<SendReportQuestionUsecase>()
        .execute(SendReportQuestionParams(
      qid: question.qid!,
      reportdetail: reportdetail,
      debugid: debugid,
    ));
    Get.back();
    Future.delayed(Duration(milliseconds: 500), () {
      if (response == "Success") {
        Popup.instance.showSuccessPopup(
          title: LocaleKeys.reportSuccessTitle.tr,
          description: LocaleKeys.reportSuccessDescription.tr,
        );
      } else {
        Popup.instance.showSnackBar(
          message: LocaleKeys.defaultErrorMsg,
          type: SnackbarType.error,
        );
      }
    });
  }
}
