import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/onboarding/widgets/onboarding_widgets.dart';

class OnbroadingController extends GetxController
    with GetSingleTickerProviderStateMixin {
  OnbroadingController();
  late PageController pageController;
  RxInt currentPage = 0.obs;
  final List<Map<String, dynamic>> onboardingData = [
    {
      'title': LocaleKeys.onbroadTitle1.tr,
      'description': LocaleKeys.onboardDescription1.tr,
      'customWidget': OnboardingPages().page1(),
    },
    {
      'title': LocaleKeys.onbroadTitle2.tr.toTitleCase,
      'description': LocaleKeys.onboardDescription2.tr,
      'customWidget': OnboardingPages().page2(),
    },
    {
      'title': LocaleKeys.onbroadTitle3.tr,
      'description': LocaleKeys.onboardDescription3.tr,
      'customWidget': OnboardingPages().page3(),
    },
    {
      'title': LocaleKeys.onbroadTitle4.tr,
      'description': LocaleKeys.onboardDescription4.tr,
      'customWidget': OnboardingPages().page4(),
    },
  ];

  @override
  void onInit() {
    super.onInit();
    pageController = PageController(initialPage: currentPage.value);
    pageController.addListener(() {
      if ((pageController.page?.toInt() ?? 0) != currentPage.value) {
        currentPage.value = (pageController.page?.toInt() ?? 0);
      }
    });
  }

  toNextPage() {
    if (currentPage < 3) {
      int data = currentPage.value + 1;
      pageController.animateToPage(
        data,
        duration: Duration(milliseconds: 200),
        curve: Curves.easeIn,
      );
    } else {
      if (Global.isPremium == false) {
        Get.toNamed(RouterName.premium);
      }
    }
  }

  skipBtn() {
    if (currentPage.value == 3) {
      Get.toNamed(RouterName.welcome);
    } else {
      int data = 3;
      pageController.animateToPage(data,
          duration: Duration(milliseconds: 300), curve: Curves.easeIn);
    }
  }
}
