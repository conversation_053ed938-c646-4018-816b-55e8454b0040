import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';

class TourController extends GetxController with _DbHandlerMixin {
  static const String typeHome = "HOME";
  static const String typeReview = "ANALYTICS";

  static const String positionLeft = "LEFT";
  static const String positionCenter = "CENTER";
  static const String positionRight = "RIGHT";

  RxBool finishPrimaryTour = (Global.getStatePrimaryTour).obs;
  RxBool finishTourReview = (Global.getStateTourReview).obs;
  RxBool finishTourMock = (Global.getStateTourMock).obs;
  RxBool finishTourAnalyst = (Global.getStateTourAnalyst).obs;
  RxBool isTourPrimaryActive = true.obs;
  RxBool isTourMockActive = false.obs;
  RxBool isTourReviewActive = false.obs;
  RxBool isTourAnalystActive = false.obs;
  RxBool test = false.obs;
  RxBool ignorePointer = true.obs;

  Future<void> showTourMock() async {
    isTourMockActive.value = true;
  }

  void showTourPrimary() {
    isTourPrimaryActive.value = true;
  }

  void showTourReview() {
    isTourReviewActive.value = true;
  }

  void showTourAnalyst() {
    isTourAnalystActive.value = true;
  }

  void completeTourPrimary({bool isDisableTab = true, isCloseAll = false}) {
    isTourPrimaryActive.value = false;
    Global.updateTourPrimaryState(true);
    updateTourPrimaryState(true);
    if (isDisableTab) ignorePointer.value = false;
    if (!isCloseAll) updateAppDataTourState();
  }

  void completeTourMock({bool isDisableTab = true, isCloseAll = false}) {
    isTourMockActive.value = false;
    Global.updateTourMockState(true);
    updateTourMockState(true);
    if (isDisableTab) ignorePointer.value = false;
    if (!isCloseAll) updateAppDataTourState();
  }

  void completeTourReview({bool isDisableTab = true, isCloseAll = false}) {
    isTourReviewActive.value = false;
    Global.updateTourReviewState(true);
    updateTourReviewState(true);
    if (isDisableTab) ignorePointer.value = false;
    if (!isCloseAll) updateAppDataTourState();
  }

  void completeTourAnalyst({bool isDisableTab = true, isCloseAll = false}) {
    isTourAnalystActive.value = false;
    Global.updateTourAnalystState(true);
    updateTourAnalystState(true);
    if (isDisableTab) ignorePointer.value = false;
    if (!isCloseAll) updateAppDataTourState();
  }

  void updateTourPrimaryState(bool state) {
    finishPrimaryTour.value = state;
  }

  void updateTourMockState(bool state) {
    finishTourMock.value = state;
  }

  void updateTourReviewState(bool state) {
    finishTourReview.value = state;
  }

  void updateTourAnalystState(bool state) {
    finishTourAnalyst.value = state;
  }

  void hideTourMock() {
    isTourMockActive.value = false;
  }

  void completeAllTour() {
    completeTourMock(isCloseAll: true);
    completeTourAnalyst(isCloseAll: true);
    completeTourPrimary(isCloseAll: true);
    completeTourReview(isCloseAll: true);
    Global.completeTourState();
    updateAppDataTourState();
  }

  void updateAppDataTourState() async {
    final appData = await _getAppData();
    if (appData != null) {
      final newData = appData.copyWith(
        finishPrimaryTour: finishPrimaryTour.value,
        finishTourReview: finishTourReview.value,
        finishTourMock: finishTourMock.value,
        finishTourAnalyst: finishTourAnalyst.value,
      );
      await _saveAppData(newData);
    }
  }
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();
  Future<AppData?> _getAppData() async {
    return localService.getAppData();
  }

  Future<void> _saveAppData(AppData appData) async {
    return localService.saveAppData(appData);
  }
}
