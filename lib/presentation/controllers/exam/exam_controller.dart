import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:isar/isar.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/extensions/int.dart';
import 'package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_empty.dart';
import 'package:scrumpass_exam_simulator/app/widgets/life_cycle_event_handler.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/other/exam_params.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/do_domain_test_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/do_practice_test_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_question_mock_test_usecase.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_mock_test.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/review/review_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/widgets/question_list.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ExamController extends BaseController with _DbHandlerMixin {
  ExamController() : super(ConnectionConcrete());
  late int numQuestion;
  List<TestDomain>? domain;
  late DoPracticeTestUsecase _doPracticeTestUsecase;
  late DoDomainTestUsecase _doDomainTestUsecase;
  late GetQuestionMockTestUsecase getQuestionMockTestUsecase;
  late Mappr mapper;
  RxInt questionIndex = 0.obs;
  Timer? _timer;
  RxInt timeDoTest = 0.obs;
  TrainingMode trainingMode = TrainingMode.practice;
  late LifeCycleEventHandler lifeCycleEvent;
  ResultEntity? result;
  bool isResume = false;
  late TestType testType;
  late String testName;
  final List<QuestionEntity> tryAgainQuestions = [];
  QuizEntity? mockTest;
  final ScrollController questionScrollController = ScrollController();

  /// In Seconds
  int mockTestDuration = 0;

  final List<QuestionEntity> questions = [
    // Fake data for skleton
    QuestionEntity(
        question: BoneMock.paragraph,
        type: QuestionType.single,
        options: [
          OptionEntity(qOption: BoneMock.paragraph),
          OptionEntity(qOption: BoneMock.paragraph),
          OptionEntity(qOption: BoneMock.paragraph),
          OptionEntity(qOption: BoneMock.paragraph),
        ])
  ];

  QuestionEntity get currentQuestion => questions[questionIndex.value];

  String get getTime => testType == TestType.fullTest
      ? (mockTestDuration - timeDoTest.value).toTimeString
      : timeDoTest.value.toTimeString;

  int get doneQuestions {
    final done = questions.where((e) => e.status == QuestionStatus.answered);
    return done.length;
  }

  bool get lastQuestionSubmit {
    if (trainingMode != TrainingMode.exam) {
      return questionIndex == questions.length - 1 &&
          questions.last.status == QuestionStatus.answered;
    } else {
      return questionIndex == questions.length - 1;
    }
  }

  @override
  void onInit() {
    _doPracticeTestUsecase = Get.find<DoPracticeTestUsecase>();
    _doDomainTestUsecase = Get.find<DoDomainTestUsecase>();
    getQuestionMockTestUsecase = Get.find<GetQuestionMockTestUsecase>();
    mapper = Get.find<MapprService>().mappr;

    if (Get.arguments is ExamParams) {
      final args = Get.arguments as ExamParams;
      numQuestion = args.numQuestion;
      domain = args.domain;
      testType = args.testType;
      testName = args.testName;
      if (args.result != null) {
        isResume = true;
      }
      // add câu hỏi cho try again
      tryAgainQuestions.addAll(args.questions ?? []);
      if (testType == TestType.fullTest) {
        if (isResume) {
          // Tiếp tục bài mock test
          initResumeMockTest(args);
        } else {
          // retry bài test mock
          mockTest = args.mockTest;
          numQuestion = mockTest!.noq;
          testType = TestType.fullTest;
          testName = mockTest!.quizName;
          mockTestDuration = mockTest!.duration * 60;
          // add câu hỏi cho try again
          getMockTestData(isRetry: true);
        }
      } else {
        getPracticeData();
      }
    } else if (Get.arguments is QuizEntity) {
      // Các bài mock test
      final args = Get.arguments as QuizEntity;
      mockTest = args;
      numQuestion = args.noq;
      testType = TestType.fullTest;
      testName = args.quizName;
      mockTestDuration = args.duration * 60;
      // add câu hỏi cho try again
      getMockTestData();
    }
    lifeCycleEvent = LifeCycleEventHandler(resumeCallBack: () async {
      startTimer();
    }, suspendingCallBack: () async {
      _timer?.cancel();
    });

    WidgetsBinding.instance.addObserver(lifeCycleEvent);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.light,
    ));
    super.onInit();
  }

  @override
  void onClose() {
    _timer?.cancel();
    _timer = null;
    WidgetsBinding.instance.removeObserver(lifeCycleEvent);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
    ));
    super.onClose();
  }

  Future<void> initBookmarkQuestion() async {
    List<QuestionEntity> listQuestions = [];
    if (Get.isRegistered<ReviewController>()) {
      final reviewController = Get.find<ReviewController>();
      listQuestions = reviewController.questions;
    } else {
      listQuestions = await _getQuestions() ?? [];
    }
    List<String> bookmarkedQids = listQuestions
        .where((q) => q.isBookmark == true)
        .map((q) => q.qid!)
        .toList();

    for (QuestionEntity question in questions) {
      if (bookmarkedQids.contains(question.qid)) {
        question.isBookmark = true;
      } else {
        question.isBookmark = false;
      }
    }
  }

  Future getPracticeData() async {
    isLoading.value = true;
    if (isResume) {
      await initResultResume();
    }
    await getTestQuestions();
    await getLocalData();
    initDoingQuestion();
    if (!isResume) await initBookmarkQuestion();
    initResult();
    isLoading.value = false;
  }

  Future initResumeMockTest(ExamParams args) async {
    isLoading.value = true;
    mockTest = args.result?.mockTest.value;
    mockTestDuration = mockTest!.duration * 60;
    result = args.result;
    timeDoTest.value = result!.timeDoTest;
    await getTestQuestions();
    await getLocalData(isMockTest: true);
    initDoingQuestion();
    isLoading.value = false;
  }

  Future<void> initResultResume() async {
    result = await _getNotDoneResult();
    timeDoTest.value = result!.timeDoTest;
  }

  void updateQuestions() {
    final questionResult =
        mapper.convertList<QuestionResult, QuestionEntity>(result!.questions);
    questions.clear();
    questions.addAll(questionResult);
  }

  void initDoingQuestion() {
    final doing = questions.indexWhere((e) => e.status == QuestionStatus.doing);
    if (doing == -1) {
      // Nếu ko tìm thấy câu đang làm thì tìm câu làm xong cuối
      final lastDoing =
          questions.lastIndexWhere((e) => e.status == QuestionStatus.answered);
      if (lastDoing == -1) {
        // Ko có câu làm xong thì gán index và set doing câu đầu tiên
        questions.first.status = QuestionStatus.doing;
        questionIndex.value = 0;
      } else {
        // Gán index câu làm xong tiếp theo
        questionIndex.value = lastDoing;
      }
    } else {
      questionIndex.value = doing;
    }
  }

  void initResult() async {
    if (result == null && questions.isNotEmpty) {
      final questionResult =
          mapper.convertList<QuestionEntity, QuestionResult>(questions);
      result = ResultEntity(
        testName: testName,
        time: DateTime.now(),
        timeDoTest: 0,
        status: ResultStatus.notDone,
        questions: questionResult,
        type: testType,
        mockTest: mockTest,
      );
      await _clearNotDoneResult();
      await _saveResult(result!);
      await _updateLinkResult(result!);
    }
  }

  Future<void> updateResult({ResultStatus? status}) async {
    result?.timeDoTest = timeDoTest.value;
    final questionResult =
        mapper.convertList<QuestionEntity, QuestionResult>(questions);
    result?.questions = List<QuestionResult>.from(questionResult);
    if (status != null) {
      result?.status = status;
    }
    // Ko rõ sao là lại tạo ra result mới, id nhảy
    if (testType == TestType.fullTest) {
      result?.mockTest.value = mockTest;
    }
    await _clearNotDoneResult();
    await _saveResult(result!);
    await _updateLinkResult(result!);
  }

  Future getTestQuestions() async {
    try {
      if (isResume) {
        updateQuestions();
        startTimer();
        return;
      }
      if (tryAgainQuestions.isNotEmpty) {
        questions.clear();
        questions.addAll(tryAgainQuestions);
        startTimer();
        return;
      }
      // Câu trả lời sai và chưa trả lời
      if (testType == TestType.missed) {
        final results = await _getResults();
        final List<QuestionResult> questionWrong = [];
        for (var element in results) {
          final questions =
              element.questions.where((e) => e.isCorrect == false);
          questionWrong.addAll(questions);
        }
        final setQuestions = questionWrong.toSet();
        if (setQuestions.length < numQuestion) {
          Get.back();
          Popup.instance.showSnackBar(
              message: setQuestions.isEmpty
                  ? "${LocaleKeys.noIncorrectAnswerAlert.tr}"
                  : LocaleKeys.notEnoughQuestion.tr,
              type: SnackbarType.normal);
          questions.clear();
          return;
        }
        final questionEntities =
            setQuestions.map((e) => e.toCleanQuestionEntity()).toList();

        questions.clear();
        while (questions.length < numQuestion) {
          var value =
              questionEntities[Random().nextInt(questionEntities.length)];
          if (!questions.contains(value)) {
            questions.add(value);
          }
        }
        startTimer();
      }
      // Câu đánh dấu
      if (testType == TestType.bookmark) {
        final results = await _getResults();
        final List<QuestionResult> questionMarked = [];
        for (var element in results) {
          final questions =
              element.questions.where((e) => e.isBookmark == true);
          questionMarked.addAll(questions);
        }
        List<QuestionResult> setQuestions = questionMarked.toSet().toList();
        setQuestions = QuestionResult.removeQuestionNotBookmarked(setQuestions);
        if (setQuestions.length < numQuestion) {
          Get.back();
          Popup.instance.showSnackBar(
              message: setQuestions.isEmpty
                  ? LocaleKeys.noBookmarkQuestionAlert.tr
                  : LocaleKeys.notEnoughQuestion.tr,
              type: SnackbarType.normal);
          questions.clear();
          return;
        }
        final questionEntities =
            setQuestions.map((e) => e.toCleanQuestionEntity()).toList();

        questions.clear();
        while (questions.length < numQuestion) {
          var value =
              questionEntities[Random().nextInt(questionEntities.length)];
          if (!questions.contains(value)) {
            questions.add(value);
          }
        }
        startTimer();
      }
      if (testType == TestType.domain && (domain?.isNotEmpty ?? false)) {
        final response = await _doDomainTestUsecase.execute(
            DoDomainTestParams(numQuestion: numQuestion, testDomains: domain!));
        final questionEntities =
            mapper.convertList<QuestionResponse, QuestionEntity>(response);
        questions.clear();
        questions.addAll(questionEntities);
        startTimer();
        return;
      }

      // Quick test
      if (testType == TestType.quickPractice) {
        final response = await _doPracticeTestUsecase
            .execute(DoPracticeTestParams(numQuestion: numQuestion));
        final questionEntities =
            mapper.convertList<QuestionResponse, QuestionEntity>(response);
        questions.clear();
        questions.addAll(questionEntities);
        startTimer();
        return;
      }
    } catch (e) {
      questions.clear();
      handleError(e);
      Get.back();
    }
  }

  Future getLocalData({bool isMockTest = false}) async {
    final appData = await _getAppData();
    if (appData?.trainingMode != null) {
      trainingMode = isMockTest
          ? TrainingMode.exam
          : appData?.trainingMode ?? TrainingMode.practice;
    }
  }

  void openQuestionList() async {
    if (isLoading.value) return;
    await Popup.instance.showBottomSheet(BottomSheetEmpty(
        title: LocaleKeys.questionListTitle.tr, child: QuestionList()));
  }

  void onNextQuestion() {
    // nếu chưa ấn next để hiện đáp án (notAnswered) thì hiện đáp án
    if (currentQuestion.status == QuestionStatus.doing) {
      if (currentQuestion.hasAnySelect) {
        // đổi trạng thái, update lại screen để hiện đáp án
        currentQuestion.status = QuestionStatus.answered;
        if (trainingMode != TrainingMode.exam) {
          calculateScore(currentQuestion);
        } else {
          // nếu câu cuối thì submit mode exam
          if (lastQuestionSubmit) {
            onSubmitTest();
            return;
          }
          // chuyển sang câu tiếp
          if (questionIndex < questions.length - 1) {
            questionIndex++;
            _scrollToTop();
          }
          onChangeStatusDoing();
        }
        update([
          ExamConstants.optionList,
          ExamConstants.explanationBox,
          ExamConstants.nextBtn
        ]);
      } else {
        // nếu chưa chọn đáp án nào thì hiện popup
        Popup.instance.showAlertPopup(title: LocaleKeys.needSelectAnswer.tr);
      }
    } else {
      // nếu câu cuối và đã trả lời thì submit
      if (lastQuestionSubmit) {
        onSubmitTest();
        return;
      }

      // chuyển sang câu tiếp
      if (questionIndex < questions.length - 1) {
        questionIndex++;
        _scrollToTop();
      }
      onChangeStatusDoing();
      update([ExamConstants.nextBtn]);
    }
    updateResult();
    update([ExamConstants.answeredHeader, ExamConstants.progressBar]);
  }

  void onPrevQuestion() {
    if (questionIndex.value > 0) {
      questionIndex--;
      _scrollToTop();
    }
    update([ExamConstants.nextBtn]);
    updateResult();
  }

  void onChangeStatusDoing() {
    if (currentQuestion.status == QuestionStatus.notAnswered) {
      currentQuestion.status = QuestionStatus.doing;
    }
  }

  void _scrollToTop() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (questionScrollController.hasClients) {
        questionScrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// chuyển câu hỏi ở question list
  void onChangeQuestion(int index) {
    if (questions[index].status != QuestionStatus.notAnswered) {
      questionIndex.value = index;
      _scrollToTop();
      Get.back();
      update([ExamConstants.nextBtn]);
    }
  }

  void calculateScore(QuestionEntity question) {
    final options = question.options ?? [];
    double totalScore = 0;
    final selectedOptions = options.where((e) => e.isSelected).toList();
    for (var element in selectedOptions) {
      final score = double.tryParse(element.score ?? '0') ?? 0;
      totalScore += score;
    }
    if (totalScore > 0.9) {
      question.isCorrect = true;
    } else {
      question.isCorrect = false;
    }
  }

  void onSelectOption(OptionEntity option) {
    if (currentQuestion.status == QuestionStatus.answered &&
        trainingMode != TrainingMode.exam) return;
    if (currentQuestion.type == QuestionType.single) {
      final options = currentQuestion.options ?? [];
      for (var opt in options) {
        if (opt == option) {
          opt.isSelected = true;
        } else {
          opt.isSelected = false;
        }
      }
    } else {
      option.isSelected = !option.isSelected;
    }
    update([ExamConstants.optionList]);
  }

  void onBack() async {
    if (isLoading.value) return;
    await Popup.instance.showActionPopup(
        title: LocaleKeys.examBackTitle.tr,
        description: LocaleKeys.examBackContent.tr,
        primaryBtnText: LocaleKeys.leave.tr,
        secondaryBtnText: LocaleKeys.submit.tr,
        showClose: true,
        onPrimaryTap: () {
          updateReviewPage();
          updateLearingPage();
          Get.until((route) => Get.currentRoute == RouterName.home);
        },
        onSecondaryTap: () {
          submit();
        });
  }

  void onBookmark() {
    currentQuestion.isBookmark = !currentQuestion.isBookmark;
    update([ExamConstants.bookmark]);
    updateResult();
  }

  void onSubmitTest() async {
    closeDialog();
    closeBottomSheet();
    submitDialog();
  }

  void submit() async {
    // case câu cuối đã chọn nhưng chưa ấn next mà ấn submit ở ds câu hỏi
    if (currentQuestion.status == QuestionStatus.doing) {
      onNextQuestion();
    }
    closeDialog();
    closeBottomSheet();
    if (trainingMode == TrainingMode.exam) {
      calculateScoreExamMode();
    }
    calculateOverallScore();
    await updateResult(status: ResultStatus.done);
    if (result != null) await updateListQuestionEntity(result!);
    await _saveQuestions(questions);
    updateLearingPage();
    updateReviewPage();
    Get.offAndToNamed(RouterName.resultDetail, arguments: result);
  }

  void calculateScoreExamMode() {
    for (var element in questions) {
      calculateScore(element);
    }
  }

  void calculateOverallScore() {
    int correctCount = 0;
    for (var element in questions) {
      if (element.isCorrect ?? false) correctCount++;
    }
    final score = (correctCount / questions.length) * 100;
    result?.score = score;
    if (testType == TestType.fullTest) {
      result?.pass = score > mockTest!.passPercentage;
    }
  }

  void submitDialog() async {
    int numNotAns =
        questions.where((q) => q.hasAnySelect == false).toList().length;
    await Popup.instance.showActionPopup(
        title: '',
        customRichText: numNotAns == 0
            ? null
            : RichText(
                text: TextSpan(children: [
                TextSpan(
                  text: '${LocaleKeys.youHave.tr} ',
                  style: AppTextStyles.smallMedium
                      .copyWith(color: AppColors.popupTitle),
                ),
                TextSpan(
                  text:
                      "${LocaleKeys.numUnfinishQuestion.tr.replaceAll("\${num}", numNotAns.toString())}",
                  style: AppTextStyles.smallBold
                      .copyWith(color: AppColors.popupTitle),
                ),
              ])),
        description: LocaleKeys.examSubmitContent.tr,
        primaryBtnText: LocaleKeys.submit.tr,
        secondaryBtnText: LocaleKeys.cancel.tr,
        onPrimaryTap: () {
          submit();
        },
        onSecondaryTap: () {});
  }

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (t) async {
      timeDoTest.value++;
      if (testType == TestType.fullTest) {
        update([ExamConstants.progressBar]);
        if (timeDoTest.value == mockTest!.duration * 60) {
          t.cancel();
          await Popup.instance.showAlertPopup(title: LocaleKeys.timeUp.tr);
          submit();
        }
      }
    });
  }

  void updateLearingPage() {
    Get.find<LearningController>().getData();
  }

  void updateReviewPage() {
    if (Get.isRegistered<ReviewController>()) {
      Get.find<ReviewController>().getResultData();
    }
  }

  void updateTourSatePage() {
    Get.find<HomeController>().tourController.completeAllTour();
  }
}

class ExamConstants {
  static const String optionList = 'optionList';
  static const String optionBox = 'optionBox';
  static const String answeredHeader = 'answeredHeader';
  static const String bookmark = 'bookmark';
  static const String explanationBox = 'explanationBox';
  static const String nextBtn = 'nextBtn';
  static const String progressBar = 'progressBar';
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();

  Future<void> _saveQuestions(List<QuestionEntity> questions) async {
    return localService.saveQuestions(questions);
  }

  Future<AppData?> _getAppData() async {
    return localService.getAppData();
  }

  Future<List<QuestionEntity>>? _getQuestions() async {
    return localService.getAllQuestions();
  }

  Future<void> _saveResult(ResultEntity result) async {
    return localService.saveResult(result);
  }

  Future<void> _updateLinkResult(ResultEntity result) async {
    return localService.updateLinkResult(result);
  }

  Future<void> _loadLinkResult(ResultEntity result) async {
    return localService.loadLinkResult(result);
  }

  Future<void> _clearNotDonePracticeResult() async {
    return localService.clearNotDonePracticeResult();
  }

  Future<void> _clearNotDoneMockResult() async {
    return localService.clearNotDoneMockResult();
  }

  Future<void> _clearNotDoneResult() async {
    return localService.clearNotDoneResult();
  }

  Future<ResultEntity?> _getNotDoneResult() async {
    return localService.getNotDoneResult();
  }

  Future<List<ResultEntity>> _getResults() async {
    return localService.getDoneResult();
  }

  Future<void> updateListQuestionEntity(ResultEntity result) async {
    List<QuestionEntity> allResults = await localService.getAllQuestions();
    if (allResults.isEmpty) return;

    List<QuestionEntity> updatedResults = List.from(allResults);

    for (var question in updatedResults) {
      var matchingResults =
          result.questions.where((q) => q.qid == question.qid);

      if (matchingResults.isNotEmpty) {
        question.isBookmark = matchingResults.first.isBookmark ?? false;
      }
    }

    await localService.saveQuestions(updatedResults);
  }
}
