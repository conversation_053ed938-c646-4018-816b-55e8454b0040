import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/data/repositories/test_repo_impl.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/do_domain_test_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/do_practice_test_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_question_mock_test_usecase.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';

class ExamBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DoPracticeTestUsecase(Get.find<TestRepositoryImpl>()));
    Get.lazyPut(() => DoDomainTestUsecase(Get.find<TestRepositoryImpl>()));
    Get.lazyPut(() => GetQuestionMockTestUsecase(Get.find<TestRepositoryImpl>()));
    Get.lazyPut(() => ExamController());
  }
}
