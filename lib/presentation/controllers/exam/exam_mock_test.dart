import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_question_mock_test_usecase.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_controller.dart';

extension ExamMockTestExt on ExamController {
  double get remainingProgress => 1 - (timeDoTest.value / mockTestDuration);

  Future getMockTestData({bool isRetry = false}) async {
    isLoading.value = true;
    if (!isRetry) {
      try {
        final resp = await getQuestionMockTestUsecase
            .execute(GetQuestionMockTestParams(id: int.parse(mockTest!.quid)));
        List<QuestionEntity> questionEntity =
            mapper.convertList<QuestionResponse, QuestionEntity>(resp);
        questions.clear();
        questions.addAll(questionEntity);
      } catch (e) {
        handleError(e);
        Get.back();
      }
    } else {
      questions.clear();
      questions.addAll(tryAgainQuestions);
    }
    await getLocalData(isMockTest: true);
    initDoingQuestion();
    await initBookmarkQuestion();
    initResult();
    startTimer();
    isLoading.value = false;
  }
}
