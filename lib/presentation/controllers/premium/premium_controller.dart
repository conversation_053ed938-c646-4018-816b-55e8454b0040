import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:purchases_flutter/models/package_wrapper.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/utils/revenuecat_service.dart';
import 'package:scrumpass_exam_simulator/domain/entities/subscripiton.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:url_launcher/url_launcher.dart';

class PremiumController extends BaseController {
  PremiumController() : super(ConnectionConcrete());

  late final ScrollController scrollController;

  RxDouble headerOpacity = 0.0.obs;
  final double fullHeaderOpacityOffset = 50;

  late Mappr _mapper;

  final List<Package> packages = [];

  final List<SubscriptionEntity> subscriptions = [
    // Fake data for skleton
    SubscriptionEntity(
        duration: '1 month',
        price: 100000,
        concurrency: 'VND',
        oldPrice: 100000,
        tag: 'Standard'),
    SubscriptionEntity(
        duration: '3 months',
        price: 100000,
        concurrency: 'VND',
        oldPrice: 100000,
        tag: 'Popular'),
    SubscriptionEntity(
        duration: '1 year',
        price: 100000,
        concurrency: 'VND',
        oldPrice: 100000,
        tag: 'Best Value'),
  ];

  RxInt selectedIndex = 1.obs;

  SubscriptionEntity get currentSubscription =>
      subscriptions[selectedIndex.value];

  @override
  void onInit() {
    _mapper = Get.find<MapprService>().mappr;
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    getSubscriptions();
    super.onInit();
  }

  @override
  void onClose() {
    scrollController.removeListener(_scrollListener);
    super.onClose();
  }

  void _scrollListener() {
    headerOpacity.value = _calculateOpacity(scrollController.offset);
  }

  double _calculateOpacity(double offset) {
    if (offset == 0) return 0.0;
    if (offset > fullHeaderOpacityOffset) {
      return 1.0;
    } else {
      return offset / fullHeaderOpacityOffset;
    }
  }

  Future getSubscriptions() async {
    try {
      isLoading.value = true;
      final packages = await RevenuecatService().getAvailablePackages();
      this.packages.addAll(packages);
      final subs = _mapper.convertList<Package, SubscriptionEntity>(packages);
      subscriptions.clear();
      subscriptions.addAll(subs);
      isLoading.value = false;
    } catch (e) {
      subscriptions.clear();
      handleError(e);
    }
  }

  void onTapItem(int index) {
    selectedIndex.value = index;
  }

  void onTapPurchase() async {
    try {
      Get.context?.loaderOverlay.show();
      await RevenuecatService().purchasePackage(packages[selectedIndex.value]);
      Global.isPremium = true;
      Get.context?.loaderOverlay.hide();
      await showSuccessPopup();
      Get.find<LogFirebaseEvent>()
          .logEvent(EventLogConstants.subscriptionSuccess);
      if (Global.getUsername == "") {
        Get.offAllNamed(RouterName.welcome);
      } else {
        Get.offAllNamed(RouterName.home);
        Get.find<LearningController>().getData();
      }
    } catch (e) {
      Get.context?.loaderOverlay.hide();
      // handleError(e);
      await showErrorPopup();
    }
  }

  void openPrivacyPolicy() {
    launchUrl(Uri.parse(Constants.policy), mode: LaunchMode.inAppBrowserView);
  }

  void openTermsOfUse() {
    launchUrl(Uri.parse(Constants.terms), mode: LaunchMode.inAppBrowserView);
  }

  void restorePurchase() async {
    try {
      Get.context?.loaderOverlay.show();
      final result = await RevenuecatService().restorePurchase();
      Get.context?.loaderOverlay.hide();
      if (result) {
        Global.isPremium = true;
        await showSuccessPopup();
        if (Global.getUsername == "") {
          Get.offAllNamed(RouterName.welcome);
        } else {
          Get.offAllNamed(RouterName.home);
        }
      } else {
        await showErrorPopup();
      }
    } catch (e) {
      Get.context?.loaderOverlay.hide();
      // handleError(e);
      await showErrorPopup();
    }
  }

  Future showSuccessPopup() async {
    if (Platform.isAndroid) {
      await Popup.instance.showPaymentSuccessPopup(
          title: LocaleKeys.paymentSuccess.tr,
          description: LocaleKeys.paymentSuccessDescription.tr,
          buttonText: LocaleKeys.done.tr);
    }
  }

  Future showErrorPopup() async {
    if (Platform.isAndroid) {
      await Popup.instance.showErrorPopup(
          title: LocaleKeys.paymentFailed.tr,
          description: LocaleKeys.paymentFailedDescription.tr,
          primaryBtnText: LocaleKeys.tryAgain.tr,
          secondaryBtnText: LocaleKeys.cancel.tr,
          onPrimaryTap: () {
            Get.back();
            onTapPurchase();
          },
          onSecondaryTap: () {
            Get.back();
          });
    }
  }
}
