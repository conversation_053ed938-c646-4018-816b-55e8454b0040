import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';

mixin PassRate {
  final localService = Get.find<LocalService>();

  Future<double> getPassRate(int totalCategoryQuestion) async {
    final results = await localService.getDoneResult();
    int count = 0;
    int countRecent = 0;
    int countExam = 0;
    List uniqueQuestion = [];
    double totalScoreRecent = 0;
    int passCountRecent = 0;
    int quizDone = 0;
    double totalScore = 0;
    int totalTime = 0;
    int questionDone = 0;
    double scoreDoneQuiz = 0;
    double scoreAvg10 = 0;
    double scoreAvg = 0;
    double scoreAvgEach = 0;
    double scoreQuestionDone = 0;
    double scorePass = 0;
    double passRate2 = 0;

    for (var i = 0; i < results.length; i++) {
      // đếm lần pass liên tiếp dài nhất
      if (count < 10) {
        count++;
        // L<PERSON>y tổng số điểm để tính avg 10 bai gan nhat
        totalScoreRecent += results[i].score.roundedPrecision(0);
      }
      if (results[i].pass == true || results[i].score >= 75) {
        passCountRecent++;
      }
      //Tim question unique
      for (var t = 0; t < results[i].questions.length; t++) {
        if (!uniqueQuestion.contains(results[i].questions[t].qid)) {
          uniqueQuestion.add(results[i].questions[t].qid);
        }
      }
      //Tổng quiz làm
      quizDone++;
      // Lấy tổng số điểm để tính avg
      if (results[i].isMockTest) {
        totalScore += results[i].score.roundedPrecision(0);
        countExam++;
      }
      if (countRecent < 10) {
        //Lấy tổng thời gian
        totalTime += results[i].timeDoTest;
        // đếm số câu đã làm
        for (var j = 0; j < results[i].questions.length; j++) {
          if (results[i].questions[j].hasAnySelect) {
            questionDone++;
          }
        }
        countRecent++;
      }
    }
    //Điểm số bài test làm
    scoreDoneQuiz = quizDone / 40 * 100;
    if (scoreDoneQuiz > 100) {
      scoreDoneQuiz = 100;
    }
    // Điểm pass 10
    if (passCountRecent >= 8) {
      scorePass = 100;
    } else {
      scorePass = passCountRecent * 100 / 8;
    }
    // TB 10
    scoreAvg10 = totalScoreRecent / count;
    // TB All
    if (countExam > 0)
      scoreAvg = totalScore / countExam;
    else
      scoreAvg = 0;
    // thoi gian TB moi cau 10 klan gan nhat

    if (questionDone > 0)
      scoreAvgEach =
          double.parse(((totalTime / 60) / questionDone).toStringAsFixed(2));
    else
      scoreAvgEach = 1;

    if (scoreAvgEach <= 0.4) {
      scoreAvgEach = 100;
    } else if (0.4 < scoreAvgEach && scoreAvgEach <= 0.45) {
      scoreAvgEach = 95;
    } else if (0.45 < scoreAvgEach && scoreAvgEach <= 0.5) {
      scoreAvgEach = 92;
    } else if (0.5 < scoreAvgEach && scoreAvgEach <= 0.55) {
      scoreAvgEach = 75;
    } else if (0.55 < scoreAvgEach && scoreAvgEach <= 0.6) {
      scoreAvgEach = 67;
    } else if (0.6 < scoreAvgEach && scoreAvgEach <= 0.65) {
      scoreAvgEach = 58;
    } else if (0.65 < scoreAvgEach && scoreAvgEach <= 0.7) {
      scoreAvgEach = 50;
    } else if (0.7 < scoreAvgEach && scoreAvgEach <= 0.75) {
      scoreAvgEach = 46;
    } else if (0.75 < scoreAvgEach && scoreAvgEach <= 0.8) {
      scoreAvgEach = 42;
    } else if (0.8 < scoreAvgEach && scoreAvgEach <= 0.85) {
      scoreAvgEach = 25;
    } else if (0.85 < scoreAvgEach && scoreAvgEach <= 0.9) {
      scoreAvgEach = 17;
    } else if (0.9 < scoreAvgEach && scoreAvgEach <= 0.95) {
      scoreAvgEach = 10;
    } else if (0.95 < scoreAvgEach && scoreAvgEach <= 1) {
      scoreAvgEach = 0;
    } else if (scoreAvgEach > 1) {
      scoreAvgEach = 0;
    }

    //So cau da lam
    scoreQuestionDone = uniqueQuestion.length / totalCategoryQuestion * 100;
    if (scoreQuestionDone > 100) {
      scoreQuestionDone = 100;
    }
    var passRate = scoreDoneQuiz * 0.10 +
        scorePass * 0.15 +
        scoreAvg10 * 0.2 +
        scoreAvg * 0.25 +
        scoreAvgEach * 0.1 +
        scoreQuestionDone * 0.2;
    if (passRate.isNaN || quizDone < 10) {
      passRate = -1;
    }
    passRate2 = double.parse(passRate.toStringAsFixed(0));
    return passRate2;
  }
}
