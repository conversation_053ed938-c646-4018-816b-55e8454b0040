import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/app/utils/datetime.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_action.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_select_num_question.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/other/exam_params.dart';
import 'package:scrumpass_exam_simulator/data/models/quiz_response.dart';
import 'package:scrumpass_exam_simulator/data/models/relative_app_response.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';
import 'package:scrumpass_exam_simulator/domain/entities/relative_app.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_list_mock_test_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_relative_app_usecase.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_num_category_usecase.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/passrate_mixin.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/domain_subject/domain_subject_bottomsheet.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/practice/practice_item_model.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/practice/practice_tab.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/widgets/quick_test_slider.dart';

class LearningController extends BaseController
    with GetSingleTickerProviderStateMixin, _DbHandlerMixin, PassRate {
  LearningController() : super(ConnectionConcrete());

  late GetRelativeAppUsecase _getRelativeAppUsecase;
  late GetNumCategoryUsecase _getNumCategoryUsecase;
  late GetListMockTestUsecase _getListMockTestUsecase;

  RxBool isGettingMockTest = false.obs;

  final List<QuizEntity> mockTests = [];

  final List<PracticeTab> tabs = [PracticeTab.practice, PracticeTab.mock];

  late final TabController tabController;

  Rx<PracticeTab> selectedTab = PracticeTab.practice.obs;

  late final List<PracticeItemModel> practiceItems;

  late final List<PracticeItemModel> mockItems;

  late Mappr _mapper;

  double quickTestNumberQuestion = 10;

  List<RelativeAppEntity> relativeApps = [];

  DateTime? testDate;
  DateTime? tempTestDate;

  ResultEntity? testInProgress;
  final TourController tourController = Get.find<TourController>();
  final List<ResultEntity> results = [];
  double averageScore = 0.0;
  double latestScore = 0.0;
  int totalTime = 0;
  double timeEachQuestion = 0;
  double passRate = 0;
  int streak = 0;
  bool mockOpened = false;
  final HomeController homeController = Get.find<HomeController>();
  final List<String> practiceTestsTour = [
    LocaleKeys.productTourQuickTest.tr,
    LocaleKeys.productTourMissedTest.tr,
    LocaleKeys.productTourBookmarkTest.tr,
    LocaleKeys.productTourDomainTest.tr,
  ];
  final List<String> mockTestsTour = [
    LocaleKeys.productTourExam.tr,
  ];
  bool _alreadyStartTour = false;

  @override
  void onInit() {
    _getRelativeAppUsecase = Get.find<GetRelativeAppUsecase>();
    _getNumCategoryUsecase = Get.find<GetNumCategoryUsecase>();
    _getListMockTestUsecase = Get.find<GetListMockTestUsecase>();
    tabController = TabController(length: tabs.length, vsync: this);
    _mapper = Get.find<MapprService>().mappr;
    tabController.addListener(() {
      onTabChanged(tabController.index);
    });
    getRelativeApp();
    getTestDate();
    getData();
    getMockTest();
    initTour();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(Duration(milliseconds: 1000), () async {
        bool stateComplete = tourController.finishPrimaryTour.value;
        if (!stateComplete && homeController.done.value == false) {
          homeController.tooltipController.start();
        }
      });
    });
    super.onInit();
  }

  void initTour() {
    if (!tourController.finishPrimaryTour.value) {
      tourController.showTourPrimary();
    }
    if (Global.getStatePrimaryTour) {
      tourController.completeTourPrimary(isDisableTab: false);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        tourController.ignorePointer.value = false;
      });
    }
  }

  void getData() async {
    isLoading.value = true;
    await calculateAnalytics();
    await getTestInProgress();
    await getStreak();
    isLoading.value = false;
  }

  Future getMockTest() async {
    isGettingMockTest.value = true;
    try {
      final resp = await _getListMockTestUsecase.execute(GetMockTestParams());
      final tests = _mapper.convertList<QuizResponse, QuizEntity>(resp);
      mockTests.addAll(tests);
      await _saveQuiz(tests);
    } catch (e) {
      handleError(e);
    }
    isGettingMockTest.value = false;
    update([LearningConstants.practiceTabContent]);
  }

  Future<void> getStreak() async {
    final appData = await _getAppData();
    if (appData != null) {
      final result = calculateStreak();
      streak = result;
      _saveAppData(appData.copyWith(streak: result));
      update([LearningConstants.streakKey]);
    }
  }

  int calculateStreak() {
    // Ngày hiện tại
    DateTime today = DateTime.now();

    List<DateTime> dateList = results
        .map((element) => element.time.toStartOfDay())
        .toSet()
        .toList()
      ..sort((a, b) => b.compareTo(a));

    int streak = 0;

    for (int i = 0; i < dateList.length; i++) {
      DateTime currentDay = dateList[i];

      if (i == 0) {
        // Nếu ngày đầu tiên trong danh sách cách ngày hiện tại đúng 2 ngày, reset streak
        if (today.difference(currentDay).inDays == 2) {
          return 0;
        }
        streak++;
      } else {
        DateTime previousDay = dateList[i - 1];

        // Nếu ngày hiện tại liên tiếp với ngày trước đó, tăng streak
        if (previousDay.difference(currentDay).inDays == 1) {
          streak++;
        } else {
          // Nếu không liên tiếp, dừng tính streak
          break;
        }
      }
    }

    return streak > 0 ? streak - 1 : streak;
  }

  Future<void> calculateAnalytics() async {
    final data = await _getResults();
    results.clear();
    results.addAll(data);
    if (results.length >= 10) {
      int totalQuestion = 0;
      totalTime = 0;
      List<double> score = [];
      for (var element in results) {
        totalTime += element.timeDoTest;
        totalQuestion += element.questions.length;
        score.add(element.score);
      }
      timeEachQuestion = totalTime / totalQuestion;
      final sumScore = score.reduce((a, b) => a + b);
      averageScore = sumScore / results.length;
      latestScore = score.first;

      await calculatePassRate();
    }
  }

  Future calculatePassRate() async {
    try {
      final numCategory = await _getNumCategoryUsecase
          .execute(NumCategoryParams(appId: Constants.appId));
      passRate = await getPassRate(int.parse(numCategory));
    } catch (e) {
      handleError(e);
    }
  }

  Future getTestInProgress() async {
    final result = await _getNotDoneResult();
    testInProgress = result;
    if (result != null) {
      await _loadLinkResult(result);
    }
    update([LearningConstants.practiceTabContent]);
  }

  Future<void> getTestDate() async {
    final appData = await _getAppData();
    if (appData?.testDate?.isAfter(DateTime.now()) ?? false) {
      testDate = appData?.testDate;
      update([LearningConstants.testDateKey]);
    }
  }

  Future<void> saveTestDate() async {
    testDate = tempTestDate?.toStartOfDay();
    final appData = await _getAppData();
    final newData = appData?.copyWith(testDate: testDate);
    if (newData != null) {
      await _saveAppData(newData);
      Popup.instance.showSnackBar(
        message: LocaleKeys.updateTestDateSuccess.tr,
        type: SnackbarType.success,
      );
      update([LearningConstants.testDateKey]);
    }
  }

  Future getRelativeApp() async {
    try {
      final response = await _getRelativeAppUsecase
          .execute(RelativeAppParams(appId: Constants.appId));

      relativeApps = Get.find<MapprService>()
          .mappr
          .convertList<RelativeAppResponse, RelativeAppEntity>(response);
      update([LearningConstants.relativeAppKey]);
    } catch (e) {
      handleError(e);
    }
  }

  void onTabChanged(int index) async {
    selectedTab.value = tabs[index];
    if (!tourController.finishTourMock.value && index == 1) {
      if (_alreadyStartTour) return;
      await Get.find<HomeController>().handleMoveTour(
        offset: Global.isPremium ? 140 : 240,
      );
      tourController.ignorePointer.value = true;
      _alreadyStartTour = true;
      mockOpened = true;
      tourController.showTourMock();
      Future.delayed(Duration(milliseconds: 500), () {
        Get.find<HomeController>().tooltipController.start(8);
      });
    }
    if (index == 0 && !tourController.finishTourMock.value) {
      tourController.hideTourMock();
    }
  }

  void onTapTestInProgress({bool fromMock = false}) async {
    if (fromMock) {
      if (homeController.tourController.finishTourMock == false) return;
    } else {
      if (homeController.tourController.finishPrimaryTour == false) return;
    }
    Get.find<LogFirebaseEvent>().logEvent(EventLogConstants.clickTestInProgress,
        params: BasicLogEventParams(
                eventName: EventLogConstants.clickTestInProgress,
                screenLocation: EventLogConstants.learningScreen,
                locationType: LocationType.screen,
                locationName: EventLogConstants.learningScreen)
            .toJson());
    goTestInProgress();
  }

  void goTestInProgress() {
    Get.toNamed(RouterName.doTest,
        arguments: ExamParams(
            numQuestion: testInProgress!.questions.length,
            testType: testInProgress!.type,
            testName: testInProgress!.testName,
            result: testInProgress));
  }

  Future<bool?> popupHasInprogress() async {
    bool? isContinue;
    await Popup.instance.showActionPopup(
        title: '',
        description: LocaleKeys.incompletePopup.tr,
        showClose: true,
        secondaryBtnText: LocaleKeys.skip.tr,
        primaryBtnText: LocaleKeys.continueTest.tr,
        onPrimaryTap: () {
          isContinue = true;
        },
        onSecondaryTap: () {
          isContinue = false;
        });
    return isContinue;
  }

  void onTapQuickTest({bool fromReviewTab = false}) async {
    if (fromReviewTab) {
      if (homeController.tourController.finishTourReview == false) return;
    } else {
      if (homeController.tourController.finishPrimaryTour == false) return;
    }
    Get.find<LogFirebaseEvent>().logEvent(EventLogConstants.clickQuickTest);
    if (testInProgress != null) {
      final continueTest = await popupHasInprogress();
      if (continueTest == true) {
        goTestInProgress();
        return;
      } else if (continueTest == null) return;
    }
    Popup.instance.showBottomSheet(BottomSheetAction(
        title: LocaleKeys.bottomsheetQuickTestTitle.tr,
        child: QuickTestSlider(
          onValueChange: onValueChangeQuickTest,
          value: quickTestNumberQuestion,
        ),
        eventLocationName: EventLogConstants.btm_choosenumberofquestion,
        onDone: () {
          if (Global.isPremium == false && quickTestNumberQuestion > 10) {
            Get.toNamed(RouterName.premium);
          } else {
            Get.toNamed(RouterName.doTest,
                arguments: ExamParams(
                    numQuestion: quickTestNumberQuestion.toInt(),
                    testType: TestType.quickPractice,
                    testName: TestType.quickPractice.testTypeKey));
          }
        }));
  }

  void onTapMissedQuestionsTest() async {
    if (homeController.tourController.finishPrimaryTour == false) return;
    Get.find<LogFirebaseEvent>()
        .logEvent(EventLogConstants.clickMissedQuestionTest);
    if (Global.isPremium == false) {
      Get.toNamed(RouterName.premium);
    } else {
      if (testInProgress != null) {
        final continueTest = await popupHasInprogress();
        if (continueTest == true) {
          goTestInProgress();
          return;
        } else if (continueTest == null) return;
      }
      final result =
          await Popup.instance.showBottomSheet(BottomSheetSelectNumQuestion());
      if (result is double) {
        Get.toNamed(RouterName.doTest,
            arguments: ExamParams(
                numQuestion: int.parse(result.roundedPrecisionToString(0)),
                testType: TestType.missed,
                testName: TestType.missed.testTypeKey));
      }
    }
  }

  void onTapBookmarkQuestionTest() async {
    if (homeController.tourController.finishPrimaryTour == false) return;
    Get.find<LogFirebaseEvent>()
        .logEvent(EventLogConstants.clickBookmarkQuestionTest);
    if (Global.isPremium == false) {
      Get.toNamed(RouterName.premium);
    } else {
      if (testInProgress != null) {
        final continueTest = await popupHasInprogress();
        if (continueTest == true) {
          goTestInProgress();
          return;
        } else if (continueTest == null) return;
      }
      final result =
          await Popup.instance.showBottomSheet(BottomSheetSelectNumQuestion());
      if (result is double) {
        Get.toNamed(RouterName.doTest,
            arguments: ExamParams(
                numQuestion: int.parse(result.roundedPrecisionToString(0)),
                testType: TestType.bookmark,
                testName: TestType.bookmark.testTypeKey));
      }
    }
  }

  void onTapDomainSubjectTest() async {
    if (homeController.tourController.finishPrimaryTour == false) return;
    Get.find<LogFirebaseEvent>()
        .logEvent(EventLogConstants.clickDomainSubjectTest);
    if (Global.isPremium == false) {
      Get.toNamed(RouterName.premium);
    } else {
      if (testInProgress != null) {
        final continueTest = await popupHasInprogress();
        if (continueTest == true) {
          goTestInProgress();
          return;
        } else if (continueTest == null) return;
      }
      Popup.instance.showBottomSheet(DomainSubjectBottomsheet());
    }
  }

  void onTapFlashcard() {
    Get.find<LogFirebaseEvent>().logEvent(EventLogConstants.clickFlashcard);
    if (Global.isPremium == false) {
      Get.toNamed(RouterName.premium);
    } else {}
  }

  void onTapMiniTest() {
    if (homeController.tourController.finishTourMock == false) return;
    Get.find<LogFirebaseEvent>().logEvent(EventLogConstants.clickMiniTest);
    if (Global.isPremium == false) {
      Get.toNamed(RouterName.premium);
    } else {}
  }

  void onTapFullTest() {
    if (homeController.tourController.finishTourMock == false) return;
    Get.find<LogFirebaseEvent>().logEvent(EventLogConstants.clickFullTest);
    if (Global.isPremium == false) {
      Get.toNamed(RouterName.premium);
    } else {}
  }

  void onTapMockTest(QuizEntity test) async {
    if (homeController.tourController.finishTourMock == false) return;
    // Get.find<LogFirebaseEvent>().logEvent(EventLogConstants.clickMiniTest);
    if (Global.isPremium == false && test.premium == true) {
      Get.toNamed(RouterName.premium);
    } else {
      if (testInProgress != null) {
        final continueTest = await popupHasInprogress();
        if (continueTest == true) {
          goTestInProgress();
          return;
        } else if (continueTest == null) return;
      }
      Get.toNamed(RouterName.instruction, arguments: test);
    }
  }

  void onValueChangeQuickTest(double value) {
    quickTestNumberQuestion = value;
  }

  void onChangeTestDate(DateTime date) {
    tempTestDate = date;
  }

  void updateUIFromSetting() {
    getTestDate();
    update([LearningConstants.usernameKey]);
  }

  RichText buildRichText(String text) {
    final boldWords = ['Practice', 'Mock Test', 'Luyện tập', 'Thi thử'];

    List<InlineSpan> spans = [];
    int start = 0;

    while (start < text.length) {
      int closestIndex = text.length;
      String? closestWord;

      for (var word in boldWords) {
        int index = text.indexOf(word, start);
        if (index != -1 && index < closestIndex) {
          closestIndex = index;
          closestWord = word;
        }
      }

      if (closestWord == null) {
        spans.add(TextSpan(text: text.substring(start)));
        break;
      }

      if (closestIndex > start) {
        spans.add(TextSpan(text: text.substring(start, closestIndex)));
      }

      spans.add(TextSpan(
        text: closestWord,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ));

      start = closestIndex + closestWord.length;
    }

    return RichText(
      text: TextSpan(
        style: AppTextStyles.smallMedium.copyWith(
          color: AppColors.textInverseTitle,
        ),
        children: spans,
      ),
    );
  }
}

class LearningConstants {
  static const String relativeAppKey = "relativeApps";
  static const String testDateKey = "testDate";
  static const String usernameKey = "username";
  static const String passrateKey = "passrate";
  static const String practiceTabContent = "practiceTabContent";
  static const String streakKey = "streak";
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();

  Future<AppData?> _getAppData() async {
    return localService.getAppData();
  }

  Future<void> _saveAppData(AppData appData) async {
    return localService.saveAppData(appData);
  }

  Future<List<ResultEntity>> _getResults() async {
    return localService.getDoneResult();
  }

  Future<ResultEntity?> _getNotDoneResult() async {
    return localService.getNotDoneResult();
  }

  Future<ResultEntity?> _getNotDoneMockTestResult() async {
    return localService.getNotDoneMockTestResult();
  }

  Future<List<QuestionEntity>> _getQuestions() async {
    return localService.getAllQuestions();
  }

  Future<void> _saveQuiz(List<QuizEntity> quiz) async {
    return localService.saveQuiz(quiz);
  }

  Future<List<QuizEntity>> _getQuiz() async {
    return localService.getQuiz();
  }

  Future<void> _loadLinkResult(ResultEntity result) async {
    return localService.loadLinkResult(result);
  }
}
