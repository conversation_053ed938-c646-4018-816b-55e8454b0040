import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/app/utils/popup.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/learning/learning_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/setting/setting_controller.dart';

class WelcomeControler extends BaseController
    with GetSingleTickerProviderStateMixin, _DbHandlerMixin {
  WelcomeControler() : super(ConnectionConcrete());

  TextEditingController username = TextEditingController();
  TextEditingController examDate = TextEditingController();
  FocusNode examDateFocusNode = FocusNode();
  var isExamDateFocused = false.obs;
  late AnimationController animationController;
  late DateTime examDateValue;

  @override
  void onInit() {
    super.onInit();
    examDateFocusNode.addListener(() {
      if (examDateFocusNode.hasFocus) {
        Future.delayed(Duration(milliseconds: 500), () {
          isExamDateFocused.value = examDateFocusNode.hasFocus;
        });
      } else {
        isExamDateFocused.value = examDateFocusNode.hasFocus;
      }
    });
    animationController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
    DateTime now = DateTime.now();
    DateTime oneWeekLater = now.add(Duration(days: 7));
    String formattedDate = DateFormat('dd/MM/yyyy').format(oneWeekLater);
    examDateValue = oneWeekLater;
    examDate.text = formattedDate;
    username.addListener(() {
      update([WellComeConstants.usernameKey]);
    });
  }

  void examDateChange(newDate) {
    examDateValue = newDate;
    String formattedDate = DateFormat('dd/MM/yyyy').format(newDate);
    examDate.text = formattedDate;
  }

  Future<void> saveUserData() async {
    if (username.text.trim() == "") {
      Popup.instance.showSnackBar(
        message: LocaleKeys.noUserName.tr,
        type: SnackbarType.error,
      );
      return;
    }
    final appData = await _getAppData();
    final newData = appData?.copyWith(
      testDate: examDateValue,
      userName: username.text.trim(),
      firstLaunch: true,
    );
    if (newData != null) {
      await _saveAppData(newData);
      Global.updateUsername(username.text.trim());
      Global.updateTestDate(examDateValue);
      update([
        LearningConstants.testDateKey,
        LearningConstants.usernameKey,
        SettingConstants.firstLaunchKey,
      ]);
      Get.offAllNamed(RouterName.home);
    }
  }

  @override
  void onClose() {
    username.dispose();
    examDate.dispose();
    animationController.dispose();
    super.onClose();
  }
}

class WellComeConstants {
  static const String usernameKey = 'usernameKey';
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();

  Future<AppData?> _getAppData() async {
    return localService.getAppData();
  }

  Future<void> _saveAppData(AppData appData) async {
    return localService.saveAppData(appData);
  }
}
