import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/core/controller/base_controller.dart';
import 'package:scrumpass_exam_simulator/app/utils/connection.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/domain/usecases/get_analytic_radar_usecase.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';

class AnalyticsController extends BaseController with _DbHandlerMixin {
  AnalyticsController() : super(ConnectionConcrete());

  ScrollController scrollController = ScrollController();
  final List<ResultEntity> results = [];
  List<double> score = [];

  // Score chart
  double highestScore = 0.0;
  double lowestScore = 0.0;

  // Knowledge

  // Quiz detail
  double averageScore = 0.0;
  double latestScore = 0.0;

  // Question detail
  /// chỉ tính các câu đã chọn đáp án
  int totalQuestionDone = 0;
  int wrongQuestion = 0;
  int bookmarkedQuestion = 0;

  // Time detail
  int totalQuestion = 0;
  int totalTime = 0;
  double timeEachQuestion = 0;

  final double questionDetailChartHeight = 64;

  int scoreChartTouchedIndex = -1;

  RxBool allowScroll = false.obs;

  late GetAnalyticRadarUsecase _getAnalyticRadarUsecase;

  final List<QuestionResult> correctQuestions = [];
  final List<double> radarChartData =
      List.generate(TestDomain.values.length, (e) => 0.0);
  final HomeController homeController = Get.find<HomeController>();

  double get questionDetailWrongHeight => wrongQuestion == 0
      ? 0.0
      : wrongQuestion / totalQuestionDone * questionDetailChartHeight;
  double get questionDetailMarkedHeight => bookmarkedQuestion == 0
      ? 0.0
      : bookmarkedQuestion / totalQuestionDone * questionDetailChartHeight;

  List<double> get scoreChartData {
    final int numColumn = 7;
    final result = score.take(numColumn).toList().reversed.toList();
    final missingCount = numColumn - result.length;
    if (missingCount > 0) {
      result.addAll(List.filled(missingCount, 0));
    }
    return result.toList();
  }

  QuizDetailAverageChange get quizDetailAverageChange {
    if (results.length <= 1) return QuizDetailAverageChange.none;
    final resultsSub1 = results.sublist(1, results.length);
    final sumSub1 = resultsSub1.map((e) => e.score).reduce((a, b) => a + b);
    final averageSub1 = sumSub1 / resultsSub1.length;
    if (averageSub1 > averageScore) {
      return QuizDetailAverageChange.decrease;
    } else if (averageSub1 < averageScore) {
      return QuizDetailAverageChange.increase;
    } else {
      return QuizDetailAverageChange.none;
    }
  }

  @override
  void onInit() {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.light,
    ));
    _getAnalyticRadarUsecase = Get.find<GetAnalyticRadarUsecase>();
    getData();
    if (Global.isPremium) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!homeController.tourController.finishTourAnalyst.value) {
          Future.delayed(Duration(milliseconds: 1000), () {
            scrollController.animateTo(110,
                duration: Duration(milliseconds: 300), curve: Curves.easeOut);
          });
          Future.delayed(Duration(milliseconds: 1500), () {
            allowScroll.value = true;
            homeController.tourController.showTourAnalyst();
            homeController.tooltipController.start(12);
          });
        } else {
          allowScroll.value = true;
        }
      });
    }
    super.onInit();
  }

  void getData() async {
    isLoading.value = true;
    final results = await _getResult();
    this.results.addAll(results ?? []);
    await calculateAnalytics();
    if (Global.isPremium) await getRadar();
    isLoading.value = false;
  }

  Future getRadar() async {
    try {
      final radarData = await _getAnalyticRadarUsecase.execute(
          GetAnalyticRadarParams(
              qids: correctQuestions.map((e) => e.qid ?? '').toList()));
      final data = radarData.split(',').map((e) => double.parse(e)).toList();
      radarChartData.clear();
      radarChartData.addAll(data);
    } catch (e) {
      handleError(e);
    }
  }

  Future calculateAnalytics() async {
    List<QuestionResult> questionDone = [];
    List<QuestionResult> questionWrong = [];
    List<QuestionResult> questionMarked = [];
    for (var element in results) {
      // Time detail
      totalTime += element.timeDoTest;
      totalQuestion += element.questions.length;
      // Question detail
      for (var quest in element.questions) {
        if (quest.hasAnySelect) {
          questionDone.add(quest);
          if (quest.isCorrect ?? false) {
            correctQuestions.add(quest);
          } else {
            questionWrong.add(quest);
          }
        }

        if (quest.isBookmark == true) {
          questionMarked.add(quest);
        }
      }
      score.add(element.score);
    }

    questionDone = questionDone.toSet().toList();
    totalQuestionDone = questionDone.length;
    questionWrong = questionWrong.toSet().toList();
    wrongQuestion = questionWrong.length;
    questionMarked = questionMarked.toSet().toList();
    questionMarked = QuestionResult.removeQuestionNotBookmarked(questionMarked);
    bookmarkedQuestion = questionMarked.length;

    // Quiz detail
    final sumScore = score.isEmpty ? 0 : score.reduce((a, b) => a + b);
    averageScore = results.isEmpty ? 0 : sumScore / results.length;
    latestScore = score.isEmpty ? 0 : score.first;

    // Score chart
    highestScore = score.isEmpty ? 0 : score.reduce(max);
    lowestScore = score.isEmpty ? 0 : score.reduce(min);
    timeEachQuestion = totalQuestion == 0 ? 0 : totalTime / totalQuestion;
  }

  void onScoreChartTouched(int index) {
    scoreChartTouchedIndex = index;
    update([AnalyticsConstants.scoreChartKey]);
  }

  @override
  void onClose() {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
    ));
    super.onClose();
  }

  void moveScreendown(bool min, {double? offset}) {
    final target = (scrollController.offset + (offset ?? (min ? 200 : 250)))
        .clamp(0.0, scrollController.position.maxScrollExtent);

    scrollController.animateTo(
      target,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  void moveScreenUp(bool min, {double? offset, bool isFixed = false}) {
    final target = (isFixed
            ? offset ?? 0.0
            : (scrollController.offset - (offset ?? (min ? 200.0 : 250.0))))
        .clamp(0.0, scrollController.position.maxScrollExtent);

    scrollController.animateTo(
      target,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  Future<void> handleNextTour({bool min = false, double? offset}) async {
    moveScreendown(min, offset: offset);
    await Future.delayed(Duration(milliseconds: 300));
  }

  Future<void> handleBackTour(
      {bool min = false, double? offset, bool isFixed = false}) async {
    moveScreenUp(min, offset: offset, isFixed: isFixed);
    await Future.delayed(Duration(milliseconds: 300));
  }

  double generateTooltipMargin() {
    if (scoreChartTouchedIndex == -1) {
      return 10;
    }
    final value = scoreChartData[scoreChartTouchedIndex];
    return value <= 10 ? 20 : 10;
  }
}

class AnalyticsConstants {
  static const String scoreChartKey = 'scoreChartKey';
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();

  Future<List<ResultEntity>>? _getResult() async {
    return localService.getDoneResult();
  }
}

enum QuizDetailAverageChange { increase, decrease, none }
