import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/routes/pages.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/home/<USER>';

class App extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      // defaultTransition: Transition.fade,
      initialRoute:
          Global.getStateLaunch == true ? RouterName.home : RouterName.onboard,
      initialBinding: HomeBinding(),
      home: HomePage(),
      debugShowCheckedModeBanner: false,
      getPages: Pages.pages,
      locale: Global.locale.locale,
      fallbackLocale: const Locale("en"),
      localizationsDelegates: [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('vi'),
        Locale('en'),
        Locale('de'),
        Locale('fr'),
        Locale('hi'),
      ],
      translationsKeys: AppTranslation.translations,
      theme: ThemeData(
          primarySwatch: Colors.green,
          scaffoldBackgroundColor: Colors.white,
          appBarTheme: const AppBarTheme(
            centerTitle: true,
          ),
          dialogBackgroundColor: Colors.white,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          scrollbarTheme: ScrollbarThemeData().copyWith(
            thumbColor: WidgetStateProperty.all(AppColors.borderLine),
          )),
      builder: (context, child) {
        return ScreenUtilInit(
          minTextAdapt: true,
          child: MediaQuery(
            child: child!,
            data: MediaQuery.of(context)
                .copyWith(textScaler: TextScaler.linear(1.0)),
          ),
        );
      },
    );
  }
}
