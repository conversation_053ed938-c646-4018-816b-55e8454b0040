import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/basic_log_event.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class GetReward extends StatelessWidget {
  const GetReward({
    super.key,
    required this.screenLocation,
  });

  final String screenLocation;

  @override
  Widget build(BuildContext context) {
    return BasicLogEvent(
      eventName: EventLogConstants.clickGetRewardBanner,
      params: BasicLogEventParams(
          eventName: EventLogConstants.clickGetRewardBanner,
          screenLocation: EventLogConstants.learningScreen,
          locationType: LocationType.screen,
          locationName: EventLogConstants.learningScreen),
      child: DecoratedBox(
        decoration: BoxDecoration(
            color: AppColors.getRewardBg,
            borderRadius: BorderRadius.circular(16)),
        child: Stack(
          children: [
            Positioned(
                right: 0,
                bottom: 0,
                child: Assets.images.rewardBgIcon.image(width: 116)),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Flexible(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text.rich(TextSpan(
                              text: LocaleKeys.rewardTitle1.tr,
                              style: AppTextStyles.smallRegular.copyWith(
                                  color: AppColors.textInverseTitle,
                                  height: 1.4),
                              children: [
                                WidgetSpan(
                                  baseline: TextBaseline.alphabetic,
                                  child: Text(LocaleKeys.rewardTitle2.tr,
                                      style: AppTextStyles.smallBold.copyWith(
                                          color: AppColors.textInverseTitle,
                                          height: 1.3)),
                                ),
                                TextSpan(
                                    text: LocaleKeys.rewardTitle3.tr,
                                    style: AppTextStyles.smallRegular.copyWith(
                                        color: AppColors.textInverseTitle,
                                        height: 1.4)),
                              ])),
                          const SizedBox(height: 10),
                          DecoratedBox(
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(20)),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 12),
                              child: Text(
                                LocaleKeys.rewardButton.tr,
                                style: AppTextStyles.smallMedium.copyWith(
                                    color: AppColors.getRewardTextBtn),
                              ),
                            ),
                          )
                        ],
                      )),
                  const SizedBox(width: 12),
                  Flexible(flex: 1, child: const SizedBox())
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
