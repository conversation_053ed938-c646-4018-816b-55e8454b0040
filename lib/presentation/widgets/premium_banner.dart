import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/basic_log_event.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';

class PremiumBanner extends StatelessWidget {
  const PremiumBanner({super.key, required this.screenLocation});

  final String screenLocation;

  @override
  Widget build(BuildContext context) {
    return BasicLogEvent(
      eventName: EventLogConstants.clickPremiumBanner,
      params: BasicLogEventParams(
          eventName: EventLogConstants.clickPremiumBanner,
          screenLocation: screenLocation,
          locationType: LocationType.screen,
          locationName: EventLogConstants.learningScreen),
      child: GestureDetector(
        onTap: () {
          if (Get.find<TourController>().ignorePointer.value == true) return;
          Get.toNamed(RouterName.premium);
        },
        child: DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.premiumBannerBg,
              borderRadius: BorderRadius.circular(16)),
          child: Stack(
            children: [
              Positioned(
                  right: 0,
                  bottom: 0,
                  child: Assets.images.premiumBgIcon.image(width: 180)),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text.rich(TextSpan(
                              text: "${LocaleKeys.premiumBannerTitle1.tr}",
                              style: AppTextStyles.baseRegular.copyWith(
                                  color: AppColors.textInverseTitle,
                                  height: 1.4),
                              children: [
                                WidgetSpan(
                                  baseline: TextBaseline.alphabetic,
                                  child: Text(
                                      " ${LocaleKeys.premiumBannerTitle2.tr}",
                                      style: AppTextStyles.baseBold.copyWith(
                                          color: AppColors.textInverseTitle,
                                          height: 1.3)),
                                ),
                                TextSpan(
                                    text:
                                        " ${LocaleKeys.premiumBannerTitle3.tr} ",
                                    style: AppTextStyles.baseRegular.copyWith(
                                        color: AppColors.textInverseTitle,
                                        height: 1.4)),
                                WidgetSpan(
                                  baseline: TextBaseline.alphabetic,
                                  child: Text(
                                      "${LocaleKeys.premiumBannerTitle4.tr} ",
                                      style: AppTextStyles.baseBold.copyWith(
                                          color: AppColors.textInverseTitle,
                                          height: 1.3)),
                                ),
                              ])),
                        ),
                        const SizedBox(width: 12),
                        DecoratedBox(
                          decoration: BoxDecoration(
                              gradient: LinearGradient(
                                  colors: AppColors.premiumTagGradient),
                              borderRadius: BorderRadius.circular(20)),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            child: Text(
                              'Premium',
                              style: AppTextStyles.xSmallMedium
                                  .copyWith(color: AppColors.textPremiumTag),
                            ),
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: 12),
                    DecoratedBox(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20)),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 6, horizontal: 12),
                        child: Text(
                          LocaleKeys.premiumBannerButton.tr,
                          style: AppTextStyles.smallMedium
                              .copyWith(color: AppColors.text1st),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
