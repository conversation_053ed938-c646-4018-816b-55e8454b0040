import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';

class PageHeader extends StatelessWidget {
  const PageHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      ignoring: true,
      child: Stack(
        children: [
          Container(
            width: Get.width,
            height: 140,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                  colors: AppColors.reviewHeaderGradient,
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter),
            ),
          ),
        ],
      ),
    );
  }
}
