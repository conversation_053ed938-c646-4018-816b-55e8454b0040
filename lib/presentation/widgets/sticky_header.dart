import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';

class StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  StickyTabBarDelegate(this.tabBar);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    bool isSticky = shrinkOffset > 0;

    return Container(
      decoration: BoxDecoration(
        color: isSticky ? AppColors.stickyNavBarBg : Colors.transparent,
        boxShadow: isSticky
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 6,
                  spreadRadius: 2,
                  offset: Offset(0, 3),
                )
              ]
            : [],
      ),
      margin: const EdgeInsets.only(bottom: 12),
      child: tabBar,
    );
  }

  @override
  double get maxExtent => tabBar.preferredSize.height;
  @override
  double get minExtent => tabBar.preferredSize.height;
  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}

class StickyWidgetDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  StickyWidgetDelegate({required this.child, this.height = 50.0});

  @override
  double get minExtent => height;

  @override
  double get maxExtent => height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    final isSticked = shrinkOffset > 0;

    return Container(
      height: maxExtent,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: isSticked
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 6,
                  spreadRadius: 2,
                  offset: Offset(0, 3),
                )
              ]
            : [],
      ),
      child: isSticked
          ? SafeArea(
              top: true,
              bottom: false,
              child: child,
            )
          : child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
