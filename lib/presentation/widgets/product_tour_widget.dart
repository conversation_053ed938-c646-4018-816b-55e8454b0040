import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overlay_tooltip/overlay_tooltip.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/controllers/product_tour/product_tour_controller.dart';

class ProductTourWidget extends StatelessWidget {
  final bool isPause;
  final int index;
  final TooltipVerticalPosition position;
  final Widget child;
  final Function? tourAction;
  final Function? tourBackAction;
  final bool disable;
  final double percent;
  final String positionIndicator;
  final bool hideIndicator;
  final String title;
  final Widget? icon;
  final String subtitle;
  final String description;
  final Widget? customDescription;
  final bool review;
  final bool analystics;
  const ProductTourWidget({
    super.key,
    required this.child,
    this.position = TooltipVerticalPosition.BOTTOM,
    this.isPause = false,
    required this.index,
    this.tourAction,
    this.tourBackAction,
    this.disable = false,
    this.percent = 0,
    this.positionIndicator = TourController.positionLeft,
    this.hideIndicator = false,
    this.icon,
    required this.title,
    this.subtitle = "",
    required this.description,
    this.customDescription,
    this.review = false,
    this.analystics = false,
  });

  static Widget generateImgIcon(int index) {
    if (index == 2) {
      return Image.asset(
        Assets.images.lightning.path,
        height: 24,
      );
    }

    Map<int, String> iconMap = {
      3: Assets.images.redClose.path,
      4: Assets.images.tag.path,
      5: Assets.images.dart.path,
      6: Assets.images.notes.path,
    };

    return iconMap.containsKey(index)
        ? index == 5
            ? Container(
                margin: EdgeInsets.only(bottom: 5),
                child: Image.asset(
                  iconMap[index]!,
                  height: 24,
                ),
              )
            : Image.asset(
                iconMap[index]!,
                height: 24,
              )
        : Container();
  }

  @override
  Widget build(BuildContext context) {
    if (disable) {
      return child;
    }
    return OverlayTooltipItem(
      displayIndex: index,
      tooltipVerticalPosition: position,
      tooltip: (tooltipController) {
        return Container(
          width: analystics ? Get.width : null,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (position == TooltipVerticalPosition.BOTTOM) ...[
                lineProductTour()
              ],
              Stack(
                children: [
                  Container(
                    clipBehavior: Clip.none,
                    width: Get.width - 32,
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.5),
                        width: 1,
                      ),
                      gradient: LinearGradient(
                        begin: Alignment(-0.91, -0.41),
                        end: Alignment(0.91, 0.41),
                        colors: AppColors.productTourGradient,
                        stops: [0.1524, 0.737],
                      ),
                    ),
                    child: Center(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.only(right: 8),
                                child: icon!,
                              ),
                              Text(
                                title,
                                style: AppTextStyles.largeBold.copyWith(
                                    color: HexColor("FFF170"), height: 1),
                              ),
                            ],
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(vertical: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (subtitle != "")
                                  Text(
                                    subtitle,
                                    style: AppTextStyles.smallMedium.copyWith(
                                      color: AppColors.textInverseTitle,
                                    ),
                                  ),
                                customDescription ??
                                    Text(
                                      description,
                                      style: AppTextStyles.smallMedium.copyWith(
                                        color: AppColors.textInverseTitle,
                                      ),
                                    ),
                              ],
                            ),
                          ),
                          SizedBox(height: 8),
                          if (!hideIndicator) ...[
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(64.0),
                              ),
                              height: 4,
                              width: double.infinity,
                              child: LinearProgressIndicator(
                                value: (percent / 100),
                                color: Colors.white,
                                backgroundColor: Colors.white.withOpacity(0.2),
                              ),
                            ),
                            SizedBox(height: 12),
                          ],
                          Row(
                            children: [
                              if (index != 0 &&
                                  index != 8 &&
                                  index != 10 &&
                                  index != 12)
                                GestureDetector(
                                  onTap: () async {
                                    if (review) {
                                      tooltipController.pause();
                                      if (tourBackAction != null)
                                        await tourBackAction!();
                                      Future.delayed(
                                          Duration(milliseconds: 500),
                                          () async {
                                        tooltipController.start(10);
                                      });
                                    } else {
                                      if (tourBackAction != null)
                                        await tourBackAction!();
                                      tooltipController.previous();
                                    }
                                  },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 4,
                                      horizontal: 16,
                                    ),
                                    child: Text(
                                      LocaleKeys.back.tr,
                                      style: AppTextStyles.smallMedium.copyWith(
                                        color: AppColors.textBtn1st,
                                      ),
                                    ),
                                  ),
                                ),
                              Expanded(child: Container()),
                              GestureDetector(
                                onTap: () async {
                                  if (review) {
                                    tooltipController.pause();
                                    if (tourAction != null) await tourAction!();
                                    Future.delayed(Duration(milliseconds: 500),
                                        () async {
                                      if (isPause == true)
                                        tooltipController.pause();
                                      else
                                        tooltipController.start(11);
                                    });
                                  } else {
                                    if (tourAction != null) await tourAction!();
                                    if (isPause == true)
                                      tooltipController.pause();
                                    else
                                      tooltipController.next();
                                  }
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: 4,
                                    horizontal: 16,
                                  ),
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                        color: AppColors.borderLine,
                                        width: 1,
                                      ),
                                      borderRadius: BorderRadius.circular(8)),
                                  child: Text(
                                    (isPause ? "OK" : LocaleKeys.next.tr),
                                    style: AppTextStyles.smallMedium.copyWith(
                                      color: AppColors.textBtn1st,
                                    ),
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Image.asset(
                      Assets.images.topCloud.path,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    child: IgnorePointer(
                      ignoring: true,
                      child: Image.asset(
                        Assets.images.botomCloud.path,
                      ),
                    ),
                  ),
                ],
              ),
              if (position == TooltipVerticalPosition.TOP) ...[
                Transform.rotate(
                  angle: math.pi,
                  child: lineProductTour(),
                ),
              ],
            ],
          ),
        );
      },
      child: child,
    );
  }

  Widget lineProductTour() {
    if (positionIndicator == TourController.positionRight) {
      return Container(
        width: Get.width - 32,
        margin: EdgeInsets.only(right: hideIndicator && index != 8 ? 32 : 0),
        alignment: Alignment.centerRight,
        child: Padding(
          padding: EdgeInsets.only(right: (Get.width * 0.23)),
          child: SvgPicture.asset(Assets.images.line.path),
        ),
      );
    } else if (positionIndicator == TourController.positionLeft) {
      return Container(
        width: Get.width - 32,
        margin: EdgeInsets.only(left: hideIndicator ? 32 : 0),
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: EdgeInsets.only(left: (Get.width * 0.23)),
          child: SvgPicture.asset(Assets.images.line.path),
        ),
      );
    } else {
      return Center(
        child: SvgPicture.asset(Assets.images.line.path),
      );
    }
  }
}

class ProductTourScaffold extends StatelessWidget {
  final Widget child;
  final HomeController controller;
  final int duration;
  const ProductTourScaffold(
      {super.key,
      required this.child,
      required this.controller,
      this.duration = 1000});

  @override
  Widget build(BuildContext context) {
    return OverlayTooltipScaffold(
        tooltipAnimationCurve: Curves.linear,
        tooltipAnimationDuration: Duration(milliseconds: 500),
        controller: controller.tooltipController,
        startWhen: (initializedWidgetLength) async {
          return false;
        },
        preferredOverlay: Stack(
          children: [
            Container(
              height: double.infinity,
              width: double.infinity,
              color: Colors.black.withOpacity(.7),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: () {
                  controller.tourController.completeAllTour();
                  controller.tooltipController.pause();
                },
                child: Container(
                  color: Colors.transparent,
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 30),
                  child: Assets.images.close.svg(
                    width: 36,
                    colorFilter: ColorFilter.mode(
                      AppColors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        builder: (context) => child);
  }
}
