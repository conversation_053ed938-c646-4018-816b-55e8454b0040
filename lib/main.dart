import 'package:amplitude_flutter/amplitude.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:isar/isar.dart';
import 'package:scrumpass_exam_simulator/app/core/database/local_data_source.dart';
import 'package:scrumpass_exam_simulator/app/core/services/amplitude_service.dart';
import 'package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart';
import 'package:scrumpass_exam_simulator/app/util/dependency.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/utils/notification_service.dart';
import 'package:scrumpass_exam_simulator/app/utils/revenuecat_service.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/firebase_options.dart';
import 'package:scrumpass_exam_simulator/presentation/app.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:sentry_flutter/sentry_flutter.dart';

void main() async {
  DependencyCreator.init();
  WidgetsFlutterBinding.ensureInitialized();
  // await GetStorage.init();
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.light,
    systemNavigationBarColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
    systemNavigationBarContrastEnforced: false,
  ));
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge,
      overlays: [SystemUiOverlay.bottom]);
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await initServices();
  await initLocalData();
  runApp(App());
}

Future<void> initServices() async {
  print('starting services ...');
  tz.initializeTimeZones();
  await Get.putAsync<Isar>(() => IsarDb().isar);
  await Get.putAsync<Amplitude>(() => AmplitudeService().amplitude);
  Get.lazyPut(() => MapprService());
  Get.lazyPut(() => LocalService());
  Get.lazyPut(() => LogFirebaseEvent());
  await initSentry();
  NotificationService().initNotification();
  await RevenuecatService().initPlatformState();
  // await RevenuecatService().purchaserInfo();
  print('All services started...');
}

Future initLocalData() async {
  await Get.find<LocalService>().initAppData();
  await Get.find<LocalService>().generateSessionId();
}

Future initSentry() async {
  if (kReleaseMode) {
    await SentryFlutter.init(
      (options) {
        options.dsn =
            'https://<EMAIL>/4508107558420560';
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
        // We recommend adjusting this value in production.
        options.tracesSampleRate = 1.0;
        // The sampling rate for profiling is relative to tracesSampleRate
        // Setting to 1.0 will profile 100% of sampled transactions:
        options.profilesSampleRate = 1.0;
        options.sendDefaultPii = true;
        options.maxRequestBodySize = MaxRequestBodySize.always;
        options.maxResponseBodySize = MaxResponseBodySize.always;
      },
    );
  }
}
