import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/exam/exam_binding.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/controllers/instruction/instruction_binding.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/onboarding/onbroading_binding.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/premium/premium_binding.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/result_detail/result_detail_binding.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/welcome/welcome_binding.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/exam/exam_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/home/<USER>';
import 'package:scrumpass_exam_simulator/presentation/pages/instruction/instruction_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/onboarding/onboarding_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/premium/premium_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/result/result_page.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/setting/about_us.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/welcome/welcome_page.dart';

import 'router_name.dart';

mixin Pages {
  static List<GetPage> pages = [
    // GetPage(
    //   name: RouterName.splash,
    //   page: () => const SplashPage(),
    // ),
    GetPage(
      name: RouterName.home,
      page: () => HomePage(),
      binding: HomeBinding(),
      transition: Transition.fadeIn,
      transitionDuration: Duration(milliseconds: 300),
    ),
    GetPage(
      name: RouterName.aboutUs,
      page: () => AboutUsPage(),
    ),
    GetPage(
      name: RouterName.doTest,
      page: () => ExamPage(),
      binding: ExamBinding(),
    ),
    GetPage(
      name: RouterName.resultDetail,
      page: () => ResultPage(),
      binding: ResultDetailBinding(),
    ),
    GetPage(
      name: RouterName.premium,
      page: () => PremiumPage(),
      binding: PremiumBinding(),
      transition: Transition.fadeIn,
      transitionDuration: Duration(milliseconds: 300),
    ),
    GetPage(
      name: RouterName.onboard,
      page: () => OnboardingPage(),
      binding: OnbroadingBinding(),
      transition: Transition.fadeIn,
      transitionDuration: Duration(milliseconds: 300),
    ),
    GetPage(
      name: RouterName.welcome,
      page: () => WelcomePage(),
      binding: WelcomeBinding(),
      transition: Transition.fadeIn,
      transitionDuration: Duration(milliseconds: 300),
    ),
    GetPage(
      name: RouterName.instruction,
      page: () => InstructionPage(),
      binding: InstructionBinding(),
      transition: Transition.fadeIn,
      transitionDuration: Duration(milliseconds: 300),
    ),
  ];
}
