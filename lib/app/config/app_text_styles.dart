import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';

class AppTextStyles {
  static TextStyle _textStyle(
      {double? fontSize,
      FontWeight? fontWeight,
      Color? color,
      double? height}) {
    return GoogleFonts.inter(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
    );
  }

  static TextStyle title = _textStyle(
    fontSize: 16.spMin,
    fontWeight: FontWeight.w500,
    color: Colors.black,
  );

  static TextStyle body = _textStyle(
    fontSize: 13.spMin,
    color: Colors.grey,
  );

  static TextStyle largeBold = _textStyle(
    fontSize: 18.spMin,
    fontWeight: FontWeight.w700,
    color: AppColors.textTitle,
  );

  static TextStyle smallRegular = _textStyle(
    fontSize: 14.spMin,
    fontWeight: FontWeight.w400,
    color: AppColors.textTitle,
  );

  static TextStyle smallMedium = _textStyle(
    fontSize: 14.spMin,
    fontWeight: FontWeight.w500,
    color: AppColors.textTitle,
  );

  static TextStyle smallSemiBold = _textStyle(
    fontSize: 14.spMin,
    fontWeight: FontWeight.w600,
    color: AppColors.textTitle,
  );

  static TextStyle smallBold = _textStyle(
    fontSize: 14.spMin,
    fontWeight: FontWeight.w700,
    color: AppColors.textTitle,
  );

  static TextStyle baseMedium = _textStyle(
    fontSize: 16.spMin,
    fontWeight: FontWeight.w500,
    color: AppColors.textTitle,
  );

  static TextStyle baseRegular = _textStyle(
    fontSize: 16.spMin,
    fontWeight: FontWeight.w400,
    color: AppColors.textTitle,
  );

  static TextStyle baseBold = _textStyle(
    fontSize: 16.spMin,
    fontWeight: FontWeight.w700,
    color: AppColors.textTitle,
  );

  static TextStyle baseSemiBold = _textStyle(
    fontSize: 16.spMin,
    fontWeight: FontWeight.w600,
    color: AppColors.textTitle,
  );

  static TextStyle largeRegular = _textStyle(
    fontSize: 18.spMin,
    fontWeight: FontWeight.w400,
    color: AppColors.textTitle,
  );

  static TextStyle largeSemiBold = _textStyle(
    fontSize: 18.spMin,
    fontWeight: FontWeight.w600,
    color: AppColors.textTitle,
  );

  static TextStyle xLargeBold = _textStyle(
    fontSize: 20.spMin,
    fontWeight: FontWeight.w700,
    color: AppColors.textTitle,
  );

  static TextStyle xLargeSemiBold = _textStyle(
    fontSize: 20.spMin,
    fontWeight: FontWeight.w600,
    color: AppColors.textTitle,
  );

  static TextStyle xLargeMedium = _textStyle(
    fontSize: 20.spMin,
    fontWeight: FontWeight.w500,
    color: AppColors.textTitle,
  );

  static TextStyle xxLargeMedium = _textStyle(
    fontSize: 24.spMin,
    fontWeight: FontWeight.w500,
    color: AppColors.textTitle,
  );

  static TextStyle xxxLargeThin = _textStyle(
    fontSize: 28.spMin,
    fontWeight: FontWeight.w300,
    color: AppColors.textTitle,
  );

  static TextStyle xxLargeBold = _textStyle(
    fontSize: 24.spMin,
    fontWeight: FontWeight.w700,
    color: AppColors.textTitle,
  );

  static TextStyle xxxLargeSemiBold = _textStyle(
    fontSize: 28.spMin,
    fontWeight: FontWeight.w600,
    color: AppColors.textTitle,
  );

  static TextStyle xxxxLargeBold = _textStyle(
    fontSize: 40.spMin,
    fontWeight: FontWeight.w700,
    color: AppColors.textTitle,
  );

  static TextStyle xxSmallRegular = _textStyle(
    fontSize: 10.spMin,
    fontWeight: FontWeight.w400,
    color: AppColors.textTitle,
  );

  static TextStyle xxSmallMedium = _textStyle(
    fontSize: 10.spMin,
    fontWeight: FontWeight.w500,
    color: AppColors.textTitle,
  );

  static TextStyle xSmallRegular = _textStyle(
    fontSize: 12.spMin,
    fontWeight: FontWeight.w400,
    color: AppColors.textTitle,
  );

  static TextStyle xSmallMedium = _textStyle(
    fontSize: 12.spMin,
    fontWeight: FontWeight.w500,
    color: AppColors.textTitle,
  );

  static TextStyle xSmallSemiBold = _textStyle(
    fontSize: 12.spMin,
    fontWeight: FontWeight.w600,
    color: AppColors.textTitle,
  );

  static TextStyle titleBold = _textStyle(
    fontSize: 18.spMin,
    fontWeight: FontWeight.w700,
    color: AppColors.textInverseTitle,
  );
}
