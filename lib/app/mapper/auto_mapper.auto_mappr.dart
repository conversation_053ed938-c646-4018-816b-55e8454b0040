// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;
import 'package:purchases_flutter/models/package_wrapper.dart' as _i9;

import '../../data/models/question_response.dart' as _i4;
import '../../data/models/quiz_response.dart' as _i7;
import '../../data/models/relative_app_response.dart' as _i2;
import '../../domain/entities/question.dart' as _i5;
import '../../domain/entities/quiz.dart' as _i8;
import '../../domain/entities/relative_app.dart' as _i3;
import '../../domain/entities/result.dart' as _i6;
import '../../domain/entities/subscripiton.dart' as _i10;
import 'auto_mapper.dart' as _i11;

/// {@template package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
/// Available mappings:
/// - `RelativeAppResponse` → `RelativeAppEntity`.
/// - `QuestionResponse` → `QuestionEntity`.
/// - `OptionResponse` → `OptionEntity`.
/// - `QuestionEntity` → `QuestionResult`.
/// - `QuestionResult` → `QuestionEntity`.
/// - `QuizResponse` → `QuizEntity`.
/// - `Package` → `SubscriptionEntity`.
/// {@endtemplate}
class $Mappr implements _i1.AutoMapprInterface {
  const $Mappr();

  Type _typeOf<T>() => T;

  List<_i1.AutoMapprInterface> get _delegates => const [];

  /// {@macro AutoMapprInterface:canConvert}
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.RelativeAppResponse>() ||
            sourceTypeOf == _typeOf<_i2.RelativeAppResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.RelativeAppEntity>() ||
            targetTypeOf == _typeOf<_i3.RelativeAppEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.QuestionResponse>() ||
            sourceTypeOf == _typeOf<_i4.QuestionResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.QuestionEntity>() ||
            targetTypeOf == _typeOf<_i5.QuestionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.OptionResponse>() ||
            sourceTypeOf == _typeOf<_i4.OptionResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.OptionEntity>() ||
            targetTypeOf == _typeOf<_i5.OptionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i5.QuestionEntity>() ||
            sourceTypeOf == _typeOf<_i5.QuestionEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.QuestionResult>() ||
            targetTypeOf == _typeOf<_i6.QuestionResult?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i6.QuestionResult>() ||
            sourceTypeOf == _typeOf<_i6.QuestionResult?>()) &&
        (targetTypeOf == _typeOf<_i5.QuestionEntity>() ||
            targetTypeOf == _typeOf<_i5.QuestionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i7.QuizResponse>() ||
            sourceTypeOf == _typeOf<_i7.QuizResponse?>()) &&
        (targetTypeOf == _typeOf<_i8.QuizEntity>() ||
            targetTypeOf == _typeOf<_i8.QuizEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.Package>() ||
            sourceTypeOf == _typeOf<_i9.Package?>()) &&
        (targetTypeOf == _typeOf<_i10.SubscriptionEntity>() ||
            targetTypeOf == _typeOf<_i10.SubscriptionEntity?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  /// {@macro AutoMapprInterface:convert}
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:tryConvert}
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  /// {@macro AutoMapprInterface:convertIterable}
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:convertList}
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// {@macro AutoMapprInterface:convertSet}
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.RelativeAppResponse>() ||
            sourceTypeOf == _typeOf<_i2.RelativeAppResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.RelativeAppEntity>() ||
            targetTypeOf == _typeOf<_i3.RelativeAppEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$RelativeAppResponse_To__i3$RelativeAppEntity(
          (model as _i2.RelativeAppResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.QuestionResponse>() ||
            sourceTypeOf == _typeOf<_i4.QuestionResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.QuestionEntity>() ||
            targetTypeOf == _typeOf<_i5.QuestionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$QuestionResponse_To__i5$QuestionEntity(
          (model as _i4.QuestionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.OptionResponse>() ||
            sourceTypeOf == _typeOf<_i4.OptionResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.OptionEntity>() ||
            targetTypeOf == _typeOf<_i5.OptionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$OptionResponse_To__i5$OptionEntity(
          (model as _i4.OptionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i5.QuestionEntity>() ||
            sourceTypeOf == _typeOf<_i5.QuestionEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.QuestionResult>() ||
            targetTypeOf == _typeOf<_i6.QuestionResult?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i5$QuestionEntity_To__i6$QuestionResult(
          (model as _i5.QuestionEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i6.QuestionResult>() ||
            sourceTypeOf == _typeOf<_i6.QuestionResult?>()) &&
        (targetTypeOf == _typeOf<_i5.QuestionEntity>() ||
            targetTypeOf == _typeOf<_i5.QuestionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i6$QuestionResult_To__i5$QuestionEntity(
          (model as _i6.QuestionResult?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i7.QuizResponse>() ||
            sourceTypeOf == _typeOf<_i7.QuizResponse?>()) &&
        (targetTypeOf == _typeOf<_i8.QuizEntity>() ||
            targetTypeOf == _typeOf<_i8.QuizEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i7$QuizResponse_To__i8$QuizEntity(
          (model as _i7.QuizResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.Package>() ||
            sourceTypeOf == _typeOf<_i9.Package?>()) &&
        (targetTypeOf == _typeOf<_i10.SubscriptionEntity>() ||
            targetTypeOf == _typeOf<_i10.SubscriptionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$Package_To__i10$SubscriptionEntity(
          (model as _i9.Package?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  /// {@macro AutoMapprInterface:useSafeMapping}
  /// {@macro package:scrumpass_exam_simulator/app/mapper/auto_mapper.dart}
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i3.RelativeAppEntity _map__i2$RelativeAppResponse_To__i3$RelativeAppEntity(
      _i2.RelativeAppResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping RelativeAppResponse → RelativeAppEntity failed because RelativeAppResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<RelativeAppResponse, RelativeAppEntity> to handle null values during mapping.');
    }
    return _i3.RelativeAppEntity(
      model.name,
      model.iosId,
      model.androidId,
      model.icon,
    );
  }

  _i5.QuestionEntity _map__i4$QuestionResponse_To__i5$QuestionEntity(
      _i4.QuestionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping QuestionResponse → QuestionEntity failed because QuestionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<QuestionResponse, QuestionEntity> to handle null values during mapping.');
    }
    return _i5.QuestionEntity(
      qid: model.qid,
      type: model.type,
      question: model.question,
      options: model.options
          ?.map<_i5.OptionEntity>(
              (value) => _map__i4$OptionResponse_To__i5$OptionEntity(value))
          .toList(),
      description: model.description,
      nextQuestion: model.nextQuestion,
    );
  }

  _i5.OptionEntity _map__i4$OptionResponse_To__i5$OptionEntity(
      _i4.OptionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OptionResponse → OptionEntity failed because OptionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OptionResponse, OptionEntity> to handle null values during mapping.');
    }
    return _i5.OptionEntity(
      oid: model.oid,
      qOption: model.qOption,
      score: model.score,
    );
  }

  _i6.QuestionResult _map__i5$QuestionEntity_To__i6$QuestionResult(
      _i5.QuestionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping QuestionEntity → QuestionResult failed because QuestionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<QuestionEntity, QuestionResult> to handle null values during mapping.');
    }
    return _i6.QuestionResult(
      question: model.question,
      qid: model.qid,
      type: model.type,
      options: model.options,
      description: model.description,
      isBookmark: model.isBookmark,
      status: model.status,
      isCorrect: model.isCorrect,
    );
  }

  _i5.QuestionEntity _map__i6$QuestionResult_To__i5$QuestionEntity(
      _i6.QuestionResult? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping QuestionResult → QuestionEntity failed because QuestionResult was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<QuestionResult, QuestionEntity> to handle null values during mapping.');
    }
    return _i5.QuestionEntity(
      qid: model.qid,
      type: model.type,
      question: model.question,
      options: model.options,
      description: model.description,
      isBookmark: model.isBookmark ?? false,
      status: _i11.Mappr.mapToStatus(model),
      isCorrect: model.isCorrect,
    );
  }

  _i8.QuizEntity _map__i7$QuizResponse_To__i8$QuizEntity(
      _i7.QuizResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping QuizResponse → QuizEntity failed because QuizResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<QuizResponse, QuizEntity> to handle null values during mapping.');
    }
    return _i8.QuizEntity(
      quid: model.quid,
      quizName: model.quizName,
      noq: _i11.Mappr.mapToNoqQuiz(model),
      duration: _i11.Mappr.mapToDurationQuiz(model),
      passPercentage: _i11.Mappr.mapToPassPercentQuiz(model),
      description: model.description,
      premium: model.premium,
    );
  }

  _i10.SubscriptionEntity _map__i9$Package_To__i10$SubscriptionEntity(
      _i9.Package? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Package → SubscriptionEntity failed because Package was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Package, SubscriptionEntity> to handle null values during mapping.');
    }
    return _i10.SubscriptionEntity(
      duration: _i11.Mappr.mapSubscripitonDuration(model),
      price: _i11.Mappr.mapSubscripitonPrice(model),
      concurrency: _i11.Mappr.mapSubscripitonConcurrency(model),
      oldPrice: _i11.Mappr.mapSubscripitonOldPrice(model),
      tag: _i11.Mappr.mapSubscripitonTag(model),
    );
  }
}
