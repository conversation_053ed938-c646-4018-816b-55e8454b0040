import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:get/get.dart';
import 'package:purchases_flutter/models/package_wrapper.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/data/models/quiz_response.dart';
import 'package:scrumpass_exam_simulator/data/models/relative_app_response.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';
import 'package:scrumpass_exam_simulator/domain/entities/relative_app.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';
import 'package:scrumpass_exam_simulator/domain/entities/subscripiton.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

import 'auto_mapper.auto_mappr.dart';

@AutoMappr([
  MapType<RelativeAppResponse, RelativeAppEntity>(),

  // Exam
  MapType<QuestionResponse, QuestionEntity>(),
  MapType<OptionResponse, OptionEntity>(),
  MapType<QuestionEntity, QuestionResult>(),
  MapType<QuestionResult, QuestionEntity>(fields: [
    Field('status', custom: Mappr.mapToStatus),
    Field('isBookmark', whenNull: false),
  ]),
  MapType<QuizResponse, QuizEntity>(fields: [
    Field('noq', custom: Mappr.mapToNoqQuiz),
    Field('duration', custom: Mappr.mapToDurationQuiz),
    Field('passPercentage', custom: Mappr.mapToPassPercentQuiz),
  ]),

  // RevenueCat
  MapType<Package, SubscriptionEntity>(fields: [
    Field('duration', custom: Mappr.mapSubscripitonDuration),
    Field('price', custom: Mappr.mapSubscripitonPrice),
    Field('concurrency', custom: Mappr.mapSubscripitonConcurrency),
    Field('oldPrice', custom: Mappr.mapSubscripitonOldPrice),
    Field('tag', custom: Mappr.mapSubscripitonTag),
  ])
])
class Mappr extends $Mappr {
  static QuestionStatus mapToStatus(QuestionResult input) {
    if (input.status == null) return QuestionStatus.notAnswered;
    return input.status!;
  }

  static String mapSubscripitonDuration(Package input) {
    switch (input.packageType) {
      case PackageType.monthly:
        return '1 ${LocaleKeys.months.tr.replaceAll('months', 'month')}';
      case PackageType.threeMonth:
        return '3 ${LocaleKeys.months.tr}';
      case PackageType.sixMonth:
        return '6 ${LocaleKeys.months.tr}';
      case PackageType.annual:
        return '12 ${LocaleKeys.months.tr}';
      default:
        return 'Unknown';
    }
  }

  static double mapSubscripitonPrice(Package input) {
    return input.storeProduct.price;
  }

  static String mapSubscripitonConcurrency(Package input) {
    return input.storeProduct.currencyCode;
  }

  static double mapSubscripitonOldPrice(Package input) {
    switch (input.packageType) {
      case PackageType.monthly:
        return Constants.monthlyOldPrice;
      case PackageType.threeMonth:
        return Constants.quarterlyOldPrice;
      case PackageType.annual:
        return Constants.yearlyOldPrice;
      default:
        return 0;
    }
  }

  static String mapSubscripitonTag(Package input) {
    switch (input.packageType) {
      case PackageType.monthly:
        return LocaleKeys.standard.tr;
      case PackageType.threeMonth:
      case PackageType.sixMonth:
        return LocaleKeys.popular.tr;
      case PackageType.annual:
        return LocaleKeys.bestValue.tr;
      default:
        return 'Unknown';
    }
  }

  static int mapToNoqQuiz(QuizResponse input) {
    return int.tryParse(input.noq) ?? 0;
  }

  static int mapToDurationQuiz(QuizResponse input) {
    return int.tryParse(input.duration) ?? 0;
  }

  static int mapToPassPercentQuiz(QuizResponse input) {
    return int.tryParse(input.passPercentage) ?? 0;
  }
}

class MapprService extends GetxService {
  Mappr get mappr => Mappr();
}
