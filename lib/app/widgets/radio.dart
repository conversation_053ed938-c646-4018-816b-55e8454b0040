import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';

class AnimatedRadioButton extends StatefulWidget {
  final bool isSelected;
  final ValueChanged<bool> onChanged;
  final Color? selectedColor;
  final Color? notSelectedColor;

  AnimatedRadioButton(
      {required this.isSelected,
      required this.onChanged,
      this.selectedColor,
      this.notSelectedColor});

  @override
  _AnimatedRadioButtonState createState() => _AnimatedRadioButtonState();
}

class _AnimatedRadioButtonState extends State<AnimatedRadioButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 500),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleSelection() {
    widget.onChanged(!widget.isSelected);
    if (widget.isSelected) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleSelection,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
              color: widget.isSelected
                  ? widget.selectedColor ?? AppColors.baseBlueBorder
                  : widget.notSelectedColor ?? AppColors.icHint),
          color: Colors.transparent,
        ),
        child: Center(
          child: widget.isSelected
              ? Transform.scale(
                  scale: _scaleAnimation.value,
                  child: SizedBox(
                      width: 12,
                      height: 12,
                      child: DecoratedBox(
                          decoration: BoxDecoration(
                              color: widget.selectedColor ??
                                  AppColors.baseBlueBorder,
                              shape: BoxShape.circle))),
                )
              : null,
        ),
      ),
    );
  }
}
