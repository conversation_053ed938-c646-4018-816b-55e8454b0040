import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';

class BottomSheetEmpty extends StatelessWidget {
  const BottomSheetEmpty({
    super.key,
    required this.title,
    required this.child,
  });

  final String title;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: true,
      child: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: Get.height * 0.9),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 36),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          style: AppTextStyles.baseBold,
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    top: 0,
                    left: 0,
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 20),
                        child: Assets.images.close.svg(width: 28),
                      ),
                      onTap: () {
                        Get.back();
                      },
                    ),
                  )
                ],
              ),
              Divider(
                height: 1,
                thickness: 1,
                color: AppColors.divider,
              ),
              child,
            ],
          ),
        ),
      ),
    );
  }
}
