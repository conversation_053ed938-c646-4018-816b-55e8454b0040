import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/routes/router_name.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/basic_log_event.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/learning/widgets/quick_test_slider.dart';

class BottomSheetSelectNumQuestion extends StatefulWidget {
  const BottomSheetSelectNumQuestion({
    super.key,
    this.eventLocationName = '',
  });

  final String eventLocationName;

  @override
  State<BottomSheetSelectNumQuestion> createState() =>
      _BottomSheetSelectNumQuestionState();
}

class _BottomSheetSelectNumQuestionState
    extends State<BottomSheetSelectNumQuestion> {
  double testNumberQuestion = 10;

  void onChangeValue(double value) {
    testNumberQuestion = value;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 36),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      LocaleKeys.bottomsheetQuickTestTitle.tr,
                      style: AppTextStyles.baseBold,
                    )
                  ],
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                child: GestureDetector(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    child: Assets.images.close.svg(width: 28),
                  ),
                  onTap: () {
                    Get.back();
                  },
                ),
              )
            ],
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: AppColors.divider,
          ),
          QuickTestSlider(
            onValueChange: onChangeValue,
            value: testNumberQuestion,
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: Get.mediaQuery.size.width,
              child: BasicLogEvent(
                eventName: EventLogConstants.clickBtnConfirm,
                params: BasicLogEventParams(
                    eventName: EventLogConstants.clickBtnConfirm,
                    screenLocation: EventLogConstants.learningScreen,
                    locationType: LocationType.bottomsheet,
                    locationName: widget.eventLocationName),
                child: PrimaryButton(
                    text: LocaleKeys.confirm.tr,
                    onTap: () {
                      if (Global.isPremium == false) {
                        if (testNumberQuestion > 10) {
                          Get.toNamed(RouterName.premium);
                        }
                      }
                      Get.back(result: testNumberQuestion);
                    }),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
