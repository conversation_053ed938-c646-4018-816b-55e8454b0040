import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/bottomsheet/bottomsheet_empty.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/system_log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/radio.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class BottomSheetSelectionWidget extends StatefulWidget {
  const BottomSheetSelectionWidget({
    required this.title,
    required this.data,
    this.beforeWidget,
    this.ignoreLog = false,
    super.key,
  });

  final String title;
  final List<BottomSheetModel> data;
  final Widget? beforeWidget;
  final bool ignoreLog;

  @override
  State<BottomSheetSelectionWidget> createState() =>
      _BottomSheetSelectionWidgetState();
}

class _BottomSheetSelectionWidgetState
    extends State<BottomSheetSelectionWidget> {
  dynamic value;

  @override
  Widget build(BuildContext context) {
    return BottomSheetEmpty(
        title: widget.title,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              if (widget.beforeWidget != null) widget.beforeWidget!,
              _FilterListView(data: widget.data, onChanged: onSelected),
              const SizedBox(height: 24),
              if (widget.ignoreLog)
                PrimaryButton(
                  text: LocaleKeys.confirm.tr,
                  onTap: onConfirm,
                )
              else
                SystemLogEvent(
                  eventName: EventLogConstants.clickBtnConfirm,
                  child: PrimaryButton(
                    text: LocaleKeys.confirm.tr,
                    onTap: onConfirm,
                  ),
                )
            ],
          ),
        ));
  }

  void onSelected(dynamic value) {
    this.value = value;
  }

  void onConfirm() {
    Get.back(result: value);
  }
}

class _FilterListView extends StatelessWidget {
  const _FilterListView({
    required this.data,
    required this.onChanged,
  });

  final List<BottomSheetModel> data;
  final Function(dynamic) onChanged;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(0),
      physics: const ClampingScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (_, index) => _Item(
        item: data[index],
        onChanged: _onChanged,
      ),
      itemCount: data.length,
      separatorBuilder: (_, __) => const SizedBox(height: 8),
    );
  }

  void _onChanged(dynamic value) {
    for (var element in data) {
      element.isSelected.value = element.value == value;
    }
    onChanged(value);
  }
}

class _Item extends StatelessWidget {
  const _Item({
    required this.item,
    required this.onChanged,
  });

  final BottomSheetModel item;
  final Function(dynamic) onChanged;

  @override
  Widget build(BuildContext context) {
    return Obx(() => SystemLogEvent(
          eventName: item.eventName ?? '',
          child: InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: _onItemTapped,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: item.bgColor ??
                    (item.isSelected.value
                        ? AppColors.baseBlue1
                        : Colors.transparent),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: item.bgColor == null
                        ? item.isSelected.value
                            ? AppColors.baseBlue3
                            : AppColors.borderLine
                        : Colors.transparent),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Row(
                  children: [
                    AnimatedRadioButton(
                      isSelected: item.isSelected.value,
                      onChanged: (value) {
                        _onItemTapped();
                      },
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.title,
                            style: item.titleStyle ??
                                AppTextStyles.smallRegular
                                    .copyWith(color: AppColors.selectionTitle),
                          ),
                          if (item.description != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Text(
                                item.description ?? '',
                                style: AppTextStyles.smallRegular
                                    .copyWith(color: AppColors.textHint),
                              ),
                            )
                        ],
                      ),
                    ),
                    if (item.iconAsset != null)
                      SizedBox(
                        width: 28,
                        height: 28,
                        child: Image.asset(item.iconAsset!),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ));
  }

  void _onItemTapped() {
    onChanged(item.value);
  }
}

class BottomSheetModel {
  BottomSheetModel({
    this.value,
    required this.title,
    this.iconAsset,
    bool? isSelected,
    this.bgColor,
    this.description,
    this.titleStyle,
    this.eventName,
  }) {
    if (isSelected != null) {
      this.isSelected.value = isSelected;
    }
  }
  final dynamic value;
  final String title;
  final String? description;
  final String? iconAsset;
  final Color? bgColor;
  final TextStyle? titleStyle;
  final String? eventName;

  final RxBool isSelected = false.obs;
}
