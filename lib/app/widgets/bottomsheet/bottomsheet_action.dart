import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/log/basic_log_event.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class BottomSheetAction extends StatelessWidget {
  const BottomSheetAction({
    super.key,
    required this.title,
    required this.child,
    this.onDone,
    this.eventLocationName = '',
    this.isEnableAction = true,
  });

  final String title;
  final Widget child;
  final void Function()? onDone;
  final bool isEnableAction;
  final String eventLocationName;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 36),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.baseBold,
                    )
                  ],
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                child: GestureDetector(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    child: Assets.images.close.svg(width: 28),
                  ),
                  onTap: () {
                    Get.back();
                  },
                ),
              )
            ],
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: AppColors.divider,
          ),
          child,
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: Get.mediaQuery.size.width,
              child: BasicLogEvent(
                eventName: EventLogConstants.clickBtnConfirm,
                params: BasicLogEventParams(
                    eventName: EventLogConstants.clickBtnConfirm,
                    screenLocation: EventLogConstants.learningScreen,
                    locationType: LocationType.bottomsheet,
                    locationName: eventLocationName),
                child: PrimaryButton(
                    text: LocaleKeys.confirm.tr,
                    isEnable: isEnableAction,
                    onTap: () {
                      if (isEnableAction == false) return;
                      Get.back();
                      onDone?.call();
                    }),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
