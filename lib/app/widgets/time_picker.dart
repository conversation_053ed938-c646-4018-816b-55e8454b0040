import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/widgets/time_picker_spinner.dart';

class TimePicker extends StatelessWidget {
  const TimePicker({super.key, this.onDateChanged, this.initDate});

  final void Function(DateTime)? onDateChanged;

  final DateTime? initDate;

  @override
  Widget build(BuildContext context) {
    return TimePickerSpinner(
      time: initDate,
      is24HourMode: false,
      normalTextStyle:
          AppTextStyles.xLargeMedium.copyWith(color: AppColors.textDisabled),
      highlightedTextStyle: AppTextStyles.xxLargeMedium,
      spacing: 50,
      itemHeight: 54,
      isForce2Digits: true,
      onTimeChange: (time) {
        onDateChanged?.call(time);
      },
    );
    // return ScrollDateTimePicker(
    //     itemExtent: 54,
    //     infiniteScroll: true,
    //     visibleItem: 5,
    //     style: DateTimePickerStyle(
    //         activeStyle: AppTextStyles.xxLargeMedium,
    //         inactiveStyle: AppTextStyles.xLargeMedium
    //             .copyWith(color: AppColors.textDisabled),
    //         disabledStyle: AppTextStyles.xLargeMedium
    //             .copyWith(color: AppColors.textDisabled)),
    //     dateOption: DateTimePickerOption(
    //       dateFormat: DateFormat('hh:mm a'),
    //       minDate: DateTime.now().subtract(1.days),
    //       maxDate: DateTime.now().add(1.days),
    //       initialDate: initDate,
    //     ),
    //     onChange: (datetime) {
    //       onDateChanged?.call(datetime);
    //     });
  }
}
