import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:scroll_datetime_picker/scroll_datetime_picker.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';

class DatePicker extends StatelessWidget {
  const DatePicker({super.key, this.onDateChanged, this.initDate});

  final void Function(DateTime)? onDateChanged;

  final DateTime? initDate;

  @override
  Widget build(BuildContext context) {
    return ScrollDateTimePicker(
        itemExtent: 54,
        infiniteScroll: false,
        visibleItem: 5,
        style: DateTimePickerStyle(
            activeStyle: AppTextStyles.xxLargeMedium,
            inactiveStyle: AppTextStyles.xLargeMedium
                .copyWith(color: AppColors.textDisabled),
            disabledStyle: AppTextStyles.xLargeMedium
                .copyWith(color: AppColors.textDisabled)),
        dateOption: DateTimePickerOption(
          dateFormat:
              DateFormat('ddMMMMyyyy', Global.locale.locale.languageCode),
          minDate: DateTime.now(),
          maxDate: DateTime.now().add(const Duration(days: 3650)),
          initialDate: initDate,
        ),
        onChange: (datetime) {
          onDateChanged?.call(datetime);
        });
  }
}
