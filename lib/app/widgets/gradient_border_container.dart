import 'package:flutter/material.dart';

class GradientBorderContainer extends StatelessWidget {
  final Widget child;
  final Gradient? gradient;
  final double thickness;
  final double radius;
  final Color? backgroundColor;
  final Gradient? bgGradient;
  final List<BoxShadow>? boxShadow;

  const GradientBorderContainer({
    Key? key,
    required this.child,
    this.gradient,
    this.bgGradient,
    this.thickness = 2,
    this.backgroundColor,
    this.radius = 8,
    this.boxShadow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
          gradient: gradient, borderRadius: BorderRadius.circular(radius)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(radius),
          gradient: bgGradient,
          color: backgroundColor,
          boxShadow: boxShadow,
        ),
        margin: EdgeInsets.all(thickness),
        child: child,
      ),
    );
  }
}
