import 'package:flutter/material.dart';

class DashedBorder extends Border {
  final double dashWidth;
  final double dashSpace;

  const DashedBorder({
    super.top,
    super.right,
    super.bottom,
    super.left,
    this.dashWidth = 5.0,
    this.dashSpace = 5.0,
  });

  void _paintDashedSide(Canvas canvas, Rect rect, BorderSide side,
      {required bool horizontal}) {
    final paint = side.toPaint()..style = PaintingStyle.stroke;
    final double totalWidth = horizontal ? rect.width : rect.height;
    final path = Path();
    double startX = horizontal ? rect.left : rect.top;
    final double endX = horizontal ? rect.right : rect.bottom;
    final double posY = horizontal ? rect.top : rect.left;

    while (startX < endX) {
      path.moveTo(horizontal ? startX : posY, horizontal ? posY : startX);
      path.lineTo(horizontal ? startX + dashWidth : posY,
          horizontal ? posY : startX + dashWidth);
      startX += dashWidth + dashSpace;
    }

    canvas.drawPath(path, paint);
  }

  @override
  void paint(Canvas canvas, Rect rect,
      {TextDirection? textDirection,
      BoxShape shape = BoxShape.rectangle,
      BorderRadius? borderRadius}) {
    if (top != BorderSide.none) {
      _paintDashedSide(canvas,
          Rect.fromLTWH(rect.left, rect.top, rect.width, top.width), top,
          horizontal: true);
    }
    if (right != BorderSide.none) {
      _paintDashedSide(
          canvas,
          Rect.fromLTWH(
              rect.right - right.width, rect.top, right.width, rect.height),
          right,
          horizontal: false);
    }
    if (bottom != BorderSide.none) {
      _paintDashedSide(
          canvas,
          Rect.fromLTWH(
              rect.left, rect.bottom - bottom.width, rect.width, bottom.width),
          bottom,
          horizontal: true);
    }
    if (left != BorderSide.none) {
      _paintDashedSide(canvas,
          Rect.fromLTWH(rect.left, rect.top, left.width, rect.height), left,
          horizontal: false);
    }
  }
}
