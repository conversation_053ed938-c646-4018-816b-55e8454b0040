import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';

class LabeledCheckbox extends StatelessWidget {
  const LabeledCheckbox({
    required this.label,
    this.contentPadding,
    required this.value,
    required this.onTap,
    this.activeColor,
    this.gap = 8.0,
    this.textStyle,
  });

  final String label;
  final EdgeInsets? contentPadding;
  final bool value;
  final Function onTap;
  final Color? activeColor;
  final double gap;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(!value),
      child: Padding(
        padding: contentPadding ?? const EdgeInsets.all(0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            SizedBox(
              width: 20,
              height: 20,
              child: Checkbox(
                value: value,
                activeColor: activeColor ?? AppColors.icBtn2ndDefault,
                visualDensity: VisualDensity.compact,
                onChanged: (val) => onTap(val),
                side: BorderSide(
                  color: AppColors.icHint,
                  width: 1,
                ),
              ),
            ),
            SizedBox(
              width: gap,
            ),
            Flexible(
              child: Text(label,
                  style: textStyle ??
                      AppTextStyles.smallMedium
                          .copyWith(color: AppColors.text1st)),
            ),
          ],
        ),
      ),
    );
  }
}
