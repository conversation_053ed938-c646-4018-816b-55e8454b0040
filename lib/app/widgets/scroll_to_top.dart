import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';

class ScrollToTop extends StatelessWidget {
  const ScrollToTop({super.key, required this.onTap});

  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      onTap: onTap,
      child: SizedBox(
        width: 48,
        height: 48,
        child: DecoratedBox(
          decoration: BoxDecoration(
              color: AppColors.icDefault,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 2))
              ]),
          child: Center(child: Assets.images.up.svg()),
        ),
      ),
    );
  }
}