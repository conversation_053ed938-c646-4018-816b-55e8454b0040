import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';

class CheckboxWidget extends StatelessWidget {
  const CheckboxWidget(
      {super.key, required this.value, required this.onChanged, this.activeColor, this.borderColor});

  final bool value;
  final Function(bool?) onChanged;
  final Color? activeColor;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 20,
      height: 20,
      child: Checkbox(
        onChanged: (value) {
          onChanged.call(value);
        },
        value: value,
        activeColor: activeColor ?? AppColors.primary,
        side: BorderSide(color: borderColor ?? AppColors.icHint, width: 1),
        splashRadius: 0,
      ),
    );
  }
}
