import 'package:flutter/cupertino.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';

class SwitchWidget extends StatelessWidget {
  const SwitchWidget({super.key, required this.value, required this.onChanged});

  final bool value;
  final Function(bool) onChanged;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: 48,
        height: 32,
        child: FittedBox(
            fit: BoxFit.contain,
            child: CupertinoSwitch(
              value: value,
              onChanged: (value) {
                onChanged.call(value);
              },
              activeColor: AppColors.primary,
              trackColor: AppColors.bgMain02,
            )));
  }
}
