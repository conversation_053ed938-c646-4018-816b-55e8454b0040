import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';

class LabeledSelectionWidget extends StatelessWidget {
  const LabeledSelectionWidget({
    required this.label,
    this.contentPadding,
    required this.value,
    required this.onTap,
    this.activeColor,
    this.textStyle,
  });

  final String label;
  final EdgeInsets? contentPadding;
  final bool value;
  final Function onTap;
  final Color? activeColor;

  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(!value),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100),
          border: Border.all(
            color: AppColors.borderLine,
          ),
          color: value ? AppColors.btn2ndDefault : Colors.transparent,
        ),
        padding: contentPadding ??
            const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Text(
          label,
          style: textStyle ??
              AppTextStyles.smallMedium.copyWith(
                color: value ? AppColors.textBtn2nd : AppColors.text1st,
              ),
        ),
      ),
    );
  }
}
