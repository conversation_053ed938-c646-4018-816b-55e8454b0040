import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';

class BasicLogEvent extends StatelessWidget {
  const BasicLogEvent(
      {super.key,
      required this.eventName,
      required this.child,
      required this.params});

  final String eventName;
  final Widget child;
  final BasicLogEventParams params;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) {
        Get.find<LogFirebaseEvent>()
            .logEvent(eventName, params: params.toJson());
      },
      child: child,
    );
  }
}
