import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/utils/log_event.dart';

class SystemLogEvent extends StatelessWidget {
  const SystemLogEvent(
      {super.key, required this.eventName, required this.child});

  final String eventName;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) {
        Get.find<LogFirebaseEvent>().logEvent(eventName);
      },
      child: child,
    );
  }
}
