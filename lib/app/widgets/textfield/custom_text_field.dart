import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode? focusNode;
  final bool readOnly;
  final Function? ontap;
  final Icon? suffixIcon;
  final int limit;
  final String hint;
  final bool autoFocus;
  final int? maxLine;
  const CustomTextField({
    Key? key,
    required this.controller,
    this.focusNode,
    this.readOnly = false,
    this.ontap,
    this.suffixIcon,
    this.limit = 30,
    this.hint = "",
    this.autoFocus = false,
    this.maxLine,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      autofocus: autoFocus,
      readOnly: readOnly,
      maxLines: maxLine,
      onTap: () {
        if (ontap != null) ontap!();
      },
      inputFormatters: [
        LengthLimitingTextInputFormatter(limit),
      ],
      style: AppTextStyles.baseRegular,
      cursorColor: AppColors.primary,
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: AppTextStyles.baseRegular.copyWith(
          color: AppColors.textDisabled,
        ),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.borderLine, width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.borderLine, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.icBtn2ndDefault, width: 1),
        ),
        contentPadding: EdgeInsets.all(16),
      ),
    );
  }
}
