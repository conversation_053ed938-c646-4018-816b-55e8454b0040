import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:idle_detector_wrapper/idle_detector_wrapper.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';

class IdleDetectorWidget extends StatelessWidget {
  const IdleDetectorWidget({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return IdleDetector(
      idleTime: const Duration(minutes: 30),
      onIdle: () async {
        await Get.find<LocalService>().generateSessionId();
      },
      child: child,
    );
  }
}
