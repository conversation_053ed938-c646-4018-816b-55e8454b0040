import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';

class PrimaryButton extends StatelessWidget {
  const PrimaryButton(
      {super.key,
      this.isEnable = true,
      this.onTap,
      required this.text,
      this.width});

  final bool isEnable;
  final void Function()? onTap;
  final String text;
  final double? width;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      child: ElevatedButton(
        onPressed: () {
          if (isEnable == false) return;
          onTap?.call();
        },
        child: Text(
          text,
          style: AppTextStyles.baseMedium.copyWith(
              color:
                  isEnable ? AppColors.textBtn1st : AppColors.textBtnDisabled),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isEnable ? AppColors.btn1stDefault : AppColors.btnDisabled,
          foregroundColor:
              isEnable ? AppColors.textBtn1st : AppColors.textBtnDisabled,
          elevation: 0,
          minimumSize: Size(20, 48),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }
}
