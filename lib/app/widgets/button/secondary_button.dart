import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';

class SecondaryButton extends StatelessWidget {
  const SecondaryButton(
      {super.key, this.isEnable = true, this.onTap, required this.text});

  final bool isEnable;
  final void Function()? onTap;
  final String text;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          if (isEnable == false) return;
          onTap?.call();
        },
        child: Text(
          text,
          style: AppTextStyles.baseMedium.copyWith(color: AppColors.textBtn2nd),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isEnable ? AppColors.btn2ndDefault : AppColors.btnDisabled,
          foregroundColor:
              isEnable ? AppColors.textBtn2nd : AppColors.textBtnDisabled,
          elevation: 0,
          minimumSize: Size(20, 48),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }
}
