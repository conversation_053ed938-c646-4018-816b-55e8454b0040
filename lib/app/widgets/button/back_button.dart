import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';

class BackButtonWidget extends StatelessWidget {
  const BackButtonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: GestureDetector(
        onTap: () {
          Get.back();
        },
        child: Transform.rotate(
            angle: 180 * pi / 180, child: Assets.images.altArrowRight.svg()),
      ),
    );
  }
}
