import 'package:flutter/material.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';

class RoundButton extends StatelessWidget {
  final String title;
  final Color borderColor;
  final Color textColor;
  final Color? bgColor;
  final double borderWidth;
  final Gradient? gradient;
  final Function? onTap;
  final double radius;
  const RoundButton({
    Key? key,
    required this.title,
    this.borderColor = Colors.transparent,
    required this.textColor,
    this.bgColor,
    this.onTap,
    this.gradient,
    this.borderWidth = 0,
    this.radius = 40,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (onTap != null) onTap!();
      },
      child: Container(
        width: MediaQuery.sizeOf(context).width,
        height: 48,
        decoration: BoxDecoration(
          color: bgColor,
          gradient: gradient,
          borderRadius: BorderRadius.circular(radius),
          border: Border.all(
            color: borderColor,
            width: borderWidth,
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: AppTextStyles.baseMedium.copyWith(color: textColor),
          ),
        ),
      ),
    );
  }
}
