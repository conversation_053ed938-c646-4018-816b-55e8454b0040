import 'package:dio/dio.dart';

class ResponseError {
  int? code;
  String? message;
  String? messageMobile;
  String? titleMobile;
  Map<String, dynamic>? data;
  int? httpStatusCode;
  String? errorCode;

  ResponseError(
      {this.code,
      this.message,
      this.data,
      this.httpStatusCode,
      this.errorCode});

  ResponseError.fromJson(Map<String, dynamic> json) {
    code ??= int.tryParse(json['code']);
    message ??= json['message'];
    code ??= json['err_code'];
    message ??= json['err_detail'];
    messageMobile ??= json['message_mobile'];
    titleMobile ??= json['title_mobile'];
    errorCode ??= json['error_code'];
  }

  factory ResponseError.fromDioResponse(Response response) {
    ResponseError responseError = ResponseError();
    if (response.data is Map<String, dynamic>) {
      responseError = ResponseError.fromJson(response.data);
    } else if (response.data is String) {
      responseError.message = response.data;
    }
    responseError.httpStatusCode = response.statusCode;
    return responseError;
  }
}
