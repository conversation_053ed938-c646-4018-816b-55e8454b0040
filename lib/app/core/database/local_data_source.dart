import 'dart:developer';

import 'package:get/get.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';

// abstract class LocalDataSource {
//   String get storageKey;

//   final _box = GetStorage();

//   String get identifyKey {
//     return "$storageKey";
//   }

//   Future<T?> getData<T>(String key) async {
//     final mapData = _box.read("$identifyKey-$key");
//     if (mapData == null) {
//       return null;
//     }
//     final data = await parseJson(mapData);
//     return data;
//   }

//   Future<void> saveToStorage(dynamic data, String key) async {
//     _box.write("$identifyKey-$key", data);
//   }

//   Future<dynamic> parseJson(Map<String, dynamic> data);
// }

class IsarDb extends GetxService {
  Future<IsarDb> init() async {
    log("IsarDb init");
    return this;
  }

  Future<Isar> get isar async {
    final dir = await getApplicationDocumentsDirectory();
    final isar = await Isar.open(
      [
        QuizEntitySchema,
        AppDataSchema,
        QuestionEntitySchema,
        ResultEntitySchema,
      ],
      directory: dir.path,
    );
    return isar;
  }
}
