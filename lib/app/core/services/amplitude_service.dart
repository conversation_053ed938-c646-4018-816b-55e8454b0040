import 'package:amplitude_flutter/amplitude.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';

class AmplitudeService extends GetxService {
  Future<Amplitude> get amplitude async {
    // Create the instance
    final Amplitude analytics = Amplitude.getInstance(instanceName: "project");
    // Initialize SDK
    analytics.init(Constants.amplitudeApiKey);
    analytics.trackingSessionEvents(true);
    return analytics;
  }
}
