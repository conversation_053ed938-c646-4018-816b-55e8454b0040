import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

extension IntExt on int {
  String get toTimeString {
    int h, m, s;

    h = this ~/ 3600;

    m = ((this - h * 3600)) ~/ 60;

    s = this - (h * 3600) - (m * 60);

    String hour = h.toString().length < 2 ? "0" + h.toString() : h.toString();

    String minute = m.toString().length < 2 ? "0" + m.toString() : m.toString();

    String seconds =
        s.toString().length < 2 ? "0" + s.toString() : s.toString();

    String result = "$hour:$minute:$seconds";

    return result;
  }

  String get toTimeTextString {
    int h, m, s;

    h = this ~/ 3600;

    m = ((this - h * 3600)) ~/ 60;

    s = this - (h * 3600) - (m * 60);

    String hour = h == 0 ? "" : "$h${LocaleKeys.hour.tr} ";

    String minute = m == 0 ? "" : "$m${LocaleKeys.mins.tr} ";

    String seconds = "$s${LocaleKeys.seconds.tr.replaceAll('seconds', 's')}";

    String result = "$hour$minute$seconds";

    return result;
  }

  String get hourMinuteString {
    int h, m, s;

    h = this ~/ 3600;

    m = ((this - h * 3600)) ~/ 60;

    s = this - (h * 3600) - (m * 60);

    String hour = "${h}h ";

    String minute = "${m}m";

    String seconds = "";

    String result = "$hour$minute$seconds";

    return result;
  }

  String get shortTextString {
    int h, m, s;

    h = this ~/ 3600;

    m = ((this - h * 3600)) ~/ 60;

    s = this - (h * 3600) - (m * 60);

    String hour = h == 0 ? "" : "${h}h ";

    String minute = m == 0 ? "" : "${m}m ";

    String seconds = "${s}s";

    String result = "$hour$minute$seconds";

    return result;
  }
}
