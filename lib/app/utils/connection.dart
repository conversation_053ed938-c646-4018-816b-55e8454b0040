import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:scrumpass_exam_simulator/app/utils/log.dart';

abstract class Connection {
  Future<bool> isInternetConnected();
  void registerFuncNeedReload({required String key, required Function func});
  void removeFuncNeedReload({required String key});
}

class ConnectionConcrete extends Connection {
  static final ConnectionConcrete _instance = ConnectionConcrete._();

  ConnectionConcrete._() {
    _initialize();
  }

  factory ConnectionConcrete() => _instance;

  final Connectivity _connectivity = Connectivity();

  @override
  Future<bool> isInternetConnected() async {
    bool isConnected = false;

    List<ConnectivityResult> result = [];

    try {
      result = await _connectivity.checkConnectivity();
    } catch (e) {
      logDebug(e.toString());
    }

    if (result != ConnectivityResult.none) {
      isConnected = true;
    }

    return isConnected;
  }

  List<ConnectivityResult>? lastState;
  Future<void> _initialize() async {
    lastState = await _connectivity.checkConnectivity();
    _connectivity.onConnectivityChanged.listen((result) {
      if (lastState != null) {
        if (lastState!.hasNetwork == false && result.hasNetwork) {
          funcNeedReloads.forEach((key, func) {
            func.call();
          });
        }
      }
      lastState = result;
    });
  }

  Map<String, Function> funcNeedReloads = {};

  @override
  void registerFuncNeedReload({required String key, required Function func}) {
    funcNeedReloads[key] = func;
  }

  @override
  void removeFuncNeedReload({required String key}) {
    funcNeedReloads.remove(key);
  }
}

extension ConnectivityResultExtension on List<ConnectivityResult> {
  bool get hasNetwork {
    bool result = false;
    if (this.contains(ConnectivityResult.mobile) ||
        this.contains(ConnectivityResult.wifi) ||
        this.contains(ConnectivityResult.vpn)) {
      result = true;
    }
    if (this.contains(ConnectivityResult.none)) result = false;
    return result;
  }
}
