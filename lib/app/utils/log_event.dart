import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/utils/log.dart';
import 'package:scrumpass_exam_simulator/data/data_source/local/local_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';

part 'log_event.freezed.dart';
part 'log_event.g.dart';

class LogFirebaseEvent extends GetxService with _DbHandlerMixin {
  Future<void> logEvent(String name, {Map<String, dynamic>? params}) async {
    final appData = _getAppData();
    final userId = appData?.debugId ?? '';
    final device = Get.mediaQuery.size.width > 550 ? 'Tablet' : 'Mobile';
    final defaultLog = {
      "timestamp": Jiffy.now().format(pattern: 'dd/MM/yyyy HH:mm:ss'),
      "session_id": appData?.sessionId ?? '',
      "user_id": userId,
      "device": device,
      "operation_system": Platform.isAndroid ? "Android" : "iOS",
      "version": Global.appVersion,
      "environment": environment.name,
      "app_name": Constants.appName
    };
    await FirebaseAnalytics.instance.setDefaultEventParameters(defaultLog);
    final json = params?..removeWhere((key, value) => value == null);
    final paramsJson = Map<String, Object>.from(json ?? {});
    logDebug('Event log: $defaultLog \n Params: $paramsJson');
    await FirebaseAnalytics.instance.logEvent(
      name: name,
      parameters: paramsJson,
    );
    logDebug('Log event: $name');
  }
}

mixin _DbHandlerMixin {
  final localService = Get.find<LocalService>();
  AppData? _getAppData() {
    return localService.getAppData();
  }
}

class LogEventParams {}

@Freezed(
  fromJson: false,
  toJson: true,
  copyWith: true,
  equal: true,
)
class BasicLogEventParams extends LogEventParams
    with _$BasicLogEventParams {
  factory BasicLogEventParams({
    @JsonKey(name: 'event_name') String? eventName,
    @JsonKey(name: 'screen_location') String? screenLocation,
    @JsonKey(name: 'location_type') LocationType? locationType,
    @JsonKey(name: 'location_name') String? locationName,
  }) = _BasicLogEventParams;
}

@Freezed(
  fromJson: false,
  toJson: true,
  copyWith: true,
  equal: true,
)
class PracticeLogEventParams extends LogEventParams
    with _$PracticeLogEventParams {
  factory PracticeLogEventParams({
    @JsonKey(name: 'exam_id') String? examId,
    @JsonKey(name: 'exam_type') String? examType,
    @JsonKey(name: 'exam_name') String? examName,
    @JsonKey(name: 'number_of_question') int? numberOfQuestion,
  }) = _PracticeLogEventParams;
}

@Freezed(
  fromJson: false,
  toJson: true,
  copyWith: true,
  equal: true,
)
class SocialMediaLogEventParams extends LogEventParams
    with _$SocialMediaLogEventParams {
  factory SocialMediaLogEventParams({
    @JsonKey(name: 'social_media_channel') SocialMediaChannel? mediaChannel,
  }) = _SocialMediaLogEventParams;
}

@Freezed(
  fromJson: false,
  toJson: true,
  copyWith: true,
  equal: true,
)
class LanguageLogEventParams extends LogEventParams
    with _$LanguageLogEventParams {
  factory LanguageLogEventParams({
    @JsonKey(name: 'language_name') String? languageName,
  }) = _LanguageLogEventParams;
}

@Freezed(
  fromJson: false,
  toJson: true,
  copyWith: true,
  equal: true,
)
class FontSizeLogEventParams extends LogEventParams
    with _$FontSizeLogEventParams {
  factory FontSizeLogEventParams({
    @JsonKey(name: 'font_size') int? fontSize,
  }) = _FontSizeLogEventParams;
}

enum OperationSystem { android, iOS }

enum LocationType { popup, bottomsheet, screen, sidepanel }
