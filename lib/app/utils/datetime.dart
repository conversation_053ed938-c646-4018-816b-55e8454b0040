import 'package:jiffy/jiffy.dart';

extension DateTimeExtension on DateTime {
  num get diffFromNow {
    final date = Jiffy.parseFromDateTime(this);
    final diff = date.diff(Jiffy.now(), unit: Unit.day);
    return diff;
  }

  String get dateString {
    final date = Jiffy.parseFromDateTime(this);
    return date.format(pattern: 'dd/MM/yyyy');
  }

  String get timeString {
    final date = Jiffy.parseFromDateTime(this);
    return date.format(pattern: 'hh:mm a');
  }

  String get dateTimeString {
    final date = Jiffy.parseFromDateTime(this);
    return date.format(pattern: 'dd/MM/yyyy HH:mm:ss');
  }

  DateTime toStartOfDay() {
    final date = Jiffy.parseFromDateTime(this);
    return DateTime(date.year, date.month, date.date, 0, 0, 0);
  }
}
