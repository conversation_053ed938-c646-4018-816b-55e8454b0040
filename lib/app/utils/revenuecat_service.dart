import 'dart:io';

import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';

class RevenuecatService {
  Future<void> initPlatformState() async {
    await Purchases.setLogLevel(LogLevel.verbose);

    PurchasesConfiguration configuration = PurchasesConfiguration('');
    if (Platform.isAndroid) {
      configuration = PurchasesConfiguration(Constants.revenueCatAndroid);
    } else if (Platform.isIOS) {
      configuration = PurchasesConfiguration(Constants.revenueCatIos);
    }
    await Purchases.configure(configuration);
  }

  Future<List<Package>> getAvailablePackages() async {
    Offerings offerings = await Purchases.getOfferings();
    return offerings.current?.availablePackages ?? [];
  }

  Future<bool> purchasePackage(Package package) async {
    CustomerInfo customerInfo = await Purchases.purchasePackage(package);
    updateSubscriptionType(package.identifier);
    return true;
    // if (customerInfo.entitlements.all["my_entitlement_identifier"].isActive) {
    //   // Unlock that great "pro" content
    // }
  }

  Future purchaserInfo() async {
    try {
      CustomerInfo purchaserInfo = await Purchases.getCustomerInfo();
      if (purchaserInfo.entitlements.all.isNotEmpty &&
          purchaserInfo.entitlements.all['Premium']!.isActive) {
        Global.isPremium = true;
        if (purchaserInfo.entitlements.all['Premium']!.periodType ==
            PeriodType.trial) {
          Global.isTrial = true;
        }

        final idProduct =
            purchaserInfo.entitlements.all['Premium']!.productIdentifier;
        updateSubscriptionType(idProduct);
        return true;
      } else {
        Global.isPremium = false;
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  void updateSubscriptionType(String idProduct) {
    if (idProduct.contains('monthly') ||
        idProduct.contains('1month') ||
        idProduct.contains('1_month')) {
      Global.updateSubscriptionType(SubscriptionType.monthly);
    } else if (idProduct.contains('3month') ||
        idProduct.contains('three_month') ||
        idProduct.contains('3_month') ||
        idProduct.contains('3months') ||
        idProduct.contains('3_months')) {
      Global.updateSubscriptionType(SubscriptionType.quarterly);
    } else if (idProduct.contains('6month') ||
        idProduct.contains('six_month') ||
        idProduct.contains('6_month') ||
        idProduct.contains('6months') ||
        idProduct.contains('6_months')) {
      Global.updateSubscriptionType(SubscriptionType.halfYearly);
    } else if (idProduct.contains('yearly') ||
        idProduct.contains('annual') ||
        idProduct.contains('12months') ||
        idProduct.contains('12_months')) {
      Global.updateSubscriptionType(SubscriptionType.yearly);
    }
  }

  Future<bool> restorePurchase() async {
    try {
      CustomerInfo restoredInfo = await Purchases.restorePurchases();
      if (restoredInfo.entitlements.all.isNotEmpty &&
          restoredInfo.entitlements.all['Premium']!.isActive) {
        Global.isPremium = true;
        updateSubscriptionType(
            restoredInfo.entitlements.all['Premium']!.productIdentifier);
        return true;
      } else {
        Global.isPremium = false;
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
