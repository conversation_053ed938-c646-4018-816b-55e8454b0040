import 'dart:math';

import 'package:get/get.dart';
import 'package:html/parser.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

class Utils {
  static double getBottomSheetPadding() {
    // check if the user's device has a navigator button bar or not
    return Get.window.viewPadding.bottom > 0 ? 0 : 8;
  }

  static String generateRandomString(int len) {
    var r = Random();
    const _chars =
        'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
    return List.generate(len, (index) => _chars[r.nextInt(_chars.length)])
        .join();
  }

  static String parseHtmlString(String htmlString) {
    final document = parse(htmlString);
    final String parsedString =
        parse(document.body!.text).documentElement!.text;

    return parsedString;
  }

  static String parseTestPracticeName(String key) {
    switch (key) {
      case LocaleKeys.quickTest:
        return LocaleKeys.flashChallenge.tr;
      case LocaleKeys.missedQuestionTest:
        return LocaleKeys.masterYourMistakes.tr;
      case LocaleKeys.bookmarkQuestionTest:
        return LocaleKeys.personalReview.tr;
      case LocaleKeys.domainKnowledgeTest:
        return LocaleKeys.topicMastery.tr;
      case LocaleKeys.fullTest:
        return LocaleKeys.fullTest.tr;
      case LocaleKeys.miniTest:
        return LocaleKeys.miniTest.tr;
      default:
        return key;
    }
  }

  /// FNV-1a 64bit hash algorithm optimized for Dart Strings
  static int fastHash(String string) {
    var hash = 0xcbf29ce484222325;

    var i = 0;
    while (i < string.length) {
      final codeUnit = string.codeUnitAt(i++);
      hash ^= codeUnit >> 8;
      hash *= 0x100000001b3;
      hash ^= codeUnit & 0xFF;
      hash *= 0x100000001b3;
    }

    return hash;
  }

  static int generateDbId([String? string]) {
    return fastHash(string ?? generateRandomString(10));
  }

  /// Check if the input date is exactly one day behind the current date
  static bool isNextDay(DateTime inputDate) {
    // Get the current date and time
    DateTime now = DateTime.now();

    // Check if the input date is exactly one day behind the current date
    DateTime nextDay = inputDate.add(Duration(days: 1));

    // Compare only the date part, ignoring the time
    return nextDay.year == now.year &&
        nextDay.month == now.month &&
        nextDay.day == now.day;
  }
}
