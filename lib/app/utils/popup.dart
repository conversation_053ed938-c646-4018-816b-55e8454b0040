import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/color.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/primary_button.dart';
import 'package:scrumpass_exam_simulator/app/widgets/button/secondary_button.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';

enum SnackbarType { normal, error, success }

extension SnackbarTypeExt on SnackbarType {
  Color get bgColor {
    switch (this) {
      case SnackbarType.normal:
        return AppColors.bgInfoSurface;
      case SnackbarType.error:
        return AppColors.bgErrorSurface;
      case SnackbarType.success:
        return AppColors.bgSuccessSurface;
    }
  }

  Color get textColor {
    switch (this) {
      case SnackbarType.normal:
        return AppColors.textInfo;
      case SnackbarType.success:
        return AppColors.textSuccess;
      case SnackbarType.error:
        return AppColors.textError;
    }
  }
}

class Popup {
  double? globalTopPadding;
  Popup._();

  static final instance = Popup._();
  SnackbarController? snackbarController;

  Future<void> closeSnackBar({bool withAnimations = true}) async {
    if (Get.isSnackbarOpen) {
      await Popup.instance.snackbarController
          ?.close(withAnimations: withAnimations);
      if (!withAnimations) {
        await Future.delayed(const Duration(milliseconds: 50));
      }
    }
  }

  void showSnackBar({
    required String message,
    String? messageBold1,
    String? message2,
    String? messageBold2,
    String? message3,
    String? messageBold3,
    SnackbarType type = SnackbarType.normal,
    Duration duration = const Duration(seconds: 3),
    Duration animationDuration = const Duration(seconds: 1),
    SnackPosition? snackPosition = SnackPosition.TOP,
    Color? backgroundColor,
    String? undoTitle,
    VoidCallback? undoCallback,
    double? customPaddingTop,
  }) {
    double topPadding = customPaddingTop ?? globalTopPadding ?? 16;

    snackbarController = Get.snackbar(
      '',
      '',
      titleText: Container(color: backgroundColor ?? type.bgColor),
      // padding: EdgeInsets.only(
      //     left: 13, right: undoTitle != null ? 0 : 12, top: 6, bottom: 12),
      // margin: EdgeInsets.only(left: 16, bottom: 16, top: topPadding, right: 16),
      padding: EdgeInsets.symmetric(vertical: 14, horizontal: 12),
      margin: EdgeInsets.zero,
      backgroundColor: backgroundColor ?? type.bgColor,
      snackPosition: snackPosition,
      borderRadius: 0,
      duration: duration,
      animationDuration: animationDuration,
      snackStyle: SnackStyle.GROUNDED,
      // boxShadows: [
      //   BoxShadow(
      //     color: Colors.black.withOpacity(0.2),
      //     blurRadius: 4,
      //     offset: Offset(0, 4),
      //   )
      // ],
      messageText: Row(
        children: [
          if (type == SnackbarType.normal)
            Padding(
              padding: const EdgeInsets.only(right: 12),
              child: Assets.images.icToast.svg(),
            ),
          if (type == SnackbarType.success)
            Padding(
              padding: const EdgeInsets.only(right: 12),
              child: Assets.images.radioboxGreen.svg(width: 16, height: 16),
            ),
          if (type == SnackbarType.error)
            Padding(
              padding: const EdgeInsets.only(right: 12),
              child: Assets.images.radioboxRed.svg(width: 16, height: 16),
            ),
          Expanded(
            child: Text(
              message,
              style: AppTextStyles.smallMedium.copyWith(color: type.textColor),
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: dismissSnackBar,
            child: Assets.images.close.svg(width: 24),
          )
        ],
      ),
    );
  }

  TextStyle _snackBarTextStyleNormal(Color color) {
    return AppTextStyles.smallSemiBold.copyWith(color: color);
  }

  TextStyle _snackBarTextStyleBold(Color color) {
    return _snackBarTextStyleNormal(color).copyWith(
      fontWeight: FontWeight.w700,
    );
  }

  void dismissSnackBar() {
    if (Get.isSnackbarOpen) {
      Get.back();
    }
  }

  Future showBottomSheet(
    Widget widget, {
    bool isScrollControlled = true,
    bool isDismissible = true,
  }) async {
    return await Get.bottomSheet(
        // wrap để auto height
        Wrap(
          children: <Widget>[
            widget.paddingOnly(bottom: Utils.getBottomSheetPadding())
          ],
        ),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        isScrollControlled: isScrollControlled,
        backgroundColor: Colors.white,
        isDismissible: isDismissible,
        enableDrag: isDismissible);
  }

  Future showAlert(
      {required String title,
      required String message,
      Widget? doneBtn,
      bool isDismissible = true}) async {
    return await showBottomSheet(
        _AlertView(title: title, message: message, doneBtn: doneBtn),
        isDismissible: isDismissible);
  }

  Future showAlertPopup({required String title}) async {
    return await Get.dialog(Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 8),
            Assets.images.alertPopupIcon.image(width: 134),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: Text(title,
                  textAlign: TextAlign.center,
                  style: AppTextStyles.smallMedium.copyWith(
                    color: AppColors.popupTitle,
                  )),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: PrimaryButton(text: 'OK', onTap: () => Get.back()),
            ),
          ],
        ),
      ),
    ));
  }

  Future showPaymentSuccessPopup(
      {required String title,
      String? buttonText,
      required String description}) async {
    return await Get.dialog(Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: DecoratedBox(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
                colors: AppColors.successPopupBgGradient,
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 8),
              Assets.images.popupSuccess.image(width: 295, height: 120),
              const SizedBox(height: 8),
              if (title.isNotEmpty) ...{
                SizedBox(
                  width: double.infinity,
                  child: Text(title,
                      textAlign: TextAlign.center,
                      style: AppTextStyles.xLargeBold.copyWith(
                        color: AppColors.elementTextNormal,
                      )),
                ),
                const SizedBox(height: 8),
              },
              if (description.isNotEmpty) ...{
                SizedBox(
                  width: double.infinity,
                  child: Text(description,
                      textAlign: TextAlign.center,
                      style: AppTextStyles.smallMedium.copyWith(
                        color: AppColors.popupTitle,
                      )),
                ),
                const SizedBox(height: 24),
              },
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: PrimaryButton(
                    text: buttonText ?? 'OK', onTap: () => Get.back()),
              ),
            ],
          ),
        ),
      ),
    ));
  }

  Future showErrorPopup({
    required String title,
    required String description,
    String? primaryBtnText,
    String? secondaryBtnText,
    required Function onPrimaryTap,
    required Function onSecondaryTap,
  }) async {
    return await Get.dialog(Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: DecoratedBox(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                gradient: LinearGradient(
                    colors: AppColors.errorPopupBgGradient,
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 8),
                  Assets.images.popupError.image(width: 295, height: 120),
                  const SizedBox(height: 8),
                  if (title.isNotEmpty) ...{
                    SizedBox(
                      width: double.infinity,
                      child: Text(title,
                          textAlign: TextAlign.center,
                          style: AppTextStyles.xLargeBold.copyWith(
                            color: AppColors.elementTextNormal,
                          )),
                    ),
                    const SizedBox(height: 8),
                  },
                  if (description.isNotEmpty) ...{
                    SizedBox(
                      width: double.infinity,
                      child: Text(description,
                          textAlign: TextAlign.center,
                          style: AppTextStyles.smallMedium.copyWith(
                            color: AppColors.popupTitle,
                          )),
                    ),
                    const SizedBox(height: 16),
                  },
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        Flexible(
                          flex: 1,
                          child: SecondaryButton(
                              text: secondaryBtnText ?? 'Cancel',
                              onTap: () {
                                onSecondaryTap();
                              }),
                        ),
                        const SizedBox(width: 16),
                        Flexible(
                            flex: 1,
                            child: PrimaryButton(
                                text: primaryBtnText ?? 'OK',
                                onTap: () {
                                  onPrimaryTap();
                                })),
                      ],
                    ),
                  ),
                ],
              ),
            ))));
  }

  Future showSuccessPopup(
      {required String title, required String description}) async {
    return await Get.dialog(Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 8),
            Assets.images.successPopupIcon.image(width: 134),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: Text(title,
                  textAlign: TextAlign.center,
                  style: AppTextStyles.baseBold.copyWith(
                    color: AppColors.popupTitle,
                  )),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: Text(description,
                  textAlign: TextAlign.center,
                  style: AppTextStyles.baseMedium.copyWith(
                    color: AppColors.popupTitle,
                  )),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: PrimaryButton(text: 'OK', onTap: () => Get.back()),
            ),
          ],
        ),
      ),
    ));
  }

  Future showActionPopup(
      {required String title,
      required String description,
      RichText? customRichText,
      String? primaryBtnText,
      String? secondaryBtnText,
      required Function onPrimaryTap,
      required Function onSecondaryTap,
      bool showClose = false}) async {
    return await Get.dialog(Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Stack(
        alignment: Alignment.center,
        clipBehavior: Clip.none,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 8),
                Assets.images.infoPopupIcon.image(width: 134),
                const SizedBox(height: 8),
                if (customRichText != null) ...{
                  SizedBox(
                    width: double.infinity,
                    child: Center(child: customRichText),
                  ),
                  const SizedBox(height: 8),
                },
                if (title.isNotEmpty) ...{
                  SizedBox(
                    width: double.infinity,
                    child: Text(title,
                        textAlign: TextAlign.center,
                        style: AppTextStyles.smallBold.copyWith(
                          color: AppColors.popupTitle,
                        )),
                  ),
                  const SizedBox(height: 8),
                },
                SizedBox(
                  width: double.infinity,
                  child: Text(description,
                      textAlign: TextAlign.center,
                      style: AppTextStyles.smallMedium.copyWith(
                        color: AppColors.popupTitle,
                      )),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    children: [
                      Flexible(
                        flex: 1,
                        child: SecondaryButton(
                            text: secondaryBtnText ?? 'Cancel',
                            onTap: () {
                              Get.back();
                              onSecondaryTap();
                            }),
                      ),
                      const SizedBox(width: 16),
                      Flexible(
                          flex: 1,
                          child: PrimaryButton(
                              text: primaryBtnText ?? 'OK',
                              onTap: () {
                                Get.back();
                                onPrimaryTap();
                              })),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (showClose)
            Positioned(
              bottom: -52,
              child: SizedBox(
                  width: 40,
                  height: 40,
                  child: DecoratedBox(
                      decoration: BoxDecoration(
                          color: HexColor("888888"), shape: BoxShape.circle),
                      child: Center(
                        child: Assets.images.close
                            .svg(color: Colors.white, width: 26, height: 26),
                      ))),
            )
        ],
      ),
    ));
  }
}

class _AlertView extends StatelessWidget {
  const _AlertView({
    required this.title,
    required this.message,
    this.doneBtn,
    Key? key,
  }) : super(key: key);

  final String title;
  final String message;
  final Widget? doneBtn;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: true,
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        const SizedBox(height: 24),
      ]).paddingSymmetric(horizontal: 24),
    );
  }
}
