// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BasicLogEventParams {
  @JsonKey(name: 'event_name')
  String? get eventName => throw _privateConstructorUsedError;
  @JsonKey(name: 'screen_location')
  String? get screenLocation => throw _privateConstructorUsedError;
  @JsonKey(name: 'location_type')
  LocationType? get locationType => throw _privateConstructorUsedError;
  @JsonKey(name: 'location_name')
  String? get locationName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BasicLogEventParamsCopyWith<BasicLogEventParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BasicLogEventParamsCopyWith<$Res> {
  factory $BasicLogEventParamsCopyWith(
          BasicLogEventParams value, $Res Function(BasicLogEventParams) then) =
      _$BasicLogEventParamsCopyWithImpl<$Res, BasicLogEventParams>;
  @useResult
  $Res call(
      {@JsonKey(name: 'event_name') String? eventName,
      @JsonKey(name: 'screen_location') String? screenLocation,
      @JsonKey(name: 'location_type') LocationType? locationType,
      @JsonKey(name: 'location_name') String? locationName});
}

/// @nodoc
class _$BasicLogEventParamsCopyWithImpl<$Res, $Val extends BasicLogEventParams>
    implements $BasicLogEventParamsCopyWith<$Res> {
  _$BasicLogEventParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eventName = freezed,
    Object? screenLocation = freezed,
    Object? locationType = freezed,
    Object? locationName = freezed,
  }) {
    return _then(_value.copyWith(
      eventName: freezed == eventName
          ? _value.eventName
          : eventName // ignore: cast_nullable_to_non_nullable
              as String?,
      screenLocation: freezed == screenLocation
          ? _value.screenLocation
          : screenLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      locationType: freezed == locationType
          ? _value.locationType
          : locationType // ignore: cast_nullable_to_non_nullable
              as LocationType?,
      locationName: freezed == locationName
          ? _value.locationName
          : locationName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BasicLogEventParamsImplCopyWith<$Res>
    implements $BasicLogEventParamsCopyWith<$Res> {
  factory _$$BasicLogEventParamsImplCopyWith(_$BasicLogEventParamsImpl value,
          $Res Function(_$BasicLogEventParamsImpl) then) =
      __$$BasicLogEventParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'event_name') String? eventName,
      @JsonKey(name: 'screen_location') String? screenLocation,
      @JsonKey(name: 'location_type') LocationType? locationType,
      @JsonKey(name: 'location_name') String? locationName});
}

/// @nodoc
class __$$BasicLogEventParamsImplCopyWithImpl<$Res>
    extends _$BasicLogEventParamsCopyWithImpl<$Res, _$BasicLogEventParamsImpl>
    implements _$$BasicLogEventParamsImplCopyWith<$Res> {
  __$$BasicLogEventParamsImplCopyWithImpl(_$BasicLogEventParamsImpl _value,
      $Res Function(_$BasicLogEventParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eventName = freezed,
    Object? screenLocation = freezed,
    Object? locationType = freezed,
    Object? locationName = freezed,
  }) {
    return _then(_$BasicLogEventParamsImpl(
      eventName: freezed == eventName
          ? _value.eventName
          : eventName // ignore: cast_nullable_to_non_nullable
              as String?,
      screenLocation: freezed == screenLocation
          ? _value.screenLocation
          : screenLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      locationType: freezed == locationType
          ? _value.locationType
          : locationType // ignore: cast_nullable_to_non_nullable
              as LocationType?,
      locationName: freezed == locationName
          ? _value.locationName
          : locationName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$BasicLogEventParamsImpl implements _BasicLogEventParams {
  _$BasicLogEventParamsImpl(
      {@JsonKey(name: 'event_name') this.eventName,
      @JsonKey(name: 'screen_location') this.screenLocation,
      @JsonKey(name: 'location_type') this.locationType,
      @JsonKey(name: 'location_name') this.locationName});

  @override
  @JsonKey(name: 'event_name')
  final String? eventName;
  @override
  @JsonKey(name: 'screen_location')
  final String? screenLocation;
  @override
  @JsonKey(name: 'location_type')
  final LocationType? locationType;
  @override
  @JsonKey(name: 'location_name')
  final String? locationName;

  @override
  String toString() {
    return 'BasicLogEventParams(eventName: $eventName, screenLocation: $screenLocation, locationType: $locationType, locationName: $locationName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BasicLogEventParamsImpl &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.screenLocation, screenLocation) ||
                other.screenLocation == screenLocation) &&
            (identical(other.locationType, locationType) ||
                other.locationType == locationType) &&
            (identical(other.locationName, locationName) ||
                other.locationName == locationName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, eventName, screenLocation, locationType, locationName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BasicLogEventParamsImplCopyWith<_$BasicLogEventParamsImpl> get copyWith =>
      __$$BasicLogEventParamsImplCopyWithImpl<_$BasicLogEventParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BasicLogEventParamsImplToJson(
      this,
    );
  }
}

abstract class _BasicLogEventParams implements BasicLogEventParams {
  factory _BasicLogEventParams(
          {@JsonKey(name: 'event_name') final String? eventName,
          @JsonKey(name: 'screen_location') final String? screenLocation,
          @JsonKey(name: 'location_type') final LocationType? locationType,
          @JsonKey(name: 'location_name') final String? locationName}) =
      _$BasicLogEventParamsImpl;

  @override
  @JsonKey(name: 'event_name')
  String? get eventName;
  @override
  @JsonKey(name: 'screen_location')
  String? get screenLocation;
  @override
  @JsonKey(name: 'location_type')
  LocationType? get locationType;
  @override
  @JsonKey(name: 'location_name')
  String? get locationName;
  @override
  @JsonKey(ignore: true)
  _$$BasicLogEventParamsImplCopyWith<_$BasicLogEventParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PracticeLogEventParams {
  @JsonKey(name: 'exam_id')
  String? get examId => throw _privateConstructorUsedError;
  @JsonKey(name: 'exam_type')
  String? get examType => throw _privateConstructorUsedError;
  @JsonKey(name: 'exam_name')
  String? get examName => throw _privateConstructorUsedError;
  @JsonKey(name: 'number_of_question')
  int? get numberOfQuestion => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PracticeLogEventParamsCopyWith<PracticeLogEventParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PracticeLogEventParamsCopyWith<$Res> {
  factory $PracticeLogEventParamsCopyWith(PracticeLogEventParams value,
          $Res Function(PracticeLogEventParams) then) =
      _$PracticeLogEventParamsCopyWithImpl<$Res, PracticeLogEventParams>;
  @useResult
  $Res call(
      {@JsonKey(name: 'exam_id') String? examId,
      @JsonKey(name: 'exam_type') String? examType,
      @JsonKey(name: 'exam_name') String? examName,
      @JsonKey(name: 'number_of_question') int? numberOfQuestion});
}

/// @nodoc
class _$PracticeLogEventParamsCopyWithImpl<$Res,
        $Val extends PracticeLogEventParams>
    implements $PracticeLogEventParamsCopyWith<$Res> {
  _$PracticeLogEventParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? examId = freezed,
    Object? examType = freezed,
    Object? examName = freezed,
    Object? numberOfQuestion = freezed,
  }) {
    return _then(_value.copyWith(
      examId: freezed == examId
          ? _value.examId
          : examId // ignore: cast_nullable_to_non_nullable
              as String?,
      examType: freezed == examType
          ? _value.examType
          : examType // ignore: cast_nullable_to_non_nullable
              as String?,
      examName: freezed == examName
          ? _value.examName
          : examName // ignore: cast_nullable_to_non_nullable
              as String?,
      numberOfQuestion: freezed == numberOfQuestion
          ? _value.numberOfQuestion
          : numberOfQuestion // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PracticeLogEventParamsImplCopyWith<$Res>
    implements $PracticeLogEventParamsCopyWith<$Res> {
  factory _$$PracticeLogEventParamsImplCopyWith(
          _$PracticeLogEventParamsImpl value,
          $Res Function(_$PracticeLogEventParamsImpl) then) =
      __$$PracticeLogEventParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'exam_id') String? examId,
      @JsonKey(name: 'exam_type') String? examType,
      @JsonKey(name: 'exam_name') String? examName,
      @JsonKey(name: 'number_of_question') int? numberOfQuestion});
}

/// @nodoc
class __$$PracticeLogEventParamsImplCopyWithImpl<$Res>
    extends _$PracticeLogEventParamsCopyWithImpl<$Res,
        _$PracticeLogEventParamsImpl>
    implements _$$PracticeLogEventParamsImplCopyWith<$Res> {
  __$$PracticeLogEventParamsImplCopyWithImpl(
      _$PracticeLogEventParamsImpl _value,
      $Res Function(_$PracticeLogEventParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? examId = freezed,
    Object? examType = freezed,
    Object? examName = freezed,
    Object? numberOfQuestion = freezed,
  }) {
    return _then(_$PracticeLogEventParamsImpl(
      examId: freezed == examId
          ? _value.examId
          : examId // ignore: cast_nullable_to_non_nullable
              as String?,
      examType: freezed == examType
          ? _value.examType
          : examType // ignore: cast_nullable_to_non_nullable
              as String?,
      examName: freezed == examName
          ? _value.examName
          : examName // ignore: cast_nullable_to_non_nullable
              as String?,
      numberOfQuestion: freezed == numberOfQuestion
          ? _value.numberOfQuestion
          : numberOfQuestion // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$PracticeLogEventParamsImpl implements _PracticeLogEventParams {
  _$PracticeLogEventParamsImpl(
      {@JsonKey(name: 'exam_id') this.examId,
      @JsonKey(name: 'exam_type') this.examType,
      @JsonKey(name: 'exam_name') this.examName,
      @JsonKey(name: 'number_of_question') this.numberOfQuestion});

  @override
  @JsonKey(name: 'exam_id')
  final String? examId;
  @override
  @JsonKey(name: 'exam_type')
  final String? examType;
  @override
  @JsonKey(name: 'exam_name')
  final String? examName;
  @override
  @JsonKey(name: 'number_of_question')
  final int? numberOfQuestion;

  @override
  String toString() {
    return 'PracticeLogEventParams(examId: $examId, examType: $examType, examName: $examName, numberOfQuestion: $numberOfQuestion)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PracticeLogEventParamsImpl &&
            (identical(other.examId, examId) || other.examId == examId) &&
            (identical(other.examType, examType) ||
                other.examType == examType) &&
            (identical(other.examName, examName) ||
                other.examName == examName) &&
            (identical(other.numberOfQuestion, numberOfQuestion) ||
                other.numberOfQuestion == numberOfQuestion));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, examId, examType, examName, numberOfQuestion);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PracticeLogEventParamsImplCopyWith<_$PracticeLogEventParamsImpl>
      get copyWith => __$$PracticeLogEventParamsImplCopyWithImpl<
          _$PracticeLogEventParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PracticeLogEventParamsImplToJson(
      this,
    );
  }
}

abstract class _PracticeLogEventParams implements PracticeLogEventParams {
  factory _PracticeLogEventParams(
          {@JsonKey(name: 'exam_id') final String? examId,
          @JsonKey(name: 'exam_type') final String? examType,
          @JsonKey(name: 'exam_name') final String? examName,
          @JsonKey(name: 'number_of_question') final int? numberOfQuestion}) =
      _$PracticeLogEventParamsImpl;

  @override
  @JsonKey(name: 'exam_id')
  String? get examId;
  @override
  @JsonKey(name: 'exam_type')
  String? get examType;
  @override
  @JsonKey(name: 'exam_name')
  String? get examName;
  @override
  @JsonKey(name: 'number_of_question')
  int? get numberOfQuestion;
  @override
  @JsonKey(ignore: true)
  _$$PracticeLogEventParamsImplCopyWith<_$PracticeLogEventParamsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SocialMediaLogEventParams {
  @JsonKey(name: 'social_media_channel')
  SocialMediaChannel? get mediaChannel => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SocialMediaLogEventParamsCopyWith<SocialMediaLogEventParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SocialMediaLogEventParamsCopyWith<$Res> {
  factory $SocialMediaLogEventParamsCopyWith(SocialMediaLogEventParams value,
          $Res Function(SocialMediaLogEventParams) then) =
      _$SocialMediaLogEventParamsCopyWithImpl<$Res, SocialMediaLogEventParams>;
  @useResult
  $Res call(
      {@JsonKey(name: 'social_media_channel')
      SocialMediaChannel? mediaChannel});
}

/// @nodoc
class _$SocialMediaLogEventParamsCopyWithImpl<$Res,
        $Val extends SocialMediaLogEventParams>
    implements $SocialMediaLogEventParamsCopyWith<$Res> {
  _$SocialMediaLogEventParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mediaChannel = freezed,
  }) {
    return _then(_value.copyWith(
      mediaChannel: freezed == mediaChannel
          ? _value.mediaChannel
          : mediaChannel // ignore: cast_nullable_to_non_nullable
              as SocialMediaChannel?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SocialMediaLogEventParamsImplCopyWith<$Res>
    implements $SocialMediaLogEventParamsCopyWith<$Res> {
  factory _$$SocialMediaLogEventParamsImplCopyWith(
          _$SocialMediaLogEventParamsImpl value,
          $Res Function(_$SocialMediaLogEventParamsImpl) then) =
      __$$SocialMediaLogEventParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'social_media_channel')
      SocialMediaChannel? mediaChannel});
}

/// @nodoc
class __$$SocialMediaLogEventParamsImplCopyWithImpl<$Res>
    extends _$SocialMediaLogEventParamsCopyWithImpl<$Res,
        _$SocialMediaLogEventParamsImpl>
    implements _$$SocialMediaLogEventParamsImplCopyWith<$Res> {
  __$$SocialMediaLogEventParamsImplCopyWithImpl(
      _$SocialMediaLogEventParamsImpl _value,
      $Res Function(_$SocialMediaLogEventParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mediaChannel = freezed,
  }) {
    return _then(_$SocialMediaLogEventParamsImpl(
      mediaChannel: freezed == mediaChannel
          ? _value.mediaChannel
          : mediaChannel // ignore: cast_nullable_to_non_nullable
              as SocialMediaChannel?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$SocialMediaLogEventParamsImpl implements _SocialMediaLogEventParams {
  _$SocialMediaLogEventParamsImpl(
      {@JsonKey(name: 'social_media_channel') this.mediaChannel});

  @override
  @JsonKey(name: 'social_media_channel')
  final SocialMediaChannel? mediaChannel;

  @override
  String toString() {
    return 'SocialMediaLogEventParams(mediaChannel: $mediaChannel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SocialMediaLogEventParamsImpl &&
            (identical(other.mediaChannel, mediaChannel) ||
                other.mediaChannel == mediaChannel));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, mediaChannel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SocialMediaLogEventParamsImplCopyWith<_$SocialMediaLogEventParamsImpl>
      get copyWith => __$$SocialMediaLogEventParamsImplCopyWithImpl<
          _$SocialMediaLogEventParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SocialMediaLogEventParamsImplToJson(
      this,
    );
  }
}

abstract class _SocialMediaLogEventParams implements SocialMediaLogEventParams {
  factory _SocialMediaLogEventParams(
          {@JsonKey(name: 'social_media_channel')
          final SocialMediaChannel? mediaChannel}) =
      _$SocialMediaLogEventParamsImpl;

  @override
  @JsonKey(name: 'social_media_channel')
  SocialMediaChannel? get mediaChannel;
  @override
  @JsonKey(ignore: true)
  _$$SocialMediaLogEventParamsImplCopyWith<_$SocialMediaLogEventParamsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$LanguageLogEventParams {
  @JsonKey(name: 'language_name')
  String? get languageName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LanguageLogEventParamsCopyWith<LanguageLogEventParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LanguageLogEventParamsCopyWith<$Res> {
  factory $LanguageLogEventParamsCopyWith(LanguageLogEventParams value,
          $Res Function(LanguageLogEventParams) then) =
      _$LanguageLogEventParamsCopyWithImpl<$Res, LanguageLogEventParams>;
  @useResult
  $Res call({@JsonKey(name: 'language_name') String? languageName});
}

/// @nodoc
class _$LanguageLogEventParamsCopyWithImpl<$Res,
        $Val extends LanguageLogEventParams>
    implements $LanguageLogEventParamsCopyWith<$Res> {
  _$LanguageLogEventParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? languageName = freezed,
  }) {
    return _then(_value.copyWith(
      languageName: freezed == languageName
          ? _value.languageName
          : languageName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LanguageLogEventParamsImplCopyWith<$Res>
    implements $LanguageLogEventParamsCopyWith<$Res> {
  factory _$$LanguageLogEventParamsImplCopyWith(
          _$LanguageLogEventParamsImpl value,
          $Res Function(_$LanguageLogEventParamsImpl) then) =
      __$$LanguageLogEventParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'language_name') String? languageName});
}

/// @nodoc
class __$$LanguageLogEventParamsImplCopyWithImpl<$Res>
    extends _$LanguageLogEventParamsCopyWithImpl<$Res,
        _$LanguageLogEventParamsImpl>
    implements _$$LanguageLogEventParamsImplCopyWith<$Res> {
  __$$LanguageLogEventParamsImplCopyWithImpl(
      _$LanguageLogEventParamsImpl _value,
      $Res Function(_$LanguageLogEventParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? languageName = freezed,
  }) {
    return _then(_$LanguageLogEventParamsImpl(
      languageName: freezed == languageName
          ? _value.languageName
          : languageName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$LanguageLogEventParamsImpl implements _LanguageLogEventParams {
  _$LanguageLogEventParamsImpl(
      {@JsonKey(name: 'language_name') this.languageName});

  @override
  @JsonKey(name: 'language_name')
  final String? languageName;

  @override
  String toString() {
    return 'LanguageLogEventParams(languageName: $languageName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguageLogEventParamsImpl &&
            (identical(other.languageName, languageName) ||
                other.languageName == languageName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, languageName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LanguageLogEventParamsImplCopyWith<_$LanguageLogEventParamsImpl>
      get copyWith => __$$LanguageLogEventParamsImplCopyWithImpl<
          _$LanguageLogEventParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LanguageLogEventParamsImplToJson(
      this,
    );
  }
}

abstract class _LanguageLogEventParams implements LanguageLogEventParams {
  factory _LanguageLogEventParams(
          {@JsonKey(name: 'language_name') final String? languageName}) =
      _$LanguageLogEventParamsImpl;

  @override
  @JsonKey(name: 'language_name')
  String? get languageName;
  @override
  @JsonKey(ignore: true)
  _$$LanguageLogEventParamsImplCopyWith<_$LanguageLogEventParamsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$FontSizeLogEventParams {
  @JsonKey(name: 'font_size')
  int? get fontSize => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FontSizeLogEventParamsCopyWith<FontSizeLogEventParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FontSizeLogEventParamsCopyWith<$Res> {
  factory $FontSizeLogEventParamsCopyWith(FontSizeLogEventParams value,
          $Res Function(FontSizeLogEventParams) then) =
      _$FontSizeLogEventParamsCopyWithImpl<$Res, FontSizeLogEventParams>;
  @useResult
  $Res call({@JsonKey(name: 'font_size') int? fontSize});
}

/// @nodoc
class _$FontSizeLogEventParamsCopyWithImpl<$Res,
        $Val extends FontSizeLogEventParams>
    implements $FontSizeLogEventParamsCopyWith<$Res> {
  _$FontSizeLogEventParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fontSize = freezed,
  }) {
    return _then(_value.copyWith(
      fontSize: freezed == fontSize
          ? _value.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FontSizeLogEventParamsImplCopyWith<$Res>
    implements $FontSizeLogEventParamsCopyWith<$Res> {
  factory _$$FontSizeLogEventParamsImplCopyWith(
          _$FontSizeLogEventParamsImpl value,
          $Res Function(_$FontSizeLogEventParamsImpl) then) =
      __$$FontSizeLogEventParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'font_size') int? fontSize});
}

/// @nodoc
class __$$FontSizeLogEventParamsImplCopyWithImpl<$Res>
    extends _$FontSizeLogEventParamsCopyWithImpl<$Res,
        _$FontSizeLogEventParamsImpl>
    implements _$$FontSizeLogEventParamsImplCopyWith<$Res> {
  __$$FontSizeLogEventParamsImplCopyWithImpl(
      _$FontSizeLogEventParamsImpl _value,
      $Res Function(_$FontSizeLogEventParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fontSize = freezed,
  }) {
    return _then(_$FontSizeLogEventParamsImpl(
      fontSize: freezed == fontSize
          ? _value.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$FontSizeLogEventParamsImpl implements _FontSizeLogEventParams {
  _$FontSizeLogEventParamsImpl({@JsonKey(name: 'font_size') this.fontSize});

  @override
  @JsonKey(name: 'font_size')
  final int? fontSize;

  @override
  String toString() {
    return 'FontSizeLogEventParams(fontSize: $fontSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FontSizeLogEventParamsImpl &&
            (identical(other.fontSize, fontSize) ||
                other.fontSize == fontSize));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, fontSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FontSizeLogEventParamsImplCopyWith<_$FontSizeLogEventParamsImpl>
      get copyWith => __$$FontSizeLogEventParamsImplCopyWithImpl<
          _$FontSizeLogEventParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FontSizeLogEventParamsImplToJson(
      this,
    );
  }
}

abstract class _FontSizeLogEventParams implements FontSizeLogEventParams {
  factory _FontSizeLogEventParams(
          {@JsonKey(name: 'font_size') final int? fontSize}) =
      _$FontSizeLogEventParamsImpl;

  @override
  @JsonKey(name: 'font_size')
  int? get fontSize;
  @override
  @JsonKey(ignore: true)
  _$$FontSizeLogEventParamsImplCopyWith<_$FontSizeLogEventParamsImpl>
      get copyWith => throw _privateConstructorUsedError;
}
