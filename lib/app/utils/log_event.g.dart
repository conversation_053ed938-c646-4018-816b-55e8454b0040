// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Map<String, dynamic> _$$BasicLogEventParamsImplToJson(
        _$BasicLogEventParamsImpl instance) =>
    <String, dynamic>{
      'event_name': instance.eventName,
      'screen_location': instance.screenLocation,
      'location_type': _$LocationTypeEnumMap[instance.locationType],
      'location_name': instance.locationName,
    };

const _$LocationTypeEnumMap = {
  LocationType.popup: 'popup',
  LocationType.bottomsheet: 'bottomsheet',
  LocationType.screen: 'screen',
  LocationType.sidepanel: 'sidepanel',
};

Map<String, dynamic> _$$PracticeLogEventParamsImplToJson(
        _$PracticeLogEventParamsImpl instance) =>
    <String, dynamic>{
      'exam_id': instance.examId,
      'exam_type': instance.examType,
      'exam_name': instance.examName,
      'number_of_question': instance.numberOfQuestion,
    };

Map<String, dynamic> _$$SocialMediaLogEventParamsImplToJson(
        _$SocialMediaLogEventParamsImpl instance) =>
    <String, dynamic>{
      'social_media_channel':
          _$SocialMediaChannelEnumMap[instance.mediaChannel],
    };

const _$SocialMediaChannelEnumMap = {
  SocialMediaChannel.facebook: 'facebook',
  SocialMediaChannel.messenger: 'messenger',
  SocialMediaChannel.website: 'website',
  SocialMediaChannel.email: 'email',
};

Map<String, dynamic> _$$LanguageLogEventParamsImplToJson(
        _$LanguageLogEventParamsImpl instance) =>
    <String, dynamic>{
      'language_name': instance.languageName,
    };

Map<String, dynamic> _$$FontSizeLogEventParamsImplToJson(
        _$FontSizeLogEventParamsImpl instance) =>
    <String, dynamic>{
      'font_size': instance.fontSize,
    };
