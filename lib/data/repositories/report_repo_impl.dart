import 'package:scrumpass_exam_simulator/data/data_source/remote/report_service.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/report_repository.dart';

final class ReportRepoImpl implements ReportRepository {
  final reportService = ReportService();

  @override
  Future<String> sendReport(
      String appId, String qid, String reportDetail, String debugid) {
    return reportService.sendReport(appId, qid, reportDetail, debugid);
  }
}
