import 'package:scrumpass_exam_simulator/data/data_source/remote/test_service.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/data/models/quiz_response.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/test_repository.dart';

final class TestRepositoryImpl implements TestRepository {
  final testService = TestService();
  @override
  Future<List<QuestionResponse>> doPracticeTest(int numQuestion) {
    return testService.getQuickTest(numQuestion);
  }

  @override
  Future<List<QuestionResponse>> doDomainTest(int numQuestion, List<TestDomain> domains) {
    return testService.getDomainTest(numQuestion, domains);
  }

  @override
  Future<List<QuizResponse>> getListMockTest() {
    return testService.getListMockTest();
  }

  @override
  Future<List<QuestionResponse>> getQuestionMockTest(int id) {
    return testService.getQuestionMockTest(id);
  }
}
