import 'package:scrumpass_exam_simulator/data/data_source/remote/app_service.dart';
import 'package:scrumpass_exam_simulator/data/models/relative_app_response.dart';
import 'package:scrumpass_exam_simulator/domain/repositories/app_repository.dart';

final class AppRepoImpl implements AppRepository {
  final appService = AppService();
  @override
  Future<List<RelativeAppResponse>> getRelativeApp(String appId) {
    return appService.getRelativeApp(appId);
  }

  @override
  Future<String> getNumCategory(String appId) {
    return appService.getNumCategory(appId);
  }

  @override
  Future<String> getAnalyticRadar(String appId, List<String> qids) {
    return appService.getAnalyticRadar(appId, qids);
  }
}
