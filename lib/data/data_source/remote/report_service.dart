import 'package:scrumpass_exam_simulator/app/config/api_url.dart';
import 'package:scrumpass_exam_simulator/app/core/networking/base/api.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/data/data_source/remote/body/send_report_body.dart';

class ReportService {
  final service = ApiService(apiUrl);

  Future<String> sendReport(
      String appId, String qid, String reportDetail, String debugid) async {
    final data = SendReportBody(
            appid: appId,
            qid: qid,
            reportdetail: reportDetail,
            debugid: debugid)
        .toJson();
    final body = {'key': Constants.key, 'data': data};
    final response =
        await service.postData(endPoint: ApiUrl.kSendReport, body: body);
    final responseData = response.data;
    return responseData;
  }
}
