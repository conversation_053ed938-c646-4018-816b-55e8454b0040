import 'dart:convert';

import 'package:scrumpass_exam_simulator/app/config/api_url.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/core/networking/base/api.dart';
import 'package:scrumpass_exam_simulator/data/data_source/remote/body/analytic_radar_request_body.dart';
import 'package:scrumpass_exam_simulator/data/data_source/remote/body/app_request_body.dart';
import 'package:scrumpass_exam_simulator/data/models/relative_app_response.dart';

class AppService {
  final service = ApiService(apiUrl);

  Future<List<RelativeAppResponse>> getRelativeApp(String appId) async {
    final data = AppRequestBody(appId: appId).toJson();
    final body = {'key': Constants.key, 'data': data};
    final response =
        await service.postData(endPoint: ApiUrl.kRelativeApp, body: body);
    final responseData = jsonDecode(response.data);
    final result = responseData
        .map<RelativeAppResponse>((e) => RelativeAppResponse.fromJson(e))
        .toList();
    return result;
  }

  Future<String> getNumCategory(String appId) async {
    final data = AppRequestBody(appId: appId).toJson();
    final body = {'key': Constants.key, 'data': data};
    final response =
        await service.postData(endPoint: ApiUrl.kNumCategory, body: body);
    final responseData = response.data;
    return responseData;
  }

  Future<String> getAnalyticRadar(String appId, List<String> qids) async {
    final correctQuestions = qids.join(',');
    final data = AnalyticRadarRequestBody(
            appId: appId, correctQuestions: correctQuestions)
        .toJson();
    final body = {'key': Constants.key, 'data': data};
    final response =
        await service.postData(endPoint: ApiUrl.kAnalyticRadar, body: body);
    final responseData = jsonDecode(response.data);
    return responseData['data'];
  }
}
