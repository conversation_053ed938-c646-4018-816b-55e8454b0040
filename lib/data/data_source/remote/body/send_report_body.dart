import 'package:json_annotation/json_annotation.dart';
part 'send_report_body.g.dart';

@JsonSerializable()
class SendReportBody {
  SendReportBody({
    required this.appid,
    required this.qid,
    required this.reportdetail,
    required this.debugid,
  });
  final String appid;
  final String qid;
  final String reportdetail;
  final String debugid;

  factory SendReportBody.fromJson(Map<String, dynamic> json) => _$SendReportBodyFromJson(json);

  Map<String, dynamic> toJson() => _$SendReportBodyToJson(this);
}
