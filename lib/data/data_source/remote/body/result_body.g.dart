// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'result_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResultRequest _$ResultRequestFromJson(Map<String, dynamic> json) =>
    ResultRequest(
      status: json['status'] as String,
      time: DateTime.parse(json['time'] as String),
      quizType: json['quiz_type'] as String? ?? "1",
      environment: json['environment'] as String?,
      percentage: json['percentage'] as String,
      name: json['name'] as String,
      examQuiz: json['exam_quiz'] as String,
      questions: (json['questions'] as List<dynamic>)
          .map((e) => ResultQuestionRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
      passPercent: (json['pass_percent'] as num).toInt(),
      quizId: json['quiz_id'] as String,
      quizDuration: (json['quiz_duration'] as num).toInt(),
      timeDoQuiz: (json['time_do_quiz'] as num).toInt(),
      shuffleOptions: json['shuffle_options'] as bool? ?? false,
    );

Map<String, dynamic> _$ResultRequestToJson(ResultRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'status': instance.status,
      'percentage': instance.percentage,
      'time': instance.time.toIso8601String(),
      'questions': instance.questions.map((e) => e.toJson()).toList(),
      'pass_percent': instance.passPercent,
      'quiz_id': instance.quizId,
      'quiz_type': instance.quizType,
      'quiz_duration': instance.quizDuration,
      'time_do_quiz': instance.timeDoQuiz,
      'exam_quiz': instance.examQuiz,
      'environment': instance.environment,
      'shuffle_options': instance.shuffleOptions,
    };

ResultQuestionRequest _$ResultQuestionRequestFromJson(
        Map<String, dynamic> json) =>
    ResultQuestionRequest(
      qid: json['qid'] as String?,
      type: json['type'] as String?,
      question: json['question'] as String?,
      description: json['description'] as String?,
      environment: json['environment'] as String?,
      quizId: json['quiz_id'] as String?,
      correct: json['correct'] as String?,
      correctIndex: (json['correct_index'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      nextQuestion: json['next_question'] as String?,
    );

Map<String, dynamic> _$ResultQuestionRequestToJson(
        ResultQuestionRequest instance) =>
    <String, dynamic>{
      'qid': instance.qid,
      'type': instance.type,
      'question': instance.question,
      'correct': instance.correct,
      'correct_index': instance.correctIndex,
      'quiz_id': instance.quizId,
      'description': instance.description,
      'environment': instance.environment,
      'next_question': instance.nextQuestion,
    };
