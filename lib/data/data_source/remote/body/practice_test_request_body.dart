import 'package:json_annotation/json_annotation.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
part 'practice_test_request_body.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class PracticeTestRequestBody {
  PracticeTestRequestBody(
      {this.appId = Constants.appId, this.numQuestion = 10, this.domain = 'all'});
  final String appId;
  @J<PERSON><PERSON>ey(name: 'noq')
  final int numQuestion;
  final String domain;

  factory PracticeTestRequestBody.fromJson(Map<String, dynamic> json) =>
      _$PracticeTestRequestBodyFromJson(json);

  Map<String, dynamic> toJson() => _$PracticeTestRequestBodyToJson(this);
}
