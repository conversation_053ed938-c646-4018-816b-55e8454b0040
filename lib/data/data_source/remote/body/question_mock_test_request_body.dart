import 'package:json_annotation/json_annotation.dart';
part 'question_mock_test_request_body.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class QuestionMockTestRequestBody {
  QuestionMockTestRequestBody(
      {required this.id});
  final String id;

  factory QuestionMockTestRequestBody.fromJson(Map<String, dynamic> json) =>
      _$QuestionMockTestRequestBodyFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionMockTestRequestBodyToJson(this);
}
