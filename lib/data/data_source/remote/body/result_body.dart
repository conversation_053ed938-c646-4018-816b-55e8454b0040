import 'package:json_annotation/json_annotation.dart';

part 'result_body.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class ResultRequest {
  final String name, status, percentage;
  final DateTime time;
  final List<ResultQuestionRequest> questions;
  // final List<dynamic> answers;
  // final List<dynamic> answersText;
  // final List<dynamic> bookmark;
  final int passPercent;
  final String quizId;
  final String quizType;
  final int quizDuration;
  final int timeDoQuiz;
  final String examQuiz;
  final String? environment;
  final bool shuffleOptions;

  ResultRequest({
    required this.status,
    required this.time,
    this.quizType = "1",
    this.environment,
    required this.percentage,
    required this.name,
    required this.examQuiz,
    required this.questions,
    // required this.answers,
    // required this.answersText,
    // required this.bookmark,
    required this.passPercent,
    required this.quizId,
    required this.quizDuration,
    required this.timeDoQuiz,
    this.shuffleOptions = false,
  });
}

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class ResultQuestionRequest {
  ResultQuestionRequest({
    this.qid,
    this.type,
    this.question,
    this.description,
    this.environment,
    this.quizId,
    this.correct,
    this.correctIndex,
    this.nextQuestion,
  });

  final String? qid;
  final String? type;
  final String? question;
  final String? correct;
  final List<int>? correctIndex;
  final String? quizId;
  final String? description;
  final String? environment;
  final String? nextQuestion;
}
