import 'package:json_annotation/json_annotation.dart';
part 'analytic_radar_request_body.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class AnalyticRadarRequestBody {
  AnalyticRadarRequestBody(
      {required this.appId, required this.correctQuestions});
  final String appId;
  final String correctQuestions;

  factory AnalyticRadarRequestBody.fromJson(Map<String, dynamic> json) =>
      _$AnalyticRadarRequestBodyFromJson(json);

  Map<String, dynamic> toJson() => _$AnalyticRadarRequestBodyToJson(this);
}
