import 'package:json_annotation/json_annotation.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
part 'list_mock_test_request_body.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class ListMockTestRequestBody {
  ListMockTestRequestBody(
      {this.appId = Constants.appId});
  final String appId;

  factory ListMockTestRequestBody.fromJson(Map<String, dynamic> json) =>
      _$ListMockTestRequestBodyFromJson(json);

  Map<String, dynamic> toJson() => _$ListMockTestRequestBodyToJson(this);
}
