import 'dart:convert';

import 'package:scrumpass_exam_simulator/app/config/api_url.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/core/networking/base/api.dart';
import 'package:scrumpass_exam_simulator/data/data_source/remote/body/list_mock_test_request_body.dart';
import 'package:scrumpass_exam_simulator/data/data_source/remote/body/practice_test_request_body.dart';
import 'package:scrumpass_exam_simulator/data/data_source/remote/body/question_mock_test_request_body.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/question_response.dart';
import 'package:scrumpass_exam_simulator/data/models/quiz_response.dart';

class TestService {
  final service = ApiService(apiUrl);

  Future<List<QuestionResponse>> getQuickTest(int numQuestion) async {
    final data = PracticeTestRequestBody(numQuestion: numQuestion).toJson();
    final body = {'key': Constants.key, 'data': data};
    final response =
        await service.postData(endPoint: ApiUrl.kPracticeTest, body: body);
    final responseData = jsonDecode(response.data);
    if (responseData['error']) {
      return [];
    }
    final result = responseData['data']
        .map<QuestionResponse>((e) => QuestionResponse.fromJson(e))
        .toList();
    return result;
  }

  Future<List<QuestionResponse>> getDomainTest(
      int numQuestion, List<TestDomain> domains) async {
    final domainString = domains.map((e) => e.text).join(',');
    final data =
        PracticeTestRequestBody(numQuestion: numQuestion, domain: domainString)
            .toJson();
    final body = {'key': Constants.key, 'data': data};
    final response =
        await service.postData(endPoint: ApiUrl.kPracticeTest, body: body);
    final responseData = jsonDecode(response.data);
    if (responseData['error']) {
      return [];
    }
    final result = responseData['data']
        .map<QuestionResponse>((e) => QuestionResponse.fromJson(e))
        .toList();
    return result;
  }

  Future<List<QuizResponse>> getListMockTest() async {
    final data =
        ListMockTestRequestBody()
            .toJson();
    final body = {'key': Constants.key, 'data': data};
    final response =
        await service.postData(endPoint: ApiUrl.kListMockTest, body: body);
    final responseData = jsonDecode(response.data);
    if (responseData['error']) {
      return [];
    }
    final result = responseData['data']
        .map<QuizResponse>((e) => QuizResponse.fromJson(e))
        .toList();
    return result;
  }

  Future<List<QuestionResponse>> getQuestionMockTest(
      int id) async {
    final data =
        QuestionMockTestRequestBody(id: id.toString())
            .toJson();
    final body = {'key': Constants.key, 'data': data};
    final response =
        await service.postData(endPoint: ApiUrl.kQuestionMockTest, body: body);
    final responseData = jsonDecode(response.data);
    if (responseData['error']) {
      return [];
    }
    final result = responseData['data']
        .map<QuestionResponse>((e) => QuestionResponse.fromJson(e))
        .toList();
    return result;
  }
}
