import 'dart:io';

import 'package:get/get.dart';
import 'package:isar/isar.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/app/utils/log.dart';
import 'package:scrumpass_exam_simulator/app/utils/utils.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/app_data.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';

class _BaseLocalService {
  const _BaseLocalService(this.isar);
  final Isar isar;
}

class LocalService extends _BaseLocalService with _AppDataMixin {
  LocalService() : super(Get.find<Isar>());
}

mixin _AppDataMixin on _BaseLocalService {
  AppData? getAppData() {
    final appData = isar.appDatas.where().findFirstSync();
    return appData;
  }

  Future<void> saveAppData(AppData appData) {
    return isar.writeTxn(() async {
      await isar.appDatas.put(appData);
    });
  }

  Future<void> initAppData() async {
    AppData? appData = isar.appDatas.where().findFirstSync();
    if (appData == null) {
      final debugId = _generateId(appData);
      appData = AppData(
        debugId: debugId,
        environment: environment,
        locale: Language.en,
        trainingMode: TrainingMode.practice,
        testFontSize: 18,
        reminder: Reminder(dayReminders: []),
        lastOpen: DateTime.now(),
      );
      await isar.writeTxn(() async {
        await isar.appDatas.put(appData!);
      });
    } else {
      Global.updateUsername(appData.userName ?? '');
      Global.updateTestDate(appData.testDate);
      Global.updateLaunchState(appData.firstLaunch);
      Global.locale = appData.locale ?? Language.en;
      Global.updateExamFontSize(appData.testFontSize ?? 18);
      Global.updateTourStateAppData(appData);
    }
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;

    if (Platform.isIOS && Constants.appId == "com.exampass.psm") {
      final parts = version.split('.');
      if (parts.length >= 1) {
        int major = int.tryParse(parts[0]) ?? 0;
        major -= 2;
        if (major <= 0) {
          version = '1.0.0';
        } else {
          parts[0] = major.toString();
          version = parts.join('.');
        }
      }
    }
    Global.appVersion = version;
  }

  Future<void> generateSessionId() async {
    AppData? appData = isar.appDatas.where().findFirstSync();
    final sessionId = Utils.generateRandomString(10);
    appData = appData?.copyWith(sessionId: sessionId);
    if (appData != null) {
      logDebug("Generated sessionId: $sessionId");
      await isar.writeTxn(() async {
        await isar.appDatas.put(appData!);
      });
    }
  }

  String _generateId(AppData? appData) {
    final server = environment == Environment.prod ? "SGP" : "DEU";
    if (appData?.debugId?.isEmpty ?? true) {
      final generateId = Utils.generateRandomString(10);
      final debugCode = "$generateId-$server";
      return debugCode;
    } else {
      final dashIndex = appData?.debugId!.indexOf('-');
      final string = appData?.debugId!.substring(0, dashIndex);
      final debugId = "$string-$server";
      return debugId;
    }
  }

  Future<void> saveQuestions(List<QuestionEntity> questions) {
    return isar.writeTxn(() async {
      await isar.questionEntitys.putAll(questions);
    });
  }

  List<QuestionEntity> getAllQuestions() {
    final questions = isar.questionEntitys.where().findAllSync();
    return questions;
  }

  Future<List<ResultEntity>> getDoneResult() async {
    final results = await isar.resultEntitys
        .filter()
        .statusEqualTo(ResultStatus.done)
        .sortByTimeDesc()
        .findAll();
    return results;
  }

  Future<List<ResultEntity>> getResult() async {
    final results = await isar.resultEntitys.where().sortByTimeDesc().findAll();
    return results;
  }

  Future<ResultEntity?> getNotDoneResult() async {
    final results = await isar.resultEntitys
        .filter()
        .statusEqualTo(ResultStatus.notDone)
        .findFirst();
    return results;
  }

  Future<ResultEntity?> getNotDoneMockTestResult() async {
    final results = await isar.resultEntitys
        .filter()
        .statusEqualTo(ResultStatus.notDone)
        .typeEqualTo(TestType.fullTest)
        .findFirst();
    return results;
  }

  Future<ResultEntity?> getResultById(int id) async {
    final results = await isar.resultEntitys.filter().idEqualTo(id).findFirst();
    return results;
  }

  Future<void> saveResult(ResultEntity result) {
    return isar.writeTxn(() async {
      await isar.resultEntitys.put(result);
    });
  }

  Future<void> updateLinkResult(ResultEntity result) {
    return isar.writeTxn(() async {
      await result.mockTest.save();
      await result.mockTest.load();
    });
  }

  Future<void> loadLinkResult(ResultEntity result) {
    return isar.writeTxn(() async {
      await result.mockTest.load();
    });
  }

  Future<void> clearNotDonePracticeResult() {
    return isar.writeTxn(() async {
      await isar.resultEntitys
          .filter()
          .statusEqualTo(ResultStatus.notDone)
          .not()
          .typeEqualTo(TestType.fullTest)
          .deleteAll();
    });
  }

  Future<void> clearNotDoneMockResult() {
    return isar.writeTxn(() async {
      await isar.resultEntitys
          .filter()
          .statusEqualTo(ResultStatus.notDone)
          .typeEqualTo(TestType.fullTest)
          .deleteAll();
    });
  }

  Future<void> clearNotDoneResult() {
    return isar.writeTxn(() async {
      await isar.resultEntitys
          .filter()
          .statusEqualTo(ResultStatus.notDone)
          .deleteAll();
    });
  }

  Future<List<QuizEntity>> getQuiz() async {
    final quiz = await isar.quizEntitys.where().findAll();
    return quiz;
  }

  Future<void> saveQuiz(List<QuizEntity> quiz) {
    return isar.writeTxn(() async {
      await isar.quizEntitys.putAll(quiz);
    });
  }
}
