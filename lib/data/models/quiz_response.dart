import 'package:json_annotation/json_annotation.dart';

part 'quiz_response.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class QuizResponse {
  QuizResponse({
    required this.quid,
    required this.quizName,
    required this.noq,
    required this.duration,
    required this.passPercentage,
    required this.description,
    required this.premium,
  });

  final String quid;
  final String quizName;
  final String noq;
  final String duration;
  final String passPercentage;
  final String description;
  final bool premium;

  factory QuizResponse.fromJson(Map<String, dynamic> json) =>
      _$QuizResponseFromJson(json);
}
