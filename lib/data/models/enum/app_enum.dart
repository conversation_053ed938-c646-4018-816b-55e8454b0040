import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_constants.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

enum TrainingMode { practice, quickPractice, exam }

extension TrainingModeExt on TrainingMode {
  String get text {
    switch (this) {
      case TrainingMode.practice:
        return LocaleKeys.practiceModeText.tr;
      case TrainingMode.quickPractice:
        return LocaleKeys.quickPracticeModeText.tr;
      case TrainingMode.exam:
        return LocaleKeys.examModeText.tr;
    }
  }

  String get title {
    switch (this) {
      case TrainingMode.practice:
        return LocaleKeys.practiceModeTitle.tr;
      case TrainingMode.quickPractice:
        return LocaleKeys.quickPracticeModeTitle.tr;
      case TrainingMode.exam:
        return LocaleKeys.examModeTitle.tr;
    }
  }

  String get description {
    switch (this) {
      case TrainingMode.practice:
        return LocaleKeys.practiceModeDescription.tr;
      case TrainingMode.quickPractice:
        return LocaleKeys.quickPracticeModeDescription.tr;
      case TrainingMode.exam:
        return LocaleKeys.examModeDescription.tr;
    }
  }

  Color get color {
    switch (this) {
      case TrainingMode.practice:
        return AppColors.yellow1;
      case TrainingMode.quickPractice:
        return AppColors.green1;
      case TrainingMode.exam:
        return AppColors.baseBlue1;
    }
  }

  String get eventName {
    switch (this) {
      case TrainingMode.practice:
        return EventLogConstants.selectPracticeMode;
      case TrainingMode.quickPractice:
        return EventLogConstants.selectQuickPracticeMode;
      case TrainingMode.exam:
        return EventLogConstants.selectSimulateTheExam;
    }
  }
}

enum Language { en, vi, de, fr, hi }

extension LanguageExt on Language {
  Locale get locale {
    switch (this) {
      case Language.en:
        return Locale('en');
      case Language.vi:
        return Locale('vi');
      case Language.de:
        return Locale('de');
      case Language.fr:
        return Locale('fr');
      case Language.hi:
        return Locale('hi');
    }
  }

  String get icon {
    switch (this) {
      case Language.en:
        return Assets.images.enFlag.path;
      case Language.vi:
        return Assets.images.viFlag.path;
      case Language.de:
        return Assets.images.deFlag.path;
      case Language.fr:
        return Assets.images.frFlag.path;
      case Language.hi:
        return Assets.images.hiFlag.path;
    }
  }

  String get text {
    switch (this) {
      case Language.en:
        return 'English';
      case Language.vi:
        return 'Vietnamese';
      case Language.de:
        return 'Germany';
      case Language.fr:
        return 'French';
      case Language.hi:
        return 'India';
    }
  }
}

enum ReminderDay {
  monday,
  tuesday,
  wednesday,
  thursday,
  friday,
  saturday,
  sunday
}

extension ReminderDayExtension on ReminderDay {
  String get text {
    switch (this) {
      case ReminderDay.monday:
        return LocaleKeys.monday.tr;
      case ReminderDay.tuesday:
        return LocaleKeys.tuesday.tr;
      case ReminderDay.wednesday:
        return LocaleKeys.wednesday.tr;
      case ReminderDay.thursday:
        return LocaleKeys.thursday.tr;
      case ReminderDay.friday:
        return LocaleKeys.friday.tr;
      case ReminderDay.saturday:
        return LocaleKeys.saturday.tr;
      case ReminderDay.sunday:
        return LocaleKeys.sunday.tr;
    }
  }

  int get id {
    switch (this) {
      case ReminderDay.monday:
        return DateTime.monday;
      case ReminderDay.tuesday:
        return DateTime.tuesday;
      case ReminderDay.wednesday:
        return DateTime.wednesday;
      case ReminderDay.thursday:
        return DateTime.thursday;
      case ReminderDay.friday:
        return DateTime.friday;
      case ReminderDay.saturday:
        return DateTime.saturday;
      case ReminderDay.sunday:
        return DateTime.sunday;
    }
  }
}

enum SocialMediaChannel { facebook, messenger, website, email }

extension SocialMediaChannelExt on SocialMediaChannel {
  String get text {
    switch (this) {
      case SocialMediaChannel.facebook:
        return "Facebook";
      case SocialMediaChannel.messenger:
        return "Messenger";
      case SocialMediaChannel.website:
        return "Website";
      case SocialMediaChannel.email:
        return "Email";
    }
  }
}
