import 'dart:ui';

import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';

enum QuestionStatus { notAnswered, answered, doing }

/// Dùng cho list question bottomsheet
enum QuestionListStatus { notDone, done, bookmarked, doing }

enum OptionStatus { correct, wrong, none, selected, notSelectCorrect }

enum QuestionType {
  @JsonValue('single')
  single,
  @JsonValue('multi')
  multiple
}

enum ResultFilter { all, correct, incorrect, bookmarked, notDone }

extension ResultFilterExt on ResultFilter {
  String get text {
    switch (this) {
      case ResultFilter.all:
        return LocaleKeys.questions.tr;
      case ResultFilter.correct:
        return LocaleKeys.correctAnswer.tr;
      case ResultFilter.incorrect:
        return LocaleKeys.incorrectAnswer.tr;
      case ResultFilter.bookmarked:
        return LocaleKeys.bookmarkedAnswer.tr;
      case ResultFilter.notDone:
        return LocaleKeys.unansweredQuestion.tr;
    }
  }

  String get emptyText {
    switch (this) {
      case ResultFilter.all:
        return LocaleKeys.noQ.tr;
      case ResultFilter.correct:
        return LocaleKeys.noCorrectQ.tr;
      case ResultFilter.incorrect:
        return LocaleKeys.noIncorrectQ.tr;
      case ResultFilter.bookmarked:
        return LocaleKeys.noBookmarkQ.tr;
      case ResultFilter.notDone:
        return LocaleKeys.noUnanswerQ.tr;
    }
  }

  String get tabText {
    switch (this) {
      case ResultFilter.all:
        return LocaleKeys.all.tr;
      case ResultFilter.correct:
        return LocaleKeys.correctAnswer.tr;
      case ResultFilter.incorrect:
        return LocaleKeys.incorrectAnswer.tr;
      case ResultFilter.bookmarked:
        return LocaleKeys.bookmarkedAnswer.tr;
      case ResultFilter.notDone:
        return LocaleKeys.unansweredQuestion.tr;
    }
  }

  String get reviewTabText {
    switch (this) {
      case ResultFilter.all:
        return LocaleKeys.all.tr;
      case ResultFilter.correct:
        return LocaleKeys.correctAnswerReviewTab.tr;
      case ResultFilter.incorrect:
        return LocaleKeys.incorrectAnswerReviewTab.tr;
      case ResultFilter.bookmarked:
        return LocaleKeys.bookmarkedAnswerReviewTab.tr;
      case ResultFilter.notDone:
        return LocaleKeys.unansweredQuestionReviewTab.tr;
    }
  }
}

enum ResultStatus { notDone, done }

enum TestType { quickPractice, missed, bookmark, domain, fullTest, miniTest }

extension TestTypeExt on TestType {
  String get icon {
    switch (this) {
      case TestType.quickPractice:
        return Assets.images.bolt.path;
      case TestType.missed:
        return Assets.images.notificationLinesRemove.path;
      case TestType.bookmark:
        return Assets.images.bookmarkSquareMinimalistic.path;
      case TestType.domain:
        return Assets.images.palette.path;
      case TestType.fullTest:
        return Assets.images.documentsMinimalistic.path;
      case TestType.miniTest:
        return Assets.images.document.path;
    }
  }

  String get testName {
    switch (this) {
      case TestType.quickPractice:
        return LocaleKeys.quickTest.tr;
      case TestType.missed:
        return LocaleKeys.missedQuestionTest.tr;
      case TestType.bookmark:
        return LocaleKeys.bookmarkQuestionTest.tr;
      case TestType.domain:
        return LocaleKeys.domainKnowledgeTest.tr;
      case TestType.fullTest:
        return LocaleKeys.fullTest.tr;
      case TestType.miniTest:
        return LocaleKeys.miniTest.tr;
    }
  }

  String get testTypeKey {
    switch (this) {
      case TestType.quickPractice:
        return LocaleKeys.quickTest;
      case TestType.missed:
        return LocaleKeys.missedQuestionTest;
      case TestType.bookmark:
        return LocaleKeys.bookmarkQuestionTest;
      case TestType.domain:
        return LocaleKeys.domainKnowledgeTest;
      case TestType.fullTest:
        return LocaleKeys.fullTest;
      case TestType.miniTest:
        return LocaleKeys.miniTest;
    }
  }

  Color get bgColor {
    switch (this) {
      case TestType.quickPractice:
        return AppColors.quickTestBg;
      case TestType.missed:
        return AppColors.missedTestBg;
      case TestType.bookmark:
        return AppColors.bookmarkTestBg;
      case TestType.domain:
        return AppColors.domainTestBg;
      case TestType.fullTest:
        return AppColors.fullTestBg;
      case TestType.miniTest:
        return AppColors.miniTestBg;
    }
  }
}

enum TestDomain {
  all,
  scrumEvent,
  scrumArtifact,
  scrumTeamAndOrganization,
  leadershipAndManagementSkills,
  productDevelopmentAndManagement
}

extension TestDomainExt on TestDomain {
  String get text {
    switch (this) {
      case TestDomain.all:
        return LocaleKeys.all.tr;
      case TestDomain.scrumEvent:
        return "Scrum Event";
      case TestDomain.scrumArtifact:
        return "Scrum Artifacts";
      case TestDomain.scrumTeamAndOrganization:
        return "Scrum Team and Organization";
      case TestDomain.leadershipAndManagementSkills:
        return "Leadership and management skills";
      case TestDomain.productDevelopmentAndManagement:
        return "Product Development and Management";
    }
  }
}
