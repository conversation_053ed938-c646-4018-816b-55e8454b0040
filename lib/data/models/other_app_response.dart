import 'package:json_annotation/json_annotation.dart';

part 'other_app_response.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class OtherAppResponse {
  OtherAppResponse({
    required this.appid,
    required this.appIcon,
    required this.driveIcon,
    required this.appName,
    required this.iosID,
    required this.androidID,
  });

  final String appid;
  final String appIcon;
  final String driveIcon;
  @JsonKey(name: 'appname')
  final String appName;
  final String iosID;
  final String androidID;

  factory OtherAppResponse.fromJson(Map<String, dynamic> json) =>
      _$OtherAppResponseFromJson(json);
}
