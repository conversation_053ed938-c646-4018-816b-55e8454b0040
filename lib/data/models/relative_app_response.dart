import 'package:json_annotation/json_annotation.dart';

part 'relative_app_response.g.dart';

@JsonSerializable(createToJson: false)
class RelativeAppResponse {
  RelativeAppResponse({
    this.name,
    this.iosId,
    this.androidId,
    this.icon,
  });

  final String? name;
  final String? iosId;
  final String? androidId;
  final String? icon;

  factory RelativeAppResponse.fromJson(Map<String, dynamic> json) =>
      _$RelativeAppResponseFromJson(json);
}
