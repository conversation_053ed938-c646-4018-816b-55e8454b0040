import 'package:json_annotation/json_annotation.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';

part 'question_response.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class QuestionResponse {
  @JsonKey(name: 'id')
  final String qid;
  final QuestionType? type;
  final String question;
  final List<OptionResponse>? options;
  final String? description;
  final String? nextQuestion;

  QuestionResponse(
      {required this.qid,
      this.type,
      required this.question,
      this.options,
      this.description,
      this.nextQuestion});

  factory QuestionResponse.fromJson(Map<String, dynamic> json) =>
      _$QuestionResponseFromJson(json);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class OptionResponse {
  final String? oid;
  final String? qOption;
  final String? score;
  final bool? correct;

  OptionResponse(
      {this.oid, this.qOption, this.score, this.correct});

  factory OptionResponse.fromJson(Map<String, dynamic> json) =>
      _$OptionResponseFromJson(json);
}
