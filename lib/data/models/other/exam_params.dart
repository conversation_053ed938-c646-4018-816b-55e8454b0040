import 'package:scrumpass_exam_simulator/data/models/enum/exam_enum.dart';
import 'package:scrumpass_exam_simulator/domain/entities/question.dart';
import 'package:scrumpass_exam_simulator/domain/entities/quiz.dart';
import 'package:scrumpass_exam_simulator/domain/entities/result.dart';

class ExamParams {
  final int numQuestion;
  final List<TestDomain>? domain;
  final TestType testType;
  final String testName;
  final QuizEntity? mockTest;

  /// Dùng cho chức năng try again
  final List<QuestionEntity>? questions;

  final ResultEntity? result;

  ExamParams({
    required this.numQuestion,
    required this.testType,
    required this.testName,
    this.domain,
    this.questions,
    this.result,
    this.mockTest,
  });
}
