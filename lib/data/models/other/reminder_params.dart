import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/data/models/enum/app_enum.dart';

class ReminderParams {
  const ReminderParams({
    required this.selectedDays,
    this.reminderTime,
  });

  final List<ReminderDay> selectedDays;
  final DateTime? reminderTime;
}

class ReminderDayModel {
  ReminderDayModel({
    required this.day,
    bool? isSelected,
  }) {
    if (isSelected != null) {
      this.isSelected.value = isSelected;
    }
  }
  final ReminderDay day;
  RxBool isSelected = false.obs;
}