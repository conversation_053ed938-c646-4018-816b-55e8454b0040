// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestionResponse _$QuestionResponseFromJson(Map<String, dynamic> json) =>
    QuestionResponse(
      qid: json['id'] as String,
      type: $enumDecodeNullable(_$QuestionTypeEnumMap, json['type']),
      question: json['question'] as String,
      options: (json['options'] as List<dynamic>?)
          ?.map((e) => OptionResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
      description: json['description'] as String?,
      nextQuestion: json['next_question'] as String?,
    );

Map<String, dynamic> _$QuestionResponseToJson(QuestionResponse instance) =>
    <String, dynamic>{
      'id': instance.qid,
      'type': _$QuestionTypeEnumMap[instance.type],
      'question': instance.question,
      'options': instance.options,
      'description': instance.description,
      'next_question': instance.nextQuestion,
    };

const _$QuestionTypeEnumMap = {
  QuestionType.single: 'single',
  QuestionType.multiple: 'multi',
};

OptionResponse _$OptionResponseFromJson(Map<String, dynamic> json) =>
    OptionResponse(
      oid: json['oid'] as String?,
      qOption: json['q_option'] as String?,
      score: json['score'] as String?,
      correct: json['correct'] as bool?,
    );

Map<String, dynamic> _$OptionResponseToJson(OptionResponse instance) =>
    <String, dynamic>{
      'oid': instance.oid,
      'q_option': instance.qOption,
      'score': instance.score,
      'correct': instance.correct,
    };
