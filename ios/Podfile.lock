PODS:
  - Amplitude (8.18.0):
    - AnalyticsConnector (~> 1.0.0)
  - amplitude_flutter (0.0.1):
    - Amplitude (= 8.18.0)
    - Flutter
  - AnalyticsConnector (1.0.3)
  - connectivity_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (11.0.0):
    - Firebase/Core
  - Firebase/Core (11.0.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.0.0)
  - Firebase/CoreOnly (11.0.0):
    - FirebaseCore (= 11.0.0)
  - firebase_analytics (11.3.0):
    - Firebase/Analytics (= 11.0.0)
    - firebase_core
    - Flutter
  - firebase_core (3.4.0):
    - Firebase/CoreOnly (= 11.0.0)
    - Flutter
  - FirebaseAnalytics (11.0.0):
    - FirebaseAnalytics/AdIdSupport (= 11.0.0)
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.0.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.0.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.14.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - GoogleAppMeasurement (11.0.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.0.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.0.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - in_app_review (2.0.0):
    - Flutter
  - isar_flutter_libs (1.0.0):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - purchases_flutter (8.8.0):
    - Flutter
    - PurchasesHybridCommon (= 13.29.1)
  - PurchasesHybridCommon (13.29.1):
    - RevenueCat (= 5.22.2)
  - RevenueCat (5.22.2)
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.2):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - share_plus (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - store_redirect (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - amplitude_flutter (from `.symlinks/plugins/amplitude_flutter/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - isar_flutter_libs (from `.symlinks/plugins/isar_flutter_libs/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - purchases_flutter (from `.symlinks/plugins/purchases_flutter/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - store_redirect (from `.symlinks/plugins/store_redirect/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Amplitude
    - AnalyticsConnector
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - GoogleAppMeasurement
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - PurchasesHybridCommon
    - RevenueCat
    - Sentry

EXTERNAL SOURCES:
  amplitude_flutter:
    :path: ".symlinks/plugins/amplitude_flutter/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  isar_flutter_libs:
    :path: ".symlinks/plugins/isar_flutter_libs/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  purchases_flutter:
    :path: ".symlinks/plugins/purchases_flutter/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  store_redirect:
    :path: ".symlinks/plugins/store_redirect/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Amplitude: 184def4f87aa26f94a93a7faa334e06b1cae704d
  amplitude_flutter: 771be03f981557e28cd9fca1be71479dd150961c
  AnalyticsConnector: a53214d38ae22734c6266106c0492b37832633a9
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  Firebase: 9f574c08c2396885b5e7e100ed4293d956218af9
  firebase_analytics: 2090f32a7f5364b03cdf11aa7e904f4610309563
  firebase_core: 53cecb83c72fea329b267bb0accb06a33e9f036a
  FirebaseAnalytics: 27eb78b97880ea4a004839b9bac0b58880f5a92a
  FirebaseCore: 3cf438f431f18c12cdf2aaf64434648b63f7e383
  FirebaseCoreInternal: 6a3b668197644aa858fc4127578637c6767ba123
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  GoogleAppMeasurement: 6e49ffac7d3f2c3ded9cc663f912a13b67bbd0de
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  in_app_review: 5596fe56fab799e8edb3561c03d053363ab13457
  isar_flutter_libs: 9fc2cfb928c539e1b76c481ba5d143d556d94920
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  purchases_flutter: 437a2d875b16ea37f1847b2f88604fc13a18646b
  PurchasesHybridCommon: 1728ff834821ecfc85e987a565d51eda36213958
  RevenueCat: 234d2361e453ce1282cee7e18449d01aba45ebbf
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 27892878729f42701297c628eb90e7c6529f3684
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  store_redirect: 55fd455802ceab09803b2df6e68f19a58815266a
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: 2d003d2a4f4b67e0a26ff44e9ea1b5df6c5e26d8

COCOAPODS: 1.16.2
