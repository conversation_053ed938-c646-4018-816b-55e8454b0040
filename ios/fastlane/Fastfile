# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane
IOS_FIREBASE_APP_DISTRIBUTION_APP = "1:616957655228:ios:001e7f5974f37093d778b9"
IOS_TEAM_ID = "R5C5YX45D5"
IOS_FIREBASE_APP_DISTRIBUTION_TESTERS = "scrumpass"
IOS_FIREBASE_APP_DISTRIBUTION_CREDENTIALS_FILE_PATH = "../android/fastlane/scrumpass-exam-pass-2.json"


default_platform(:ios)

platform :ios do
  # Have an easy way to get the root of the project
  def root_path
    Dir.pwd.sub(/.*\Kfastlane/, '').sub(/.*\Kandroid/, '').sub(/.*\Kios/, '').sub(/.*\K\/\//, '')
  end

  # Have an easy way to run flutter tasks on the root of the project
  lane :sh_on_root do |options|
    command = options[:command]
    sh("cd #{root_path} && #{command}")
  end

  lane :increment_version do
    latest_release = firebase_app_distribution_get_latest_release(
      app: IOS_FIREBASE_APP_DISTRIBUTION_APP
    )
    if latest_release.nil?
      increment_build_number({ build_number: 1})
    else
      increment_build_number({ build_number: latest_release[:buildVersion].to_i + 1 })
    end
  end

  # Updates XCode project settings to use a different code signing based on method
  private_lane :archive do |options|
    method = options[:method]
    # env = ENV['IOS_SCHEME']
    # scheme = env == "dev" ? "dev" : "prod"
    # configuration = env == "dev" ? "Release-dev" : "Release-prod"

    update_code_signing_settings(
      # build_configurations: "Release-dev",
      use_automatic_signing: true
    )

    increment_version

    update_project_team(
      teamid: IOS_TEAM_ID
    )

    build_app(
      output_directory: "#{root_path}/build/ios",
      build_path: "#{root_path}/build/ios",
      archive_path: "#{root_path}/build/ios",
      export_method: method,
      # scheme: env,
      # configuration: "Release-dev",
      xcargs: "-allowProvisioningUpdates"
    )
  end

  lane :build do |options|
    # env = ENV['IOS_SCHEME']

    # params = env == "dev" ? '--flavor dev -t lib/main_dev.dart ' : '--flavor prod -t lib/main_prod.dart '
    # desc("Cleaning...")
    # sh_on_root(command: "flutter clean && rm -Rf ios/Pods")
    desc("Building...")
    sh_on_root(command: "flutter build ipa")
  end

  private_lane :prompt_for_marketing_version do |options|
    marketing_version = get_version_number
    new_version = UI.input("Nhập số phiên bản? <enter để giữ phiên bản hiện tại #{marketing_version}>")
    unless new_version.strip == ""
      increment_version_number(version_number: new_version)
      UI.message("Phiên bản được đặt là #{new_version}")
      marketing_version = new_version
    end
  end

  lane :deploy_firebase do |options|
    # prompt_for_marketing_version
    build(sign_enabled: true)
    archive(method: "ad-hoc")

    firebase_app_distribution(
      app: IOS_FIREBASE_APP_DISTRIBUTION_APP,
      groups: IOS_FIREBASE_APP_DISTRIBUTION_TESTERS,
      release_notes: options[:release_note],
      service_credentials_file: IOS_FIREBASE_APP_DISTRIBUTION_CREDENTIALS_FILE_PATH,
      debug: true
    )
  end

end
