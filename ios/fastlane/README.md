fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios sh_on_root

```sh
[bundle exec] fastlane ios sh_on_root
```



### ios increment_version

```sh
[bundle exec] fastlane ios increment_version
```



### ios build

```sh
[bundle exec] fastlane ios build
```



### ios deploy_firebase

```sh
[bundle exec] fastlane ios deploy_firebase
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
