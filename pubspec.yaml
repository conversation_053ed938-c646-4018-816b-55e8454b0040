name: scrumpass_exam_simulator
description: ScrumPass Exam Simulator

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.5.0+1

environment:
  sdk: ">=3.1.3 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  get: ^4.1.4
  tuple: ^2.0.0
  cached_network_image: ^3.3.1
  json_annotation: ^4.9.0
  jiffy: ^6.2.1
  visibility_detector: ^0.4.0+2
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  dio: ^5.4.3+1
  flutter_svg: ^2.0.7
  get_storage: ^2.1.1
  gg_sheet_localiization:
    git:
      url: https://gitlab.com/chithanh1998/gg_sheet_localiization.git
      ref: main
  connectivity_plus: ^6.0.3
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1
  path_provider: ^2.1.3
  auto_mappr_annotation: ^2.2.0
  google_fonts: ^6.2.1
  flutter_animation_progress_bar: ^2.3.1
  syncfusion_flutter_sliders:
    git:
      url: https://gitlab.com/chithanh1998/syncfusion_slider.git
      ref: main
  syncfusion_flutter_core: ^26.1.39
  animations: ^2.0.11
  freezed_annotation: ^2.4.1
  skeletonizer: ^1.3.0
  store_redirect: ^2.0.2
  scroll_datetime_picker: ^0.1.2
  intl: ^0.19.0
  firebase_core: ^3.1.1
  firebase_analytics: ^11.1.0
  package_info_plus: ^8.0.0
  idle_detector_wrapper: ^1.0.0
  url_launcher: ^6.3.0
  expandable: ^5.0.1
  flutter_local_notifications: ^17.2.1+2
  in_app_review: ^2.0.9
  share_plus: ^11.0.0
  flutter_html: ^3.0.0-beta.2
  equatable: ^2.0.5
  dotted_border: ^2.1.0
  purchases_flutter: ^8.5.1
  loader_overlay: ^4.0.1
  flutter_spinkit: ^5.2.1
  autoscale_tabbarview: ^1.0.2
  fl_chart: ^0.69.0
  blur: ^4.0.0
  sentry_flutter: ^8.9.0
  sentry_dio: ^8.9.0
  onboarding_animation: ^0.0.3
  smooth_page_indicator: ^1.2.0+3
  carousel_slider: ^5.0.0
  scroll_wheel_date_picker: ^0.0.2+1
  amplitude_flutter: ^3.13.0
  overlay_tooltip: ^0.2.3
  flutter_screenutil: ^5.9.3
  photo_view: ^0.14.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.1.7
  source_gen: ^1.2.6
  json_serializable: ^6.8.0
  isar_generator: ^3.1.0+1
  auto_mappr: ^2.5.0
  flutter_gen_runner: 5.5.0+1
  freezed: ^2.5.2
  icons_launcher: ^3.0.0

flutter_gen:
  output: lib/generated/
  integrations:
    flutter_svg: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

dependency_overrides:
  flutter_gen_core: 5.5.0+1
