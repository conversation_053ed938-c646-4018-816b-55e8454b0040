BUNDLE_ID ?= com.example.default
APP_NAME ?= Default App Name

lang:
	@echo "🧹 Cleaning generated file..."
	@rm -f assets/generated/gp_locale_keys.m.g.part
	@echo "🔧 Running build_runner..."
	@dart run build_runner build --delete-conflicting-outputs
	@echo "🌍 Generating locales..."
	@getx generate locales assets/locales

doctor:
	fvm flutter doctor

init:
	fvm flutter pub get

pod_init:
	cd ios && pod install

clean:
	fvm flutter clean && cd ios && rm -rf Pods && rm -rf Podfile.lock && cd .. && fvm flutter pub get && cd ios && pod install

run_build_runner:
	fvm flutter pub run build_runner clean
	fvm flutter pub run build_runner build --delete-conflicting-outputs

icon:
	dart run icons_launcher:create

firebase:
	dart pub global activate flutterfire_cli && flutterfire configure

activate_fvm:
	dart pub global activate fvm

rename:
	flutter pub global activate rename

	@echo "📦 Setting Bundle ID to $(BUNDLE_ID)..."
	rename setBundleId --targets ios,android --value "$(BUNDLE_ID)"

	@echo "📱 Setting App Name to $(APP_NAME)..."
	rename setAppName --targets ios,android --value "$(APP_NAME)"

	@echo "✅ Done renaming to $(APP_NAME) with Bundle ID $(BUNDLE_ID)"

merge_main:
	for branch in $$(git branch | grep -v "main"); do \
		echo "Đang merge main vào $$branch"; \
		git checkout "$$branch"; \
		git merge main --no-edit; \
		git push origin "$$branch"; \
	done