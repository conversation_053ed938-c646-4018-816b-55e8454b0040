stages:
  - build

# Build iOS với Fastlane
build_ios:
  stage: build
  tags:
    - mac-mini   # tag runner c<PERSON><PERSON> bạn
  when: manual   # job thủ công
  only:
    - branches   # chỉ chạy trên branch (không chạy cho tag)
  script:
    - echo ">>> <PERSON><PERSON><PERSON> đầu build iOS với Fastlane"
    - flutter clean
    - flutter pub get
    - cd ios
    - rm Podfile.lock
    - pod install
    - fastlane deploy_firebase release_note:""

# Build Android với Fastlane
build_android:
  stage: build
  tags:
    - mac-mini
  when: manual
  only:
    - branches
  script:
    - echo ">>> B<PERSON><PERSON> đầu build Android với Fastlane"
    - flutter clean
    - flutter pub get
    - cd android
    - fastlane deploy_firebase release_note:""