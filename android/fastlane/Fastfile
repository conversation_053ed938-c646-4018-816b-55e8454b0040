# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

ANDROID_FIREBASE_APP_DISTRIBUTION_APP = "1:616957655228:android:6bc06b9f2f03e20bd778b9"
ANDROID_FIREBASE_APP_DISTRIBUTION_TESTERS = "scrumpass"
ANDROID_FIREBASE_APP_DISTRIBUTION_CREDENTIALS_FILE_PATH = "fastlane/scrumpass-exam-pass-2.json"

default_platform(:android)

platform :android do
  # Have an easy way to get the root of the project
  def root_path
    Dir.pwd.sub(/.*\Kfastlane/, '').sub(/.*\Kandroid/, '').sub(/.*\Kios/, '').sub(/.*\K\/\//, '')
  end

  # Have an easy way to run flutter tasks on the root of the project
  lane :sh_on_root do |options|
    command = options[:command]
    sh("cd #{root_path} && #{command}")
  end

  lane :increment_version do
    latest_release = firebase_app_distribution_get_latest_release(
      app: ANDROID_FIREBASE_APP_DISTRIBUTION_APP
    )
    increment_build_number({ build_number: latest_release[:buildVersion].to_i + 1 })
  end

  private_lane :build_apk do
    # env = ENV['ANDROID_SCHEME']

    # params = env == "dev" ? '--flavor dev -t lib/main_dev.dart ' : '--flavor prod -t lib/main_prod.dart '

    desc "Building Android APK"
    sh_on_root(command: "flutter build apk")
  end

  lane :deploy_firebase do |options|
    build_apk

    # increment_version

    firebase_app_distribution(
      app: ANDROID_FIREBASE_APP_DISTRIBUTION_APP,
      groups: ANDROID_FIREBASE_APP_DISTRIBUTION_TESTERS,
      release_notes: options[:release_note],
      service_credentials_file: ANDROID_FIREBASE_APP_DISTRIBUTION_CREDENTIALS_FILE_PATH,
      apk_path: "../build/app/outputs/apk/release/app-release.apk",
      # firebase_cli_token: FIREBASE_CLI_TOKEN,
      debug: true
    )
  end
end
